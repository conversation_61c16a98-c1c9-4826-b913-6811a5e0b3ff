<template>
  <div id="cesiumContainer" ref="SceneRef"></div>
</template>
<script setup lang="ts">
import World from '@/packages/components/Decorates/Three/ThreeEarth01/code/world/Word'
import { onMounted, PropType, ref, toRefs, watch } from 'vue'
import axios from 'axios'
import config, { includesThreeSceneStructure } from './config'
const Cesium:any = window.Cesium

let Viewer:any=null
const token = '57fa3c6af134f69ebac3b2bff6d51112'

const props = defineProps({
  themeSetting: {
    type: Object,
    required: true
  },
  themeColor: {
    type: Object,
    required: true
  },
  chartConfig: {
    type: Object as PropType<config>,
    required: true
  }
})



const SceneRef =  ref<HTMLElement>()
let threeClassInstance: World

function NewCesium() {
  return new Cesium.Viewer('cesiumContainer', {
    scene3DOnly: true,
    animation: false, // 是否显示左下角的仪表盘
    fullscreenButton: false, // 是否显示全屏按钮
    baseLayerPicker: false, // 是否显示图层选择器按钮，右上角那个地图图标
    vrButton: false, // 是否显示VR按钮
    geocoder: false, // 是否显示搜索按钮
    homeButton: false, // 是否显示主页按钮
    infoBox: false, // 是否显示提示信息
    sceneModePicker: false, // 是否显示右上角的模式切换按钮
    selectionIndicator: false, // 是否显示选取指示器组件
    timeline: false, // 是否显示下边的时间轴
    navigationHelpButton: false, // 是否显示右上角的帮助按钮
    navigationInstructionsInitiallyVisible: false, // 是不显示导航
    CreditsDisplay: false,//展示数据版权属性
  })
}

function get3DTileset(params: any,Viewer: any) {
    let tileset = Viewer.scene.primitives.add(
      new Cesium.Cesium3DTileset({
        url: params.url, //切片url
        maximumScreenSpaceError: 16, //用于驱动细节细化级别的最大屏幕空间误差。 默认16
        maximumMemoryUsage: 2048 * 2, //瓦片集可以使用的最大内存量（以 MB 为单位）。 默认512
        cullWithChildrenBounds: true, //优化选项。是否使用子边界体积的并集来剔除瓦片。默认true
        cullRequestsWhileMoving: true, //优化选项。不要请求由于相机移动而在返回时可能未使用的图块。这种优化只适用于静止的瓦片集。默认true
        cullRequestsWhileMovingMultiplier: 100.0, //优化选项。移动时用于剔除请求的乘数。较大的是更积极的剔除，较小的较不积极的剔除。 默认值60
        preloadWhenHidden: true, //tileset.show时 预加载瓷砖false。加载图块，就好像图块集可见但不渲染它们。 默认false
        preloadFlightDestinations: true, //优化选项。在相机飞行时在相机的飞行目的地预加载图块。。 默认true
        preferLeaves: true, //优化选项 最好先装载叶子。 默认false
        dynamicScreenSpaceError: true, //优化选项。减少距离相机较远的图块的屏幕空间错误。 默认false
        dynamicScreenSpaceErrorDensity: 0.00278, //用于调整动态屏幕空间误差的密度，类似于雾密度。
        skipLevelOfDetail: true,
        baseScreenSpaceError: 512,
        skipScreenSpaceErrorFactor: 1,
        skipLevels: 0,
        immediatelyLoadDesiredLevelOfDetail: true,
        loadSiblings: false,
        show: true,
      })
    )
    Viewer.flyTo(tileset)
  }


const init=()=>{
  const dom: HTMLElement | undefined = SceneRef.value
  if(dom){
    threeClassInstance
  }
}

// 查询地址
function getSite(place: any){
  console.log(place)
  axios.get(`http://api.tianditu.gov.cn/geocoder?ds={"keyWord":"${place}"}&tk=${token}`)
  .then(data=>{
    if(data.status===200 && data.data.location != undefined){
      console.log(data.data.location);
      let result=data.data.location
      Viewer.camera.flyTo({
        destination: Cesium.Cartesian3.fromDegrees(result.lon, result.lat, 80000),
        duration: 0
      })
    }
  }).catch(error=>{
    console.log(error)
  })
}

// 监听地图定位
watch(()=>props.chartConfig.option.mapRegion.mapServiceAddressURL,
  async newData => {
    try{
      console.log(newData);
      getSite(newData)
    } catch(error){
      console.log(error);
    }
  })

onMounted(()=>{
  try {
    if(navigator.userAgent.indexOf('Chrome') < -1 || navigator.userAgent.indexOf('Edg') > -1){
      window['$message'].error('此组件仅在【谷歌】浏览器上能正常展示！')
      return
    }
    if(SceneRef.value){
      Viewer=NewCesium()
      get3DTileset({url:'http://localhost:9003/model/WZhdDd3Q/tileset.json'},Viewer)
    }
    init()
  } catch (error){
    console.log(error);
  }
})
</script>
<style scoped>
#cesiumContainer {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
}
#cesiumContainer /deep/ .cesium-credit-textContainer,
#cesiumContainer /deep/ .cesium-credit-expand-link,
#cesiumContainer /deep/ .cesium-credit-logoContainer,
#cesiumContainer /deep/ .cesium-viewer-toolbar {
  display: none !important;
}
</style>