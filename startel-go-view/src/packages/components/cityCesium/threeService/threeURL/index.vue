<template>
  <iframe v-if="Object.keys(props.chartConfig.option.mapRegion.mapServiceAddressURL).length>0" :src="props.chartConfig.option.mapRegion.mapServiceAddressURL" width="100%" height="100%" frameborder="0"></iframe>
</template>
<script setup lang="ts">
import { PropType, ref, watch, onMounted, onUpdated  } from 'vue'
// import config, { includes } from './config'

const props = defineProps({
  themeSetting: {
    type: Object,
    required: true
  },
  themeColor: {
    type: Object,
    required: true
  },
  chartConfig: {
    type: Object as PropType<config>,
    required: true
  }
})
const url='http://*************:59083/CD_GIS/#/'
const cesiumContainer=ref(null)

watch(()=> props.chartConfig.option.mapRegion.mapServiceAddressURL, async newData =>{
  if(newData){
    console.log(newData);
  }
})
</script>
<style scoped>
#cesiumContainer {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
}
#cesiumContainer /deep/ .cesium-credit-textContainer,
#cesiumContainer /deep/ .cesium-credit-expand-link,
#cesiumContainer /deep/ .cesium-credit-logoContainer,
#cesiumContainer /deep/ .cesium-viewer-toolbar {
  display: none !important;
}
</style>