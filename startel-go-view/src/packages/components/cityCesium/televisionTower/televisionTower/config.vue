<template>
  <CollapseItem name="地图" :expanded="true">
    <SettingItemBox name="地图区域">
      <setting-item name="默认中国">
       <!-- <n-select v-model:value="mapRegion.mapServiceAddressURL" :options="mapRegion.options" /> -->
       <n-input v-model:value="mapRegion.mapServiceAddressURL" type="text" placeholder="请输入位置" />
      </setting-item>
    </SettingItemBox>
  </CollapseItem>
  <CollapseItem name="影像" :expanded="true">
    <SettingItemBox name="服务地址">
      <setting-item name="URL">
       <n-input v-model:value="mapRegion.mapServiceAddressURL" type="text" placeholder="三维服务URL" />
      </setting-item>
    </SettingItemBox>
  </CollapseItem>
</template>
<script setup lang="ts">
import { watch, ref, PropType, computed } from 'vue'
import { CollapseItem, SettingItemBox, SettingItem } from '@/components/Pages/ChartItemSetting'
import { optionTelevisionTower } from './config'

console.log(optionTelevisionTower);


const props = defineProps({
  optionData: {
    type: Object as PropType<typeof optionTelevisionTower>,
    required: true
  },
})
console.log(props);
console.log( props.optionData.mapRegion.mapServiceAddressURL);
console.log( props.optionData.mapRegion.options);

 const mapRegion = computed(() => {
  return props.optionData.mapRegion
})
</script>