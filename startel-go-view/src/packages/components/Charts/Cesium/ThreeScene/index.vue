<template>
  <div id="cesiumContainer" ref="SceneRef"></div>
</template>
<script setup lang="ts">
import World from '@/packages/components/Decorates/Three/ThreeEarth01/code/world/Word'
import { onMounted, PropType, ref, toRefs, watch } from 'vue'
import axios from 'axios'
import config, { includesThreeScene } from './config'
const Cesium = window.Cesium
let Viewer=null
const token = '57fa3c6af134f69ebac3b2bff6d51112'

const props = defineProps({
  themeSetting: {
    type: Object,
    required: true
  },
  themeColor: {
    type: Object,
    required: true
  },
  chartConfig: {
    type: Object as PropType<config>,
    required: true
  }
})



const SceneRef =  ref<HTMLElement>()
let threeClassInstance: World

function NewCesium() {
  return new Cesium.Viewer('cesiumContainer', {
    scene3DOnly: true,
    animation: false, // 是否显示左下角的仪表盘
    fullscreenButton: false, // 是否显示全屏按钮
    baseLayerPicker: false, // 是否显示图层选择器按钮，右上角那个地图图标
    vrButton: false, // 是否显示VR按钮
    geocoder: false, // 是否显示搜索按钮
    homeButton: false, // 是否显示主页按钮
    infoBox: false, // 是否显示提示信息
    sceneModePicker: false, // 是否显示右上角的模式切换按钮
    selectionIndicator: false, // 是否显示选取指示器组件
    timeline: false, // 是否显示下边的时间轴
    navigationHelpButton: false, // 是否显示右上角的帮助按钮
    navigationInstructionsInitiallyVisible: false, // 是不显示导航
    CreditsDisplay: false,//展示数据版权属性
  })
}


const init=()=>{
  const dom: HTMLElement | undefined = SceneRef.value
  if(dom){
    threeClassInstance
  }
}

// 查询地址
function getSite(place){
  console.log(place)
  axios.get(`http://api.tianditu.gov.cn/geocoder?ds={"keyWord":"${place}"}&tk=${token}`)
  .then(data=>{
    if(data.status===200 && data.data.location != undefined){
      console.log(data.data.location);
      let result=data.data.location
      Viewer.camera.flyTo({
        destination: Cesium.Cartesian3.fromDegrees(result.lon, result.lat, 80000),
        duration: 0
      })
    }
  }).catch(error=>{
    console.log(error)
  })
}

// 监听地图定位
watch(()=>props.chartConfig.option.mapRegion.mapServiceAddressURL,
  async newData => {
    try{
      console.log(newData);
      getSite(newData)
    } catch(error){
      console.log(error);
    }
  })

onMounted(()=>{
  try {
    if(navigator.userAgent.indexOf('Chrome') < -1 || navigator.userAgent.indexOf('Edg') > -1){
      window['$message'].error('此组件仅在【谷歌】浏览器上能正常展示！')
      return
    }
    if(SceneRef.value){
      Viewer=NewCesium()
    }
    init()
  } catch (error){
    console.log(error);
  }
})
</script>
<style scoped>
#cesiumContainer {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
}
#cesiumContainer /deep/ .cesium-credit-textContainer,
#cesiumContainer /deep/ .cesium-credit-expand-link,
#cesiumContainer /deep/ .cesium-credit-logoContainer,
#cesiumContainer /deep/ .cesium-viewer-toolbar {
  display: none !important;
}
</style>