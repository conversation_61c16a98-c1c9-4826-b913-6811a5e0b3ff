// 展示图片
import image from '@/assets/images/chart/charts/threeMap.png'
// 公共类型声明
import { ConfigType, PackagesCategoryEnum, ChartFrameEnum } from '@/packages/index.d'
// 当前[信息模块]分类声明
import { ChatCategoryEnum,ChatCategoryEnumName } from '../../index.d'

export const ThreeMapConfig: ConfigType={
  // 唯一key，注意！！！文件夹名称需要修改成与当前组件一致！！！
  key: 'ThreeMap',
  // 图表组件渲染 Components 格式: V + key
  chartKey: 'VThreeMap',
  // 配置组件渲染 Components 格式: VC + key
  conKey: 'VCThreeMap',
  // 名称
  title: '三维服务',
  // 子分类目录
  category: ChatCategoryEnum.CESIUM,
  // 子分类目录
  categoryName: ChatCategoryEnumName.CESIUM,
  // 包分类
  package: PackagesCategoryEnum.CHARTS,
  // 图表类型
  chartFrame: ChartFrameEnum.COMMON,
  // 图片
  image: image

}