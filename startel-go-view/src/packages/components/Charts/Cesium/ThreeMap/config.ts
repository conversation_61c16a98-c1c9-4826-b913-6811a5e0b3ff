import { echartOptionProfixHandle, PublicConfigClass } from '@/packages/public'
import { ThreeMapConfig } from './index'
import { chartInitConfig } from '@/settings/designSetting'
import { CreateComponentType } from '@/packages/index.d'
console.log(ThreeMapConfig);

export const includes = []
export const option = {
  // dataset: dataJson,
  mapRegion:{
    mapServiceURL:'',
    // mapServiceAddressURL:''
    mapServiceAddressURL:'http://*************:59083/CD_GIS/#/'
  },
  tooltip: {
    show: true,
    trigger: 'item'
  },
  geo: {
    show: false,
    type: 'map',
    roam: false,
    map: 'china'
  },
  series: [
    {
      type: 'effectScatter',
      coordinateSystem: 'geo',
      symbolSize: 6,
      zlevel: 1,
      label: {
        show: false
      },
      itemStyle: {
        shadowBlur: 10,
        color: '#00ECC8'
      },
      data: []
    },
    {
      name: '地图',
      type: 'map',
      map: 'china',
      zoom: 1, //缩放
      itemStyle: {
        // 背景色
        areaColor: 'rgba(117, 236, 170, 0.3)',
        emphasis: {
          areaColor: 'rgba(117, 236, 170, .8)',
          borderWidth: 1,
          shadowBlur: 10,
          shadowColor: '#75ecaa'
        },
        color: '#ffffff',
        borderColor: '#75ecaa',
        borderWidth: 1,
        showHainanIsLands: true // 是否显示南海群岛
      },
      label: {
        show: true,
        color: '#ffffff'
      },
      data: []
    }
  ]
}
console.log(chartInitConfig);

export default class Config extends PublicConfigClass implements CreateComponentType {
  public key: string = ThreeMapConfig.key
  public attr = { ...chartInitConfig, w: 1920, h: 1080, zIndex: -1 }
  public chartConfig = ThreeMapConfig
  public option = echartOptionProfixHandle(option, includes)
}