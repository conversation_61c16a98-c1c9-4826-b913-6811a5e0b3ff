<template>
  <iframe v-if="Object.keys(props.chartConfig.option.mapRegion.mapServiceAddressURL).length>0" :src="props.chartConfig.option.mapRegion.mapServiceAddressURL" width="100%" height="100%" frameborder="0"></iframe>
  <!-- <div v-else id="cesiumContainer" ref="cesiumContainer"></div> -->
  <!-- <div id="cesiumContainer" ref="cesiumContainer"></div> -->
</template>
<script setup lang="ts">
import { PropType, ref, watch, onMounted, onUpdated  } from 'vue'
import config, { includes } from './config'
const Cesium = window.Cesium
console.log(Cesium,window)

function NewCesium() {
  return new Cesium.Viewer('cesiumContainer', {
    scene3DOnly: true,
    animation: false, // 是否显示左下角的仪表盘
    fullscreenButton: false, // 是否显示全屏按钮
    baseLayerPicker: false, // 是否显示图层选择器按钮，右上角那个地图图标
    vrButton: false, // 是否显示VR按钮
    geocoder: false, // 是否显示搜索按钮
    homeButton: false, // 是否显示主页按钮
    infoBox: false, // 是否显示提示信息
    sceneModePicker: false, // 是否显示右上角的模式切换按钮
    selectionIndicator: false, // 是否显示选取指示器组件
    timeline: false, // 是否显示下边的时间轴
    navigationHelpButton: false, // 是否显示右上角的帮助按钮
    navigationInstructionsInitiallyVisible: false, // 是不显示导航
    CreditsDisplay: false,//展示数据版权属性
  })
}
const props = defineProps({
  themeSetting: {
    type: Object,
    required: true
  },
  themeColor: {
    type: Object,
    required: true
  },
  chartConfig: {
    type: Object as PropType<config>,
    required: true
  }
})
const url='http://*************:59083/CD_GIS/#/'
const cesiumContainer=ref(null)

watch(()=> props.chartConfig.option.mapRegion.mapServiceAddressURL, async newData =>{
  if(newData){
    console.log(newData);
  }
})
onMounted(() => {
  if(cesiumContainer.value){
    NewCesium()
  }
  // NewCesium()
})
onUpdated(() => {
  console.log(cesiumContainer);
  if(cesiumContainer.value){
    NewCesium()
  }
})
</script>
<style scoped>
#cesiumContainer {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
}
#cesiumContainer /deep/ .cesium-credit-textContainer,
#cesiumContainer /deep/ .cesium-credit-expand-link,
#cesiumContainer /deep/ .cesium-credit-logoContainer,
#cesiumContainer /deep/ .cesium-viewer-toolbar {
  display: none !important;
}
</style>