<template>
  <CollapseItem name="三维场景服务地址" :expanded="true">
    <SettingItemBox name="服务地址">
      <setting-item name="URL">
       <n-input v-model:value="mapRegion.mapServiceAddressURL" type="text" placeholder="三维服务URL" />
      </setting-item>
    </SettingItemBox>
  </CollapseItem>
</template>
<script setup lang="ts">
import { watch, ref, PropType, computed } from 'vue'
import { CollapseItem, SettingItemBox, SettingItem } from '@/components/Pages/ChartItemSetting'
import { option } from './config'

// let mapServiceAddressURL = ref('http://*************:59083/CD_GIS/#/')
console.log(option);


const props = defineProps({
  optionData: {
    type: Object as PropType<typeof option>,
    required: true
  },
})
console.log(props);
console.log( props.optionData.mapRegion.mapServiceAddressURL);

 const mapRegion = computed(() => {
  return props.optionData.mapRegion
})
</script>