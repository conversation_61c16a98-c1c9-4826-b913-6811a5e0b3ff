// 设置项布局
import SettingItem from './SettingItem.vue'
import SettingItemBox from './SettingItemBox.vue'
import CollapseItem from './CollapseItem.vue'

// 全局配置属性
import GlobalSetting from './GlobalSetting.vue'
// 全局配置属性-位置
import GlobalSettingPosition from './GlobalSettingPosition.vue'

// 名称
import NameSetting from './NameSetting.vue'
// 方向
import PositionSetting from './PositionSetting.vue'
// 尺寸
import SizeSetting from './SizeSetting.vue'
// 样式
import StylesSetting from './StylesSetting.vue'

export { CollapseItem, SettingItemBox, SettingItem, GlobalSetting, GlobalSettingPosition, NameSetting, PositionSetting, SizeSetting, StylesSetting }
