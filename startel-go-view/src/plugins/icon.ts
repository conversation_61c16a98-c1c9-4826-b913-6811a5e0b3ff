import {
  Close as CloseIcon,
  Remove as RemoveIcon,
  Resize as ResizeI<PERSON>,
  EllipsisHorizontalSharp as <PERSON>lipsisH<PERSON>zontalCircleSharpIcon,
  CopyOutline as CopyIcon,
  Trash as TrashIcon,
  Pencil as PencilIcon,
  HammerOutline as HammerIcon,
  DesktopOutline as DesktopOutlineIcon,
  Download as DownloadIcon,
  DownloadOutline as DownloadOutlineIcon,
  Share as ShareIcon,
  Send as SendIcon,
  InformationCircleOutline as InformationCircleIcon,
  Grid as GridIcon,
  TvOutline as TvOutlineIcon,
  DocumentText as DocumentTextIcon,
  Language as LanguageIcon,
  Moon as MoonIcon,
  Sunny as SunnyIcon,
  Person as PersonIcon,
  PersonOutline as PersonOutlineIcon,
  LogOutOutline as LogOutOutlineIcon,
  ChatboxEllipses as ChatboxEllipsesIcon,
  LockOpenOutline as LockOpenOutlineIcon,
  LockClosedOutline as LockClosedOutlineIcon,
  HelpCircleOutline as HelpOutlineIcon,
  CodeSlash as <PERSON><PERSON>lashIcon,
  Rocket as RocketIcon,
  Duplicate as DuplicateIcon,
  DuplicateOutline as DuplicateOutlineIcon,
  Fish as FishIcon,
  <PERSON><PERSON>hart as Bar<PERSON><PERSON>Icon,
  Layers as LayersIcon,
  Prism as PrismIcon,
  Construct as ConstructIcon,
  ChevronBackOutline as ChevronBackOutlineIcon,
  Flash as FlashIcon,
  SettingsSharp as SettingsSharpIcon,
  Home as HomeIcon,
  Card as CardIcon,
  ChevronUp as ChevronUpIcon,
  ChevronDown as ChevronDownIcon,
  ClipboardOutline as ClipboardOutlineIcon,
  BrowsersOutline as BrowsersOutlineIcon,
  Cut as CutIcon,
  Square as SquareIcon,
  ColorPalette as ColorPaletteIcon,
  Leaf as LeafIcon,
  ColorWand as ColorWandIcon,
  ArrowBack as ArrowBackIcon,
  ArrowForward as ArrowForwardIcon,
  Planet as PawIcon,
  Search as SearchIcon,
  Reload as ReloadIcon,
  ChevronUpOutline as ChevronUpOutlineIcon,
  ChevronDownOutline as ChevronDownOutlineIcon,
  Pulse as PulseIcon,
  Folder as FolderIcon,
  FolderOpen as FolderOpenIcon,
  Image as ImageIcon,
  Images as ImagesIcon,
  List as ListIcon,
  EyeOutline as EyeOutlineIcon,
  EyeOffOutline as EyeOffOutlineIcon
} from '@vicons/ionicons5'

import {
  DnsServices as DnsServicesIcon,
  TableSplit as TableSplitIcon,
  Roadmap as RoadmapIcon,
  SpellCheck as SpellCheckIcon,
  GraphicalDataFlow as GraphicalDataFlowIcon,
  Store as StoreIcon,
  Devices as DevicesIcon,
  ObjectStorage as ObjectStorageIcon,
  DicomOverlay as DicomOverlayIcon,
  UpToTop as UpToTopIcon,
  DownToBottom as DownToBottomIcon,
  StackedMove as StackedMoveIcon,
  PaintBrush as PaintBrushIcon,
  ComposerEdit as ZAxisIcon,
  AlignHorizontalLeft as AlignHorizontalLeftIcon,
  AlignVerticalCenter as AlignVerticalCenterIcon,
  AlignVerticalTop as AlignVerticalTopIcon,
  AlignHorizontalCenter as AlignHorizontalCenterIcon,
  AlignHorizontalRight as AlignHorizontalRightIcon,
  AlignVerticalBottom as AlignVerticalBottomIcon,
  DocumentAdd as DocumentAddIcon,
  DocumentDownload as DocumentDownloadIcon,
  Scale as ScaleIcon,
  FitToScreen as FitToScreenIcon,
  FitToHeight as FitToHeightIcon,
  FitToWidth as FitToWidthIcon,
  Save as SaveIcon,
  Carbon3DCursor as Carbon3DCursorIcon,
  Carbon3DSoftware as Carbon3DSoftwareIcon,
  Filter as FilterIcon,
  FilterEdit as FilterEditIcon
} from '@vicons/carbon'

const ionicons5 = {
  // 帮助（问号）
  HelpOutlineIcon,
  // 添加
  DuplicateIcon,
  // 添加 Outline
  DuplicateOutlineIcon,
  // 代码
  CodeSlashIcon,
  // 事件(火箭)
  RocketIcon,
  // 退出
  LogOutOutlineIcon,
  // 锁
  LockOpenOutlineIcon,
  LockClosedOutlineIcon,
  // 人
  PersonIcon,
  // 人2
  PersonOutlineIcon,
  // 更多（省略号）
  ChatboxEllipsesIcon,
  // 月亮
  MoonIcon,
  // 太阳
  SunnyIcon,
  // 关闭
  CloseIcon,
  // 移除（最小化）
  RemoveIcon,
  // 调整（最大化）
  ResizeIcon,
  // 省略号
  EllipsisHorizontalCircleSharpIcon,
  // 复制
  CopyIcon,
  // 删除（垃圾桶）
  TrashIcon,
  // 编辑1（笔）
  PencilIcon,
  // 编辑2（锤子）
  HammerIcon,
  // 电脑
  DesktopOutlineIcon,
  // 下载
  DownloadIcon,
  DownloadOutlineIcon,
  // 导出
  ShareIcon,
  // 导出
  SendIcon,
  // 信息
  InformationCircleIcon,
  // 项目
  GridIcon,
  // 电脑1
  TvOutlineIcon,
  // 预览，浏览器
  BrowsersOutlineIcon,
  // 文档
  DocumentTextIcon,
  // 语言
  LanguageIcon,
  // 新项目（鱼）
  FishIcon,
  // 图表
  BarChartIcon,
  // 图层
  LayersIcon,
  // 组件详情设置（三棱镜）
  PrismIcon,
  // 正方体
  ConstructIcon,
  // 折叠/回退
  ChevronBackOutlineIcon,
  // 后端数据（闪电）
  FlashIcon,
  // 设置（齿轮）
  SettingsSharpIcon,
  // 回退
  HomeIcon,
  // 控件(卡片)
  CardIcon,
  // 上移
  ChevronUpIcon,
  // 下移
  ChevronDownIcon,
  // 剪贴板
  ClipboardOutlineIcon,
  // 剪贴
  CutIcon,
  // 正方形
  SquareIcon,
  // 色彩选择
  ColorPaletteIcon,
  ZAxisIcon,
  // 气球
  LeafIcon,
  // 颜色
  ColorWandIcon,
  // 撤回
  ArrowBackIcon,
  // 前进
  ArrowForwardIcon,
  // 狗爪
  PawIcon,
  // 搜索（放大镜）
  SearchIcon,
  // 加载
  ReloadIcon,
  // 过滤器
  FilterIcon,
  // 向上
  ChevronUpOutlineIcon,
  // 向下
  ChevronDownOutlineIcon,
  // 脉搏
  PulseIcon,
  // 文件夹
  FolderIcon,
  // 文件夹打开
  FolderOpenIcon,
  // 图片
  ImageIcon,
  // 多个图片
  ImagesIcon,
  // 列表
  ListIcon,
  // 眼睛
  EyeOutlineIcon,
  EyeOffOutlineIcon
}

const carbon = {
  // 三维
  DnsServicesIcon,
  // 图表
  RoadmapIcon,
  // 信息
  SpellCheckIcon,
  // 表格
  TableSplitIcon,
  // 装饰
  GraphicalDataFlowIcon,
  // 项目
  DevicesIcon,
  // 模板市场
  StoreIcon,
  // 我的模板
  ObjectStorageIcon,
  // 键盘
  DicomOverlayIcon,
  // 置顶
  UpToTopIcon,
  // 置底
  DownToBottomIcon,
  // 移动
  StackedMoveIcon,
  // 清空剪切板（刷子）
  PaintBrushIcon,
  // 坐标轴
  ZAxisIcon,
  AlignHorizontalLeftIcon,
  AlignVerticalCenterIcon,
  AlignVerticalTopIcon,
  AlignHorizontalCenterIcon,
  AlignHorizontalRightIcon,
  AlignVerticalBottomIcon,
  // 添加文件
  DocumentAddIcon,
  // 下载文件
  DocumentDownloadIcon,
  // 预览展示方式
  ScaleIcon,
  FitToScreenIcon,
  FitToHeightIcon,
  FitToWidthIcon,
  // 保存
  SaveIcon,
  // 成组
  Carbon3DCursorIcon,
  // 解组
  Carbon3DSoftwareIcon,
  // 过滤器
  FilterIcon,
  FilterEditIcon
}

// https://www.xicons.org/#/ 还有很多

export const icon = {
  ionicons5,
  carbon
}
