/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.95
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["./AxisAlignedBoundingBox-b1c095aa","./Transforms-d3d3b2a9","./Matrix2-73789715","./defaultValue-97284df2","./TerrainEncoding-080f16eb","./ComponentDatatype-e7fbe225","./OrientedBoundingBox-ee3011f6","./RuntimeError-4f8ec8a2","./WebMercatorProjection-04ef6bc3","./createTaskProcessorWorker","./_commonjsHelpers-3aae1032-65601a27","./combine-d11b1f00","./AttributeCompression-5744d52e","./WebGLConstants-6da700a2","./EllipsoidTangentPlane-7ae496b2","./IntersectionTests-33ace2d6","./Plane-e916220d"],(function(t,e,n,i,o,a,r,s,c,u,h,d,l,g,m,p,I){"use strict";const f=Uint16Array.BYTES_PER_ELEMENT,E=Int32Array.BYTES_PER_ELEMENT,T=Uint32Array.BYTES_PER_ELEMENT,C=Float32Array.BYTES_PER_ELEMENT,M=Float64Array.BYTES_PER_ELEMENT;function x(t,e,n){n=i.defaultValue(n,a.CesiumMath);const o=t.length;for(let i=0;i<o;++i)if(n.equalsEpsilon(t[i],e,a.CesiumMath.EPSILON12))return i;return-1}const N=new n.Cartographic,b=new n.Cartesian3,S=new n.Cartesian3,w=new n.Cartesian3,B=new n.Matrix4;function P(t,e,o,r,s,c,u,h,d,l,g){const m=h.length;for(let p=0;p<m;++p){const I=h[p],f=I.cartographic,E=I.index,T=t.length,C=f.longitude;let M=f.latitude;M=a.CesiumMath.clamp(M,-a.CesiumMath.PI_OVER_TWO,a.CesiumMath.PI_OVER_TWO);const x=f.height-u.skirtHeight;u.hMin=Math.min(u.hMin,x),n.Cartographic.fromRadians(C,M,x,N),l&&(N.longitude+=d),l?p===m-1?N.latitude+=g:0===p&&(N.latitude-=g):N.latitude+=d;const S=u.ellipsoid.cartographicToCartesian(N);t.push(S),e.push(x),o.push(n.Cartesian2.clone(o[E])),r.length>0&&r.push(r[E]),s.length>0&&s.push(s[E]),n.Matrix4.multiplyByPoint(u.toENU,S,b);const w=u.minimum,B=u.maximum;n.Cartesian3.minimumByComponent(b,w,w),n.Cartesian3.maximumByComponent(b,B,B);const P=u.lastBorderPoint;if(i.defined(P)){const t=P.index;c.push(t,T-1,T,T,E,t)}u.lastBorderPoint=I}}return u((function(u,h){u.ellipsoid=n.Ellipsoid.clone(u.ellipsoid),u.rectangle=n.Rectangle.clone(u.rectangle);const d=function(u,h,d,l,g,m,p,I,A,y,R){let _,W,v,F,O,V;i.defined(l)?(_=l.west,W=l.south,v=l.east,F=l.north,O=l.width,V=l.height):(_=a.CesiumMath.toRadians(g.west),W=a.CesiumMath.toRadians(g.south),v=a.CesiumMath.toRadians(g.east),F=a.CesiumMath.toRadians(g.north),O=a.CesiumMath.toRadians(l.width),V=a.CesiumMath.toRadians(l.height));const Y=[W,F],H=[_,v],U=e.Transforms.eastNorthUpToFixedFrame(h,d),k=n.Matrix4.inverseTransformation(U,B);let L,j;A&&(L=c.WebMercatorProjection.geodeticLatitudeToMercatorAngle(W),j=1/(c.WebMercatorProjection.geodeticLatitudeToMercatorAngle(F)-L));const D=1!==m,G=new DataView(u);let z=Number.POSITIVE_INFINITY,q=Number.NEGATIVE_INFINITY;const J=S;J.x=Number.POSITIVE_INFINITY,J.y=Number.POSITIVE_INFINITY,J.z=Number.POSITIVE_INFINITY;const K=w;K.x=Number.NEGATIVE_INFINITY,K.y=Number.NEGATIVE_INFINITY,K.z=Number.NEGATIVE_INFINITY;let Q,X,Z=0,$=0,tt=0;for(X=0;X<4;++X){let t=Z;Q=G.getUint32(t,!0),t+=T;const e=a.CesiumMath.toRadians(180*G.getFloat64(t,!0));t+=M,-1===x(H,e)&&H.push(e);const n=a.CesiumMath.toRadians(180*G.getFloat64(t,!0));t+=M,-1===x(Y,n)&&Y.push(n),t+=2*M;let i=G.getInt32(t,!0);t+=E,$+=i,i=G.getInt32(t,!0),tt+=3*i,Z+=Q+T}const et=[],nt=[],it=new Array($),ot=new Array($),at=new Array($),rt=A?new Array($):[],st=D?new Array($):[],ct=new Array(tt),ut=[],ht=[],dt=[],lt=[];let gt=0,mt=0;for(Z=0,X=0;X<4;++X){Q=G.getUint32(Z,!0),Z+=T;const t=Z,e=a.CesiumMath.toRadians(180*G.getFloat64(Z,!0));Z+=M;const i=a.CesiumMath.toRadians(180*G.getFloat64(Z,!0));Z+=M;const o=a.CesiumMath.toRadians(180*G.getFloat64(Z,!0)),r=.5*o;Z+=M;const u=a.CesiumMath.toRadians(180*G.getFloat64(Z,!0)),h=.5*u;Z+=M;const l=G.getInt32(Z,!0);Z+=E;const g=G.getInt32(Z,!0);Z+=E,Z+=E;const m=new Array(l);for(let t=0;t<l;++t){const s=e+G.getUint8(Z++)*o;N.longitude=s;const l=i+G.getUint8(Z++)*u;N.latitude=l;let g=G.getFloat32(Z,!0);if(Z+=C,0!==g&&g<R&&(g*=-Math.pow(2,y)),g*=6371010,N.height=g,-1!==x(H,s)||-1!==x(Y,l)){const e=x(et,N,n.Cartographic);if(-1!==e){m[t]=nt[e];continue}et.push(n.Cartographic.clone(N)),nt.push(gt)}m[t]=gt,Math.abs(s-_)<r?ut.push({index:gt,cartographic:n.Cartographic.clone(N)}):Math.abs(s-v)<r?dt.push({index:gt,cartographic:n.Cartographic.clone(N)}):Math.abs(l-W)<h?ht.push({index:gt,cartographic:n.Cartographic.clone(N)}):Math.abs(l-F)<h&&lt.push({index:gt,cartographic:n.Cartographic.clone(N)}),z=Math.min(g,z),q=Math.max(g,q),at[gt]=g;const p=d.cartographicToCartesian(N);if(it[gt]=p,A&&(rt[gt]=(c.WebMercatorProjection.geodeticLatitudeToMercatorAngle(l)-L)*j),D){const t=d.geodeticSurfaceNormal(p);st[gt]=t}n.Matrix4.multiplyByPoint(k,p,b),n.Cartesian3.minimumByComponent(b,J,J),n.Cartesian3.maximumByComponent(b,K,K);let I=(s-_)/(v-_);I=a.CesiumMath.clamp(I,0,1);let f=(l-W)/(F-W);f=a.CesiumMath.clamp(f,0,1),ot[gt]=new n.Cartesian2(I,f),++gt}const p=3*g;for(let t=0;t<p;++t,++mt)ct[mt]=m[G.getUint16(Z,!0)],Z+=f;if(Q!==Z-t)throw new s.RuntimeError("Invalid terrain tile.")}it.length=gt,ot.length=gt,at.length=gt,A&&(rt.length=gt);D&&(st.length=gt);const pt=gt,It=mt,ft={hMin:z,lastBorderPoint:void 0,skirtHeight:I,toENU:k,ellipsoid:d,minimum:J,maximum:K};ut.sort((function(t,e){return e.cartographic.latitude-t.cartographic.latitude})),ht.sort((function(t,e){return t.cartographic.longitude-e.cartographic.longitude})),dt.sort((function(t,e){return t.cartographic.latitude-e.cartographic.latitude})),lt.sort((function(t,e){return e.cartographic.longitude-t.cartographic.longitude}));const Et=1e-5;if(P(it,at,ot,rt,st,ct,ft,ut,-Et*O,!0,-Et*V),P(it,at,ot,rt,st,ct,ft,ht,-Et*V,!1),P(it,at,ot,rt,st,ct,ft,dt,Et*O,!0,Et*V),P(it,at,ot,rt,st,ct,ft,lt,Et*V,!1),ut.length>0&&lt.length>0){const t=ut[0].index,e=pt,n=lt[lt.length-1].index,i=it.length-1;ct.push(n,i,e,e,t,n)}$=it.length;const Tt=e.BoundingSphere.fromPoints(it);let Ct;i.defined(l)&&(Ct=r.OrientedBoundingBox.fromRectangle(l,z,q,d));const Mt=new o.EllipsoidalOccluder(d).computeHorizonCullingPointPossiblyUnderEllipsoid(h,it,z),xt=new t.AxisAlignedBoundingBox(J,K,h),Nt=new o.TerrainEncoding(h,xt,ft.hMin,q,U,!1,A,D,m,p),bt=new Float32Array($*Nt.stride);let St=0;for(let t=0;t<$;++t)St=Nt.encode(bt,St,it[t],ot[t],at[t],void 0,rt[t],st[t]);const wt=ut.map((function(t){return t.index})).reverse(),Bt=ht.map((function(t){return t.index})).reverse(),Pt=dt.map((function(t){return t.index})).reverse(),At=lt.map((function(t){return t.index})).reverse();return Bt.unshift(Pt[Pt.length-1]),Bt.push(wt[0]),At.unshift(wt[wt.length-1]),At.push(Pt[0]),{vertices:bt,indices:new Uint16Array(ct),maximumHeight:q,minimumHeight:z,encoding:Nt,boundingSphere3D:Tt,orientedBoundingBox:Ct,occludeePointInScaledSpace:Mt,vertexCountWithoutSkirts:pt,indexCountWithoutSkirts:It,westIndicesSouthToNorth:wt,southIndicesEastToWest:Bt,eastIndicesNorthToSouth:Pt,northIndicesWestToEast:At}}(u.buffer,u.relativeToCenter,u.ellipsoid,u.rectangle,u.nativeRectangle,u.exaggeration,u.exaggerationRelativeHeight,u.skirtHeight,u.includeWebMercatorT,u.negativeAltitudeExponentBias,u.negativeElevationThreshold),l=d.vertices;h.push(l.buffer);const g=d.indices;return h.push(g.buffer),{vertices:l.buffer,indices:g.buffer,numberOfAttributes:d.encoding.stride,minimumHeight:d.minimumHeight,maximumHeight:d.maximumHeight,boundingSphere3D:d.boundingSphere3D,orientedBoundingBox:d.orientedBoundingBox,occludeePointInScaledSpace:d.occludeePointInScaledSpace,encoding:d.encoding,vertexCountWithoutSkirts:d.vertexCountWithoutSkirts,indexCountWithoutSkirts:d.indexCountWithoutSkirts,westIndicesSouthToNorth:d.westIndicesSouthToNorth,southIndicesEastToWest:d.southIndicesEastToWest,eastIndicesNorthToSouth:d.eastIndicesNorthToSouth,northIndicesWestToEast:d.northIndicesWestToEast}}))}));
