package com.startel.middleware.controller;

import cn.hutool.json.JSONUtil;
import com.startel.common.core.controller.BaseController;
import com.startel.common.core.domain.AjaxResult;
import com.startel.common.core.page.TableDataInfo;
import com.startel.common.utils.ServletUtils;
import com.startel.common.utils.StringUtils;
import com.startel.middleware.service.IResModelManageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.BadSqlGrammarException;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Map;

@RestController
@RequestMapping("/middleware/resmodel-manage")
@Slf4j
@Api(tags = "中间件模型管理接口")
public class ResModelManageController extends BaseController {
    @Autowired
    IResModelManageService resModelManageService;

    @PostMapping(value = "/query")
    @ApiOperation(value = "查询模型列表", notes = "查询模型列表")
    @ResponseBody
    public TableDataInfo queryResModels(@RequestBody Map<String, Object> params){
        String reqUrl = ServletUtils.getRequest().getRequestURI();
        TableDataInfo tableDataInfo = new TableDataInfo();
        tableDataInfo.setRows(new ArrayList());
        tableDataInfo.setTotal(0);
        try
        {
            log.info("{}查询参数: {}", reqUrl, JSONUtil.parse(params).toString());
            return resModelManageService.queryResModels(params);
        }
        catch (BadSqlGrammarException ex)
        {
            tableDataInfo.setCode(500);
            tableDataInfo.setMsg(StringUtils.format("查询发生不明异常：参数非法，请检测查询参数。"));
            log.error("查询发生不明异常", ex);
            return tableDataInfo;
        }
        catch (Exception ex)
        {
            tableDataInfo.setCode(500);
            tableDataInfo.setMsg(StringUtils.format("查询发生不明异常:{}", ex.getMessage()));
            log.error("查询发生不明异常", ex);
            return tableDataInfo;
        }
    }

    @PostMapping(value = "/tree")
    @ApiOperation(value = "查询模型树", notes = "查询模型树")
    @ResponseBody
    public AjaxResult queryResModelTrees(@RequestBody Map<String, Object> params){
        String reqUrl = ServletUtils.getRequest().getRequestURI();
        try
        {
            log.info("{}查询参数: {}", reqUrl, JSONUtil.parse(params).toString());
            return resModelManageService.queryResModelTrees(params);
        }
        catch (BadSqlGrammarException ex)
        {
            log.error("查询发生不明异常", ex);
            return AjaxResult.error("查询发生不明异常：参数非法，请检测查询参数。", new ArrayList<>());
        }
        catch (Exception ex)
        {
            log.error("查询发生不明异常", ex);
            return AjaxResult.error("查询发生不明异常：参数非法，请检测查询参数。", new ArrayList<>());
        }
    }

    @PostMapping(value = "/detail")
    @ApiOperation(value = "模型详情", notes = "查询模型详情")
    @ResponseBody
    public AjaxResult detailResModel(@RequestBody Map<String, Object> params) {
        String reqUrl = ServletUtils.getRequest().getRequestURI();
        try
        {
            log.info("{}入参：{}", reqUrl, JSONUtil.parse(params).toString());
            return resModelManageService.detailResModel(params);
        }
        catch (Exception ex)
        {
            log.error("查询发生不明异常", ex);
            return AjaxResult.error("查询发生异常，请检查参数。");
        }
    }

    @PostMapping(value = "/save")
    @ApiOperation(value = "保存模型", notes = "新增或者修改模型")
    @ResponseBody
    public AjaxResult saveResModel(@RequestBody Map<String, Object> params) {
        String reqUrl = ServletUtils.getRequest().getRequestURI();
        try
        {
            log.info("{}入参：{}", reqUrl, JSONUtil.parse(params).toString());
            return resModelManageService.saveResModel(params);
        }
        catch (Exception ex)
        {
            log.error("保存模型发生不明异常", ex);
            return AjaxResult.error("保存模型发生异常，请检查参数。");
        }
    }

    @PostMapping(value = "/delete")
    @ApiOperation(value = "删除模型(逻辑删除)", notes = "删除模型，执行逻辑删除，模型物理记录和属性表记录仍然存在")
    @ResponseBody
    public AjaxResult deleteResModel(@RequestBody Map<String, Object> params) {
        String reqUrl = ServletUtils.getRequest().getRequestURI();
        try
        {
            log.info("{}入参：{}", reqUrl, JSONUtil.parse(params).toString());
            return resModelManageService.deleteResModel(params);
        }
        catch (Exception ex)
        {
            log.error("删除模型发生不明异常", ex);
            return AjaxResult.error("删除模型发生异常，请检查参数。");
        }
    }

    @PostMapping(value = "/save-attr")
    @ApiOperation(value = "保存模型属性", notes = "新增或者修改模型属性")
    @ResponseBody
    public AjaxResult saveResModelAttr(@RequestBody Map<String, Object> params) {
        String reqUrl = ServletUtils.getRequest().getRequestURI();
        try
        {
            log.info("{}入参：{}", reqUrl, JSONUtil.parse(params).toString());
            return resModelManageService.saveResModelAttr(params);
        }
        catch (Exception ex)
        {
            log.error("接口发生不明异常", ex);
            return AjaxResult.error("接口发生异常，请检查参数。");
        }
    }

    @PostMapping(value = "/save-attr-batch")
    @ApiOperation(value = "批量保存模型属性", notes = "批量新增或者修改模型属性")
    @ResponseBody
    public AjaxResult saveResModelAttrBatch(@RequestBody Map<String, Object> params) {
        String reqUrl = ServletUtils.getRequest().getRequestURI();
        try
        {
            log.info("{}入参：{}", reqUrl, JSONUtil.parse(params).toString());
            return resModelManageService.saveResModelAttrBatch(params);
        }
        catch (Exception ex)
        {
            log.error("接口发生不明异常", ex);
            return AjaxResult.error("接口发生异常，请检查参数。");
        }
    }

    @PostMapping(value = "/delete-attr")
    @ApiOperation(value = "删除模型属性(逻辑删除)", notes = "删除模型属性，执行逻辑删除，模型物理记录和属性表记录仍然存在,支持批量删除")
    @ResponseBody
    public AjaxResult deleteResModelAttr(@RequestBody Map<String, Object> params) {
        String reqUrl = ServletUtils.getRequest().getRequestURI();
        try
        {
            log.info("{}入参：{}", reqUrl, JSONUtil.parse(params).toString());
            return resModelManageService.deleteResModelAttr(params);
        }
        catch (Exception ex)
        {
            log.error("删除模型属性发生不明异常", ex);
            return AjaxResult.error("删除模型属性发生异常，请检查参数。");
        }
    }

    @PostMapping(value = "/query-form")
    @ApiOperation(value = "查询模型表单配置", notes = "查询模型表单配置")
    @ResponseBody
    public AjaxResult queryResModelForm(@RequestBody Map<String, Object> params) {
        String reqUrl = ServletUtils.getRequest().getRequestURI();
        try
        {
            log.info("{}入参：{}", reqUrl, JSONUtil.parse(params).toString());
            return resModelManageService.queryResModelForm(params);
        }
        catch (Exception ex)
        {
            log.error("查询模型表单发生不明异常", ex);
            return AjaxResult.error("查询模型表单发生异常，请检查参数。");
        }
    }

    @PostMapping(value = "/save-form")
    @ApiOperation(value = "保存模型表单配置", notes = "保存模型表单配置")
    @ResponseBody
    public AjaxResult saveResModelForm(@RequestBody Map<String, Object> params) {
        String reqUrl = ServletUtils.getRequest().getRequestURI();
        try
        {
            log.info("{}入参：{}", reqUrl, JSONUtil.parse(params).toString());
            return resModelManageService.saveResModelForm(params);
        }
        catch (Exception ex)
        {
            log.error("保存模型表单配置发生不明异常", ex);
            return AjaxResult.error("保存模型表单配置发生异常，请检查参数。");
        }
    }

    @PostMapping(value = "/delete-form")
    @ApiOperation(value = "删除模型表单配置", notes = "删除模型表单配置")
    @ResponseBody
    public AjaxResult deleteResModelForm(@RequestBody Map<String, Object> params) {
        String reqUrl = ServletUtils.getRequest().getRequestURI();
        try
        {
            log.info("{}入参：{}", reqUrl, JSONUtil.parse(params).toString());
            return resModelManageService.deleteResModelForm(params);
        }
        catch (Exception ex)
        {
            log.error("删除模型表单配置发生不明异常", ex);
            return AjaxResult.error("删除模型表单配置发生异常，请检查参数。");
        }
    }
}
