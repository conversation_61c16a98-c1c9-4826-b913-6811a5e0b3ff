package com.startel.middleware.controller;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.startel.common.core.controller.BaseController;
import com.startel.common.core.domain.AjaxResult;
import com.startel.common.utils.ServletUtils;
import com.startel.common.utils.StringUtils;
import com.startel.framework.manager.AsyncManager;
import com.startel.middleware.service.ICommonTableOperate;
import com.startel.middleware.task.AsyncBusiTask;
import com.startel.middleware.utils.TextUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/middleware/res-special-op")
@Slf4j
@Api(tags = "中间件资源特殊操作接口")
public class ResSpecialOperateController extends BaseController {
    @Autowired
    ICommonTableOperate commonTableOperate;
    @Autowired
    JdbcTemplate jdbcTemplate;

    @PostMapping(value = "/cable/{cable_id}/rm")
    @ApiOperation(value = "删除光缆", notes = "删除光缆连同删除关联资源，包括光缆属性、光缆几何、光缆段属性、光缆段几何、光缆敷设段、光缆敷设结果等记录")
    @ResponseBody
    public AjaxResult removeCableRecord(@PathVariable("cable_id") String cable_id) {
        String reqUrl = ServletUtils.getRequest().getRequestURI();
        log.info("当前请求地址:" + reqUrl);
        try
        {
            if(TextUtil.isNull(cable_id))
            {
                return AjaxResult.error("光缆标识参数不允许为空");
            }
            log.info("光缆ID: {}", cable_id);
            //删除光缆
            String sql = "delete from t_cable where resid=?";
            jdbcTemplate.update(sql, cable_id);
            sql = "delete from g_cable where resid=?";
            jdbcTemplate.update(sql, cable_id);
            //删除光接头盒
            sql = "delete from g_joint_box where resid in (select resid from t_joint_box where rel_resid =?)";
            jdbcTemplate.update(sql, cable_id);
            sql = "delete from t_joint_box where rel_resid = ?";
            jdbcTemplate.update(sql, cable_id);
            //删除接头盒占用信息
            sql = "delete from t_joint_box_fcore_occupy where rel_cable_id = ?";
            jdbcTemplate.update(sql, cable_id);
            //删除光缆段
            sql = "delete from g_cable_seg where resid in (select resid from t_cable_seg where res_cable_id=?)";
            jdbcTemplate.update(sql, cable_id);
            sql = "delete from t_cable_seg where res_cable_id=?";
            jdbcTemplate.update(sql, cable_id);
            //删除敷设段
            sql = "delete from t_cable_lay_relations where related_cable_id=?";
            jdbcTemplate.update(sql, cable_id);
            //删除敷设结果json串
            sql = "delete from t_cable_lay_results where related_cable_id=?";
            jdbcTemplate.update(sql, cable_id);

            return AjaxResult.success("成功!");
        }
        catch (Exception ex)
        {
            log.error("删除光缆发生不明异常", ex);
            return AjaxResult.error("删除光缆发生不明异常");
        }
    }

    @PostMapping(value = "/cable/calc-length")
    @ApiOperation(value = "计算光缆、中继段、光缆段的各类长度并返回结果", notes = "计算光缆、中继段、光缆段的各类长度并返回结果")
    @ResponseBody
    public AjaxResult calcCableLength(@RequestBody Map<String, Object> params) throws Exception {
        String reqUrl = ServletUtils.getRequest().getRequestURI();
        String opUser = "anonymous";
        log.info("当前请求地址:" + reqUrl);
        try
        {
            AjaxResult ajaxResult = AjaxResult.success(params);

            Map<String, Object> cableRecord = (Map<String, Object>)params.get("cable"); //光缆
            Collection cableSegRecords = (Collection)params.get("cableSegs"); //中继段
            Collection cableSectionRecords = (Collection)params.get("cableSections"); //光缆段
            String taskid = MapUtils.getString(params, "taskid", "");

            String defaultSysParamStr = "{\"reserves\":[{\"item_name\":\"机房\",\"res_model\":\"room\",\"unit\":\"站\",\"item_value\":10,\"item_comment\":\"标准规范10~15米\",\"min_value\":10,\"max_value\":15,\"step\":1,\"format\":\"n0\"},{\"item_name\":\"光缆交接箱\",\"res_model\":\"opti_tran_box\",\"unit\":\"个\",\"item_value\":5,\"item_comment\":\"标准规范5~10米\",\"min_value\":5,\"max_value\":10,\"step\":1,\"format\":\"n0\"},{\"item_name\":\"光纤分纤箱\",\"res_model\":\"opti_fiber_box\",\"unit\":\"个\",\"item_value\":2,\"item_comment\":\"标准规范1~3米\",\"min_value\":1,\"max_value\":3,\"step\":1,\"format\":\"n0\"},{\"item_name\":\"光缆接头盒\",\"res_model\":\"joint_box\",\"unit\":\"个\",\"item_value\":15,\"item_comment\":\"标准规范每侧5~10米\",\"min_value\":5,\"max_value\":15,\"step\":1,\"format\":\"n0\"},{\"item_name\":\"架空杆路\",\"res_model\":\"pole\",\"unit\":\"杆\",\"item_value\":0.2,\"item_comment\":\"预留弯半径30cm，仰俯角大于 30 度，计算至少0.157\",\"min_value\":0.2,\"max_value\":1,\"step\":0.1,\"format\":\"n1\"},{\"item_name\":\"管道人（手）孔\",\"res_model\":\"staff_well\",\"unit\":\"个\",\"item_value\":0.5,\"item_comment\":\"标准规范0.5~1米\",\"min_value\":0.5,\"max_value\":1,\"step\":0.1,\"format\":\"n1\"},{\"item_name\":\"光缆预留架\",\"res_model\":\"t_reserve_rack\",\"unit\":\"处\",\"item_value\":10,\"item_comment\":\"每500米预留长度，提示校验，超500米，没有设置预留架，提示需要设置\",\"min_value\":10,\"max_value\":15,\"step\":1,\"format\":\"n0\"},{\"item_name\":\"其他\",\"res_model\":\"\",\"unit\":\"处\",\"item_value\":10,\"item_comment\":\"提供人工设置功能，光缆穿越河流、公路、跨越桥梁时，光缆在两侧人手孔内或杆路各预留长度\",\"min_value\":10,\"max_value\":15,\"step\":1,\"format\":\"n0\"}],\"bend\":{\"bury_cable\":0.007,\"pipe_cable\":0.01,\"pole_cable\":0.007},\"loss\":0.007,\"test_window\":\"1\",\"single_tray_len\":2,\"dictionary\":{\"test_window\":[{\"text\":\"单窗口\",\"value\":\"1\"},{\"text\":\"双窗口\",\"value\":\"2\"}],\"single_tray_len\":[{\"text\":\"2km\",\"value\":\"2\"},{\"text\":\"3km\",\"value\":\"3\"}]}}";
            String sql = "select * from res_task_params where taskid = ?";
            JSONObject taskParamsJson = new JSONObject(defaultSysParamStr);
            try
            {
                List<Map<String, Object>> listParams = jdbcTemplate.queryForList(sql, taskid);
                if(listParams.size() > 0)
                {
                    Map<String, Object> paramRowMap = listParams.get(0);
                    String paramsJsonStr = MapUtils.getString(paramRowMap, "params_json", defaultSysParamStr);
                    taskParamsJson = new JSONObject(paramsJsonStr);
                }
            }
            catch (Exception ex)
            {
                log.error("系统光缆计算参数查询失败：" + ex.getMessage(), ex);
            }
            Double bury_cable_bendRatio = MapUtils.getDouble(taskParamsJson.getJSONObject("bend"), "bury_cable", 0.007);
            Double pipe_cable_bendRatio = MapUtils.getDouble(taskParamsJson.getJSONObject("bend"), "pipe_cable", 0.01);
            Double pole_cable_bendRatio = MapUtils.getDouble(taskParamsJson.getJSONObject("bend"), "pole_cable", 0.007);
            Double lossRatio = MapUtils.getDouble(taskParamsJson, "loss", 0.007);
            String testWindow = MapUtils.getString(taskParamsJson, "test_window", "1");
            Integer single_tray_len = MapUtils.getInteger(taskParamsJson, "single_tray_len", 2);

            //数据字典准备
            Map<String, Map<String, Object>> cableSectionDic = new HashMap<>();
            List<String> listBearingSegIds = new ArrayList<>();
            List<String> listBearingPointIds = new ArrayList<>();
            for(Object cableSectionObj : cableSectionRecords)
            {
                Map cableSectionMap = (Map) cableSectionObj;
                String resid = MapUtils.getString(cableSectionMap, "resid", "");
                String relBrearingSegId = MapUtils.getString(cableSectionMap, "line_seg_id", "");
                String relBrearingSegType = MapUtils.getString(cableSectionMap, "segment_type", "");
                String fromResid = MapUtils.getString(cableSectionMap, "from_resid", "");
                String toResid = MapUtils.getString(cableSectionMap, "to_resid", "");
                String fromRestype = MapUtils.getString(cableSectionMap, "from_res_type", "");
                String toRestype = MapUtils.getString(cableSectionMap, "to_res_type", "");
                cableSectionDic.put(resid, cableSectionMap);
                listBearingSegIds.add(relBrearingSegType + ":" + relBrearingSegId);
                listBearingPointIds.add(fromRestype + ":" + fromResid);
                listBearingPointIds.add(toRestype + ":" + toResid);
            }
            //查询所有承载段详情
            List<Map<String, Object>> listBearingLines = new ArrayList<>();
            Map<String, Map<String, Object>> bearingLinesMap = new HashMap<>();
            List<Map<String, Object>> listBearingPoints = new ArrayList<>();
            Map<String, Map<String, Object>> bearingPointsMap = new HashMap<>();

            MapSqlParameterSource parameters = new MapSqlParameterSource();
            List<String> wallIds = listBearingSegIds.stream().filter((item) -> item.startsWith("wall-seg:")).map((item) -> item.replace("wall-seg:", "")).collect(Collectors.toList());
            String inSql = String.join(",", Collections.nCopies(wallIds.size(), "?"));
            sql = "select * from t_wall_seg where resid in (" + inSql + ")";
            if(wallIds.size() > 0)
            {
                List<Map<String, Object>> mapRows = jdbcTemplate.queryForList(sql, wallIds.toArray());
                listBearingLines.addAll(mapRows);
            }

            List<String> poleIds = listBearingSegIds.stream().filter((item) -> item.startsWith("pole-seg:")).map((item) -> item.replace("pole-seg:", "")).collect(Collectors.toList());
            inSql = String.join(",", Collections.nCopies(poleIds.size(), "?"));
            sql = "select * from t_pole_road_seg where resid in (" + inSql + ")";
            if(poleIds.size() > 0)
            {
                List<Map<String, Object>> mapRows = jdbcTemplate.queryForList(sql, poleIds.toArray());
                listBearingLines.addAll(mapRows);
            }

            List<String> pipeIds = listBearingSegIds.stream().filter((item) -> item.startsWith("pipe-seg:")).map((item) -> item.replace("pipe-seg:", "")).collect(Collectors.toList());
            inSql = String.join(",", Collections.nCopies(pipeIds.size(), "?"));
            sql = "select * from t_pipeline_seg where resid in (" + inSql + ")";
            if(pipeIds.size() > 0)
            {
                List<Map<String, Object>> mapRows = jdbcTemplate.queryForList(sql, pipeIds.toArray());
                listBearingLines.addAll(mapRows);
            }

            List<String> buryIds = listBearingSegIds.stream().filter((item) -> item.startsWith("bury-seg:")).map((item) -> item.replace("bury-seg:", "")).collect(Collectors.toList());
            inSql = String.join(",", Collections.nCopies(buryIds.size(), "?"));
            sql = "select * from t_marker_route_seg where resid in (" + inSql + ")";
            if(buryIds.size() > 0)
            {
                List<Map<String, Object>> mapRows = jdbcTemplate.queryForList(sql, buryIds.toArray());
                listBearingLines.addAll(mapRows);
            }

            for(Map<String, Object> mapRow : listBearingLines)
            {
                String resid = MapUtils.getString(mapRow, "resid", "");
                bearingLinesMap.put(resid, mapRow);
            }

            //承载点


            List<String> listCalcedRerserveSegIds = new ArrayList<>();
            //遍历中继段
            for(Object oCableSeg : cableSegRecords)
            {
                Map<String, Object> cableSegMap = (Map<String, Object>) oCableSeg;
                String cableSegResid = MapUtils.getString(cableSegMap, "resid", "");

                //初始化各类长度
                cableSegMap.put("construct_length", 0.0);
                cableSegMap.put("budget_length", 0.0);
                cableSegMap.put("measure_length", 0.0);
                cableSegMap.put("bury_measure_length", 0.0);
                cableSegMap.put("pole_measure_length", 0.0);
                cableSegMap.put("pipe_measure_length", 0.0);
                cableSegMap.put("newpipe_measure_length", 0.0);
                cableSegMap.put("other_measure_length", 0.0);
                cableSegMap.put("bend_length", 0.0);
                cableSegMap.put("loss_length", 0.0);
                cableSegMap.put("up_length", 0.0);
                cableSegMap.put("reserve_length", 0.0);
                cableSegMap.put("room_reserve_length", 0.0);
                cableSegMap.put("optitrans_reserve_length", 0.0);
                cableSegMap.put("optifiber_reserve_length", 0.0);
                cableSegMap.put("jointbox_reserve_length", 0.0);
                cableSegMap.put("pole_reserve_length", 0.0);
                cableSegMap.put("pipe_reserve_length", 0.0);
                cableSegMap.put("rack_reserve_length", 0.0);
                cableSegMap.put("other_reserve_length", 0.0);

                //开始遍历计算测量长度、自然弯曲长度、预留长度分量
                for(Object oCableSection : cableSectionRecords)
                {
                    Map<String, Object> cableSectionMap = (Map<String, Object>) oCableSection;
                    String cableSectionResid = MapUtils.getString(cableSectionMap, "resid", "");
                    String relSegResid = MapUtils.getString(cableSectionMap, "cable_seg_id", "");
                    if(relSegResid.equals(cableSegResid))
                    {
                        String bearingSegResid = MapUtils.getString(cableSectionMap, "line_seg_id", "");
                        String bearingSegType = MapUtils.getString(cableSectionMap, "segment_type", "");
                        Double auto_length = MapUtils.getDouble(cableSectionMap, "auto_length", 0.0);
                        String design_status = MapUtils.getString(cableSectionMap, "design_status", "");
                        String fromResid = MapUtils.getString(cableSectionMap, "from_resid", "");
                        String toResid = MapUtils.getString(cableSectionMap, "to_resid", "");
                        String fromRestype = MapUtils.getString(cableSectionMap, "from_res_type", "");
                        String toRestype = MapUtils.getString(cableSectionMap, "to_res_type", "");

                        Map<String, Object> bearingSegRowMap = bearingLinesMap.containsKey(bearingSegResid) ? bearingLinesMap.get(bearingSegResid) : new HashMap<>();

                        //测量长度、弯曲长度计算
                        if(bearingSegType.equals("bury-seg"))
                        {
                            cableSegMap.put("bury_measure_length", MapUtils.getDouble(cableSegMap, "bury_measure_length", 0.0) + auto_length);
                            cableSegMap.put("bend_length", MapUtils.getDouble(cableSegMap, "bend_length", 0.0) + auto_length*bury_cable_bendRatio);
                        }
                        else if(bearingSegType.equals("pole-seg"))
                        {
                            cableSegMap.put("pole_measure_length", MapUtils.getDouble(cableSegMap, "pole_measure_length", 0.0) + auto_length);
                            cableSegMap.put("bend_length", MapUtils.getDouble(cableSegMap, "bend_length", 0.0) + auto_length*pole_cable_bendRatio);
                        }
                        else if(bearingSegType.equals("pipe-seg") && design_status.equals("3"))
                        {
                            //新建管道段
                            cableSegMap.put("newpipe_measure_length", MapUtils.getDouble(cableSegMap, "newpipe_measure_length", 0.0) + auto_length);
                            cableSegMap.put("bend_length", MapUtils.getDouble(cableSegMap, "bend_length", 0.0) + auto_length*pipe_cable_bendRatio);
                        }
                        else if(bearingSegType.equals("pipe-seg"))
                        {
                            cableSegMap.put("pipe_measure_length", MapUtils.getDouble(cableSegMap, "pipe_measure_length", 0.0) + auto_length);
                            cableSegMap.put("bend_length", MapUtils.getDouble(cableSegMap, "bend_length", 0.0) + auto_length*pipe_cable_bendRatio);
                        }
                        else
                        {
                            cableSegMap.put("other_measure_length", MapUtils.getDouble(cableSegMap, "other_measure_length", 0.0) + auto_length);
                        }
                        cableSegMap.put("measure_length", MapUtils.getDouble(cableSegMap, "measure_length", 0.0) + auto_length);

                        //预留长度计算
                        JSONArray reserves = taskParamsJson.getJSONArray("reserves");
                        //整个光缆的尺度上面避免重算起点、终点内的预览长度
                        if(!listCalcedRerserveSegIds.contains(fromResid))
                        {
                            listCalcedRerserveSegIds.add(fromResid);
                            if(fromRestype.equals("room"))
                            {
                                List list = reserves.stream().filter((item) -> MapUtils.getString((JSONObject)item, "res_model", "").equals("room")).collect(Collectors.toList());
                                if(list.size() > 0)
                                {
                                    JSONObject reserveJsonParam = (JSONObject)list.get(0);
                                    Double item_val = MapUtils.getDouble(reserveJsonParam, "item_value", 0.0);
                                    cableSegMap.put("room_reserve_length", MapUtils.getDouble(cableSegMap, "room_reserve_length", 0.0) + item_val);
                                }
                            }
                            else if(fromRestype.equals("opti_tran_box"))
                            {
                                List list = reserves.stream().filter((item) -> MapUtils.getString((JSONObject)item, "res_model", "").equals("opti_tran_box")).collect(Collectors.toList());
                                if(list.size() > 0)
                                {
                                    JSONObject reserveJsonParam = (JSONObject)list.get(0);
                                    Double item_val = MapUtils.getDouble(reserveJsonParam, "item_value", 0.0);
                                    cableSegMap.put("optitrans_reserve_length", MapUtils.getDouble(cableSegMap, "optitrans_reserve_length", 0.0) + item_val);
                                }
                            }
                            else if(fromRestype.equals("opti_fiber_box"))
                            {
                                List list = reserves.stream().filter((item) -> MapUtils.getString((JSONObject)item, "res_model", "").equals("opti_fiber_box")).collect(Collectors.toList());
                                if(list.size() > 0)
                                {
                                    JSONObject reserveJsonParam = (JSONObject)list.get(0);
                                    Double item_val = MapUtils.getDouble(reserveJsonParam, "item_value", 0.0);
                                    cableSegMap.put("optifiber_reserve_length", MapUtils.getDouble(cableSegMap, "optifiber_reserve_length", 0.0) + item_val);
                                }

                            }
                            else if(fromRestype.equals("pole"))
                            {
                                List list = reserves.stream().filter((item) -> MapUtils.getString((JSONObject)item, "res_model", "").equals("pole")).collect(Collectors.toList());
                                if(list.size() > 0)
                                {
                                    JSONObject reserveJsonParam = (JSONObject)list.get(0);
                                    Double item_val = MapUtils.getDouble(reserveJsonParam, "item_value", 0.0);
                                    cableSegMap.put("pole_reserve_length", MapUtils.getDouble(cableSegMap, "pole_reserve_length", 0.0) + item_val);
                                }
                            }
                            else if(fromRestype.equals("staff_well"))
                            {
                                List list = reserves.stream().filter((item) -> MapUtils.getString((JSONObject)item, "res_model", "").equals("staff_well")).collect(Collectors.toList());
                                if(list.size() > 0)
                                {
                                    JSONObject reserveJsonParam = (JSONObject)list.get(0);
                                    Double item_val = MapUtils.getDouble(reserveJsonParam, "item_value", 0.0);
                                    cableSegMap.put("pipe_reserve_length", MapUtils.getDouble(cableSegMap, "pipe_reserve_length", 0.0) + item_val);
                                }
                            }
                        }

                        if(!listCalcedRerserveSegIds.contains(toResid))
                        {
                            listCalcedRerserveSegIds.add(toResid);
                            if(toRestype.equals("room"))
                            {
                                List list = reserves.stream().filter((item) -> MapUtils.getString((JSONObject)item, "res_model", "").equals("room")).collect(Collectors.toList());
                                if(list.size() > 0)
                                {
                                    JSONObject reserveJsonParam = (JSONObject)list.get(0);
                                    Double item_val = MapUtils.getDouble(reserveJsonParam, "item_value", 0.0);
                                    cableSegMap.put("room_reserve_length", MapUtils.getDouble(cableSegMap, "room_reserve_length", 0.0) + item_val);
                                }
                            }
                            else if(toRestype.equals("opti_tran_box"))
                            {
                                List list = reserves.stream().filter((item) -> MapUtils.getString((JSONObject)item, "res_model", "").equals("opti_tran_box")).collect(Collectors.toList());
                                if(list.size() > 0)
                                {
                                    JSONObject reserveJsonParam = (JSONObject)list.get(0);
                                    Double item_val = MapUtils.getDouble(reserveJsonParam, "item_value", 0.0);
                                    cableSegMap.put("optitrans_reserve_length", MapUtils.getDouble(cableSegMap, "optitrans_reserve_length", 0.0) + item_val);
                                }
                            }
                            else if(toRestype.equals("opti_fiber_box"))
                            {
                                List list = reserves.stream().filter((item) -> MapUtils.getString((JSONObject)item, "res_model", "").equals("opti_fiber_box")).collect(Collectors.toList());
                                if(list.size() > 0)
                                {
                                    JSONObject reserveJsonParam = (JSONObject)list.get(0);
                                    Double item_val = MapUtils.getDouble(reserveJsonParam, "item_value", 0.0);
                                    cableSegMap.put("optifiber_reserve_length", MapUtils.getDouble(cableSegMap, "optifiber_reserve_length", 0.0) + item_val);
                                }

                            }
                            else if(toRestype.equals("pole"))
                            {
                                List list = reserves.stream().filter((item) -> MapUtils.getString((JSONObject)item, "res_model", "").equals("pole")).collect(Collectors.toList());
                                if(list.size() > 0)
                                {
                                    JSONObject reserveJsonParam = (JSONObject)list.get(0);
                                    Double item_val = MapUtils.getDouble(reserveJsonParam, "item_value", 0.0);
                                    cableSegMap.put("pole_reserve_length", MapUtils.getDouble(cableSegMap, "pole_reserve_length", 0.0) + item_val);
                                }
                            }
                            else if(toRestype.equals("staff_well"))
                            {
                                List list = reserves.stream().filter((item) -> MapUtils.getString((JSONObject)item, "res_model", "").equals("staff_well")).collect(Collectors.toList());
                                if(list.size() > 0)
                                {
                                    JSONObject reserveJsonParam = (JSONObject)list.get(0);
                                    Double item_val = MapUtils.getDouble(reserveJsonParam, "item_value", 0.0);
                                    cableSegMap.put("pipe_reserve_length", MapUtils.getDouble(cableSegMap, "pipe_reserve_length", 0.0) + item_val);
                                }
                            }

                            else if(fromRestype.equals("joint_box"))
                            {

                            }
                            else if(fromRestype.equals("t_reserve_rack"))
                            {

                            }
                        }
                        //其他预留长度从承载段继承
                        cableSectionMap.put("other_reserve_length", MapUtils.getDouble(bearingSegRowMap, "other_reserve_length", 0.0));
                        cableSegMap.put("other_reserve_length", MapUtils.getDouble(cableSegMap, "other_reserve_length", 0.0) + MapUtils.getDouble(cableSectionMap, "other_reserve_length", 0.0));

                    }
                }

                //算预览长度总值
                Double room_reserve_length = MapUtils.getDouble(cableSegMap, "room_reserve_length", 0.0);
                Double optitrans_reserve_length = MapUtils.getDouble(cableSegMap, "optitrans_reserve_length", 0.0);
                Double optifiber_reserve_length = MapUtils.getDouble(cableSegMap, "optifiber_reserve_length", 0.0);
                Double jointbox_reserve_length = MapUtils.getDouble(cableSegMap, "jointbox_reserve_length", 0.0);
                Double pole_reserve_length = MapUtils.getDouble(cableSegMap, "pole_reserve_length", 0.0);
                Double pipe_reserve_length = MapUtils.getDouble(cableSegMap, "pipe_reserve_length", 0.0);
                Double rack_reserve_length = MapUtils.getDouble(cableSegMap, "rack_reserve_length", 0.0);
                Double other_reserve_length = MapUtils.getDouble(cableSegMap, "other_reserve_length", 0.0);
                cableSegMap.put("reserve_length", room_reserve_length + optitrans_reserve_length + optifiber_reserve_length + jointbox_reserve_length + pole_reserve_length + pipe_reserve_length + rack_reserve_length + other_reserve_length);

                //损耗长度
                Double lossLen = MapUtils.getDouble(cableSegMap, "measure_length", 0.0) * lossRatio;
                cableSegMap.put("loss_length", lossLen);
                //施工长度
                Double constructLen = MapUtils.getDouble(cableSegMap, "measure_length", 0.0) + MapUtils.getDouble(cableSegMap, "bend_length", 0.0) + MapUtils.getDouble(cableSegMap, "reserve_length", 0.0);
                cableSegMap.put("construct_length", constructLen);
                //引上长度
                Double upLen = 0.0;
                cableSegMap.put("up_length", upLen);
                //预算长度
                cableSegMap.put("budget_length", constructLen + lossLen + upLen);
            }

            //光缆长度汇总
            cableRecord.put("construct_length", 0.0);
            cableRecord.put("budget_length", 0.0);
            cableRecord.put("measure_length", 0.0);
            cableRecord.put("bury_measure_length", 0.0);
            cableRecord.put("pole_measure_length", 0.0);
            cableRecord.put("pipe_measure_length", 0.0);
            cableRecord.put("newpipe_measure_length", 0.0);
            cableRecord.put("other_measure_length", 0.0);
            cableRecord.put("bend_length", 0.0);
            cableRecord.put("loss_length", 0.0);
            cableRecord.put("up_length", 0.0);
            cableRecord.put("reserve_length", 0.0);
            cableRecord.put("room_reserve_length", 0.0);
            cableRecord.put("optitrans_reserve_length", 0.0);
            cableRecord.put("optifiber_reserve_length", 0.0);
            cableRecord.put("jointbox_reserve_length", 0.0);
            cableRecord.put("pole_reserve_length", 0.0);
            cableRecord.put("pipe_reserve_length", 0.0);
            cableRecord.put("rack_reserve_length", 0.0);
            cableRecord.put("other_reserve_length", 0.0);
            for(Object oCableSeg : cableSegRecords) {
                Map<String, Object> cableSegMap = (Map<String, Object>) oCableSeg;
                String res_cable_id = MapUtils.getString(cableSegMap, "res_cable_id", "");
                if(MapUtils.getString(cableRecord, "resid", "").equals(res_cable_id))
                {
                    cableRecord.put("construct_length", MapUtils.getDouble(cableRecord, "construct_length", 0.0) + MapUtils.getDouble(cableSegMap, "construct_length", 0.0));
                    cableRecord.put("budget_length", MapUtils.getDouble(cableRecord, "budget_length", 0.0) + MapUtils.getDouble(cableSegMap, "budget_length", 0.0));
                    cableRecord.put("measure_length", MapUtils.getDouble(cableRecord, "measure_length", 0.0) + MapUtils.getDouble(cableSegMap, "measure_length", 0.0));
                    cableRecord.put("bury_measure_length", MapUtils.getDouble(cableRecord, "bury_measure_length", 0.0) + MapUtils.getDouble(cableSegMap, "bury_measure_length", 0.0));
                    cableRecord.put("pole_measure_length", MapUtils.getDouble(cableRecord, "pole_measure_length", 0.0) + MapUtils.getDouble(cableSegMap, "pole_measure_length", 0.0));
                    cableRecord.put("pipe_measure_length", MapUtils.getDouble(cableRecord, "pipe_measure_length", 0.0) + MapUtils.getDouble(cableSegMap, "pipe_measure_length", 0.0));
                    cableRecord.put("newpipe_measure_length", MapUtils.getDouble(cableRecord, "newpipe_measure_length", 0.0) + MapUtils.getDouble(cableSegMap, "newpipe_measure_length", 0.0));
                    cableRecord.put("other_measure_length", MapUtils.getDouble(cableRecord, "other_measure_length", 0.0) + MapUtils.getDouble(cableSegMap, "other_measure_length", 0.0));
                    cableRecord.put("bend_length", MapUtils.getDouble(cableRecord, "bend_length", 0.0) + MapUtils.getDouble(cableSegMap, "bend_length", 0.0));
                    cableRecord.put("loss_length", MapUtils.getDouble(cableRecord, "loss_length", 0.0) + MapUtils.getDouble(cableSegMap, "loss_length", 0.0));
                    cableRecord.put("up_length", MapUtils.getDouble(cableRecord, "up_length", 0.0) + MapUtils.getDouble(cableSegMap, "up_length", 0.0));
                    cableRecord.put("reserve_length", MapUtils.getDouble(cableRecord, "reserve_length", 0.0) + MapUtils.getDouble(cableSegMap, "reserve_length", 0.0));
                    cableRecord.put("room_reserve_length", MapUtils.getDouble(cableRecord, "room_reserve_length", 0.0) + MapUtils.getDouble(cableSegMap, "room_reserve_length", 0.0));
                    cableRecord.put("optitrans_reserve_length", MapUtils.getDouble(cableRecord, "optitrans_reserve_length", 0.0) + MapUtils.getDouble(cableSegMap, "optitrans_reserve_length", 0.0));
                    cableRecord.put("optifiber_reserve_length", MapUtils.getDouble(cableRecord, "optifiber_reserve_length", 0.0) + MapUtils.getDouble(cableSegMap, "optifiber_reserve_length", 0.0));
                    cableRecord.put("jointbox_reserve_length", MapUtils.getDouble(cableRecord, "jointbox_reserve_length", 0.0) + MapUtils.getDouble(cableSegMap, "jointbox_reserve_length", 0.0));
                    cableRecord.put("pole_reserve_length", MapUtils.getDouble(cableRecord, "pole_reserve_length", 0.0) + MapUtils.getDouble(cableSegMap, "pole_reserve_length", 0.0));
                    cableRecord.put("pipe_reserve_length", MapUtils.getDouble(cableRecord, "pipe_reserve_length", 0.0) + MapUtils.getDouble(cableSegMap, "pipe_reserve_length", 0.0));
                    cableRecord.put("rack_reserve_length", MapUtils.getDouble(cableRecord, "rack_reserve_length", 0.0) + MapUtils.getDouble(cableSegMap, "rack_reserve_length", 0.0));
                    cableRecord.put("other_reserve_length", MapUtils.getDouble(cableRecord, "other_reserve_length", 0.0) + MapUtils.getDouble(cableSegMap, "other_reserve_length", 0.0));
                }
            }

            Map<String, Object> result = new HashMap<>();
            result.put("cable", cableRecord);
            result.put("cableSegs", cableSegRecords);
            result.put("cableSections", cableSectionRecords);
            result.put("taskid", taskid);
            result.put("taskParams", taskParamsJson);
            ajaxResult = AjaxResult.success(result);

            //异步保存日志
            AsyncManager.me().execute(AsyncBusiTask.saveApiLog(reqUrl, opUser, ajaxResult, params));
            return ajaxResult;
        }
        catch (Exception ex)
        {
            log.error("接口发生不明异常", ex);
            AjaxResult ajaxResult = AjaxResult.error(StringUtils.format("保存失败-接口发生不明异常:{}", ex.getMessage()));
            AsyncManager.me().execute(AsyncBusiTask.saveApiLog(reqUrl, opUser, ajaxResult, params));
            return ajaxResult;
        }
    }

    @PostMapping(value = "/materials/calc")
    @ApiOperation(value = "计算主材", notes = "土味情话计算主材")
    @ResponseBody
    public AjaxResult calcPrimaryMaterials(@PathVariable("cable_id") String cable_id) {
        String reqUrl = ServletUtils.getRequest().getRequestURI();
        log.info("当前请求地址:" + reqUrl);
        try
        {
            return AjaxResult.success("成功!");
        }
        catch (Exception ex)
        {
            log.error("计算主材发生不明异常", ex);
            return AjaxResult.error("计算主材发生不明异常");
        }
    }
}
