package com.startel.middleware.utils;
import com.alibaba.fastjson.JSON;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.LinkedHashMap;

/**
 * @Author：ranchl
 * @Date：2024/9/4 5:48
 */
public class NrSpeedCalculateUtil {

    /**
     *
     * @param W 带宽（MHz),取值范围 [10, 15, 20, 25, 30, 40, 50, 60, 80, 90, 100]
     * @param SC 子载波带宽（KHz), 取值范围 [15, 30, 60]
     * @param NUM_scs 每RB子载波数量, 5G固定设置值：12
     * @param FR 频段开销指数， 取值： FR1, FR2
     * @param str_f 单双周期， 取值： 单周期， 双周期
     * @param p_slot 周期时间（ms）， 单周期取5，双周期取2.5
     * @param str_slot  特殊子帧时隙配比， 取值： 10:2:2, 6:4:4, 11:1:2
     * @param str_m 调制，取值： 256QAM, 64QAM, 16QAM, QPSK
     * @param v 编码率，取值： 0.9258， 0.87
     * @param strL MIMO配置， 取值： 1T1R， 2T2R, 4T4R
     * @param W0 功率配置(单天线口）（W), 取值： 60
     * @return
     */
    public static java.util.Map<String, Object> calculate(int W, int SC, int NUM_scs, String FR, String str_f, double p_slot, String str_slot,String str_m, double v, String strL, double W0) {
        java.util.Map<String, Object> map = new LinkedHashMap<>();
        int RB = getRBCount(W, SC);
        if(RB > 1) {
            double OAd = 0.14;
            double Num_slot_d = 0.0;
            double OAu = 0.08;
            double Num_slot_u = 0.0;
            double L = 1;
            double M = 8;
            double W1;
            double fd;
            double fx;
            double fu;
            if (FR.equalsIgnoreCase("FR1"))
            {
                OAd = 0.14;
                OAu = 0.08;
            }
            else
            {
                OAd = 0.18;
                OAu = 0.1;
            }

            fd = Integer.valueOf(str_slot.split(":")[0]);
            fx = Integer.valueOf(str_slot.split(":")[1]);
            fu = Integer.valueOf(str_slot.split(":")[2]);

            if (str_f.equalsIgnoreCase("单周期"))
            {
                Num_slot_u = (2 + fu / 14) / 5.0;
                Num_slot_d = (7 + fd / 14) / 5.0;
            }

            if (str_f.equalsIgnoreCase("双周期"))
            {
                Num_slot_u = (3 + 2 * fu / 14) / 5.0;
                Num_slot_d = (5 + 2 * fd / 14) / 5.0;
            }

            if (strL.equalsIgnoreCase("1T1R"))
                L = 1;
            if (strL.equalsIgnoreCase("2T2R"))
                L = 2;
            if (strL.equalsIgnoreCase("4T4R"))
                L = 4;
            if (str_m.equals("256QAM"))
                M = 8;
            if (str_m.equals("64QAM"))
                M = 6;
            if (str_m.equals("16QAM"))
                M = 4;
            if (str_m.equals("QPSK"))
                M = 2;


            W1 = 10 * Math.log10(L * W0 * 1000);

            map.put("RB数量（个）", RB);
            map.put("下行每秒符号数量", Math.ceil(Num_slot_d * 14 * 1000));
            map.put("上行每秒符合数量", Math.ceil(Num_slot_u * 14 * 1000));
            map.put("下行速率(Gbps)", new BigDecimal(RB * NUM_scs * 14 * (1 - OAd) * Num_slot_d * 1000 * M * v * L / Math.pow(10, 9)).setScale(2, RoundingMode.HALF_UP));
            map.put("上行速率(Gbps)", new BigDecimal(RB * NUM_scs * 14 * (1 - OAu) * Num_slot_u * 1000 * M * v * L / Math.pow(10, 9)).setScale(2, RoundingMode.HALF_UP));
            map.put("上下行时隙配比", new BigDecimal(Num_slot_d / (Num_slot_d + Num_slot_u)).setScale(2, RoundingMode.HALF_UP));
            map.put("SS功率", new BigDecimal(W1 - 10 * Math.log10(RB * 12)).setScale(2, RoundingMode.HALF_UP));

            return map;
        }
        else
        {
            throw new RuntimeException("参数不正确!");
        }
    }

    public static int getRBCount(int W, int SC) {
        java.util.Map<String, Integer> map = new HashMap<>();
        map.put("5,15", 25);
        map.put("10,15", 52);
        map.put("15,15", 79);
        map.put("20,15", 106);
        map.put("25,15", 133);
        map.put("30,15", 160);
        map.put("40,15", 216);
        map.put("50,15", 270);

        map.put("5,30", 11);
        map.put("10,30", 24);
        map.put("15,30", 38);
        map.put("20,30", 51);
        map.put("25,30", 65);
        map.put("30,30", 78);
        map.put("40,30", 106);
        map.put("50,30", 133);
        map.put("60,30", 162);
        map.put("80,30", 217);
        map.put("90,30", 245);
        map.put("100,30", 273);

        map.put("10,60", 11);
        map.put("15,60", 18);
        map.put("20,60", 24);
        map.put("25,60", 31);
        map.put("30,60", 38);
        map.put("40,60", 51);
        map.put("50,60", 65);
        map.put("60,60", 79);
        map.put("80,60", 107);
        map.put("90,60", 121);
        map.put("100,60", 135);

        if(SC == 15 && W >= 60)
        {
            return -1;
        }

        if(map.containsKey(W + "," + SC))
        {
            return map.get(W + "," + SC);
        }

        return -1;
    }

    public static void main(String[] args) {
        System.out.println(JSON.toJSONString(calculate(10, 30, 12, "FR2", "单周期", 5, "6:4:4", "256QAM", 0.9258, "4T4R", 60)));


        System.out.println(JSON.toJSONString(calculate(10, 15, 20, "FR1", "单周期", 2, "11:1:2", "256QAM", 0.9258, "1T1R", 20)));

    }
}
