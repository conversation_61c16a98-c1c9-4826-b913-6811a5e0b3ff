package com.startel.middleware.utils;

import java.util.stream.IntStream;

/**
 * @Author：ranchl
 * @Date：2024/11/5 09:58
 */
@Deprecated
public class PhiThetaToAzel {
    public static double[][] phitheta2azel(double[][] phitheta, Boolean... varargin) {
        if (phitheta.length != 2) {
            throw new IllegalArgumentException("Invalid row numbers for PhiTheta. Expected 2 rows.");
        }

        double[] phi = phitheta[0];
        double[] theta = phitheta[1];

        validateAngle(phi, "phitheta2azel", "angle phi", new double[]{0, 360});
        validateAngle(theta, "phitheta2azel", "angle theta", new double[]{0, 180});

        boolean rotateZ2X = (varargin.length > 0) ? varargin[0] : true;

        double[][] azel = new double[2][phi.length];

        if (rotateZ2X) {
            azel = phasedInternalPhiTheta2Azel(phi, theta, new double[]{0, 0});

            double[] az = azel[0];

            for (int i = 0; i < phi.length; i++) {
                if ((phi[i] == 90 || phi[i] == 270) && (theta[i] > 90)) {
                    az[i] = 180;
                }
                if (theta[i] == 180) {
                    az[i] = 180;
                }
                if (theta[i] == 90 && (phi[i] == 90 || phi[i] == 270)) {
                    az[i] = 0;
                }
            }

            azel[0] = az;
        } else {
            azel = phasedInternalPhiTheta2Azel(phi, theta, new double[]{-90, 90});

            double[] az = azel[0];

            for (int i = 0; i < phi.length; i++) {
                if (phi[i] == 180) {
                    az[i] = 180;
                }
                if ((theta[i] == 0 || theta[i] == 180) && (phi[i] <= 180)) {
                    az[i] = phi[i];
                }
                if ((theta[i] == 0 || theta[i] == 180) && (phi[i] > 180)) {
                    az[i] = phi[i] - 360;
                }
            }

            azel[0] = az;
        }

        return azel;
    }

    private static void validateAngle(double[] angle, String functionName, String angleName, double[] range) {
        for (double a : angle) {
            if (a < range[0] || a > range[1]) {
                throw new IllegalArgumentException(functionName + ": " + angleName + " out of range.");
            }
        }
    }

    private static double[][] phasedInternalPhiTheta2Azel(double[] phi, double[] theta, double[] reference) {
        return phitheta2azel(phi, theta, reference);
    }

    public static double[][] phitheta2azel(double[] phi, double[] theta, double[] boresightazel) {
        // Obtain projection values of the direction vector to the U and V directions
        double[][] uv = phitheta2uv(phi, theta);
        double[] uproj = uv[0];
        double[] vproj = uv[1];

        // Obtain the projected value of the direction vector to normal with theta ranges from 0 to 180 degrees
        double[] nproj = new double[theta.length];
        for (int i = 0; i < theta.length; i++) {
            nproj[i] = Math.cos(Math.toRadians(theta[i]));
        }

        // Definition of U direction in Cartesian
        double[][] u0 = {{0}, {1}, {0}}; // default U direction is Y-axis
        double[][] u = rotazel(u0, boresightazel);

        // Definition of V direction in Cartesian
        double[][] v0 = {{0}, {0}, {1}}; // default V direction is Z-axis
        double[][] v = rotazel(v0, boresightazel);

        // Azimuth and elevation of boresight to boresight in Cartesian
        double[][] n = azel2dirvec(new double[][]{{boresightazel[0]}, {boresightazel[1]}});

        // Direction vectors
        double[][] r = new double[3][uproj.length];
        for (int i = 0; i < r.length; i++) {
            for(int j = 0; j < uproj.length; j++) {
                r[i][j] = u[i][j] * uproj[j] + v[i][j] * vproj[j] + n[i][j] * nproj[j];
            }

        }

        // Convert directional vectors to azimuth/elevation angles
        double[][] azel = dirvec2azel(r);

        // Take care of edge cases for azimuth angles
        double[] az = azel[0];
        for (int i = 0; i < az.length; i++) {
            if (az[i] > 180) {
                az[i] = 180;
            } else if (az[i] < -180) {
                az[i] = -180;
            }
        }

        // Take care of edge cases for elevation angles
        double[] el = azel[1];
        for (int i = 0; i < el.length; i++) {
            if (el[i] > 90) {
                el[i] = 90;
            } else if (el[i] < -90) {
                el[i] = -90;
            }
        }

        // Phi and Theta output vector
        return new double[][]{az, el};
    }

    public static double[][] phitheta2uv(double[] phi, double[] theta) {
        // Convert degrees to radians for calculations
        double[] phiRad = new double[phi.length];
        double[] thetaRad = new double[theta.length];
        for(int i = 0; i < phi.length; i++) {
            phiRad[i] = Math.toRadians(phi[i]);
        }
        for(int i = 0; i < theta.length; i++) {
            thetaRad[i] = Math.toRadians(theta[i]);
        }

        // Calculate u and v projections
        double[] uproj = new double[phiRad.length * thetaRad.length];
        double[] vproj = new double[phiRad.length * thetaRad.length];
        int k = 0;
        for(int i = 0; i < phiRad.length; i++) {
            for(int j = 0; j < thetaRad.length; j++) {
                uproj[k++] = Math.sin(thetaRad[j]) * Math.cos(phiRad[i]);
                vproj[k++] = Math.sin(thetaRad[j]) * Math.sin(phiRad[i]);
            }
        }

        // Create the result array
        double[][] uv = {uproj, vproj};

        return uv;
    }

    public static double[][] rotazel(double[][] startaxes, double[] azel) {
        // This function is for internal use only. It may be removed in the future.

        // ROTAZEL  Rotate axes for a given azimuth and elevation pair
        // AXES_NEW = rotazel(AXES_OLD, AZEL) rotates the axes
        // specified in AXES_OLD by a pair of azimuth/elevation angles specified
        // in AZEL (in degrees). AXES_OLD is a 3-row matrix with each column
        // specifying an axis in [x;y;z] form. Note that axis can be interpreted
        // as either a position vector (in meters) or a direction vector.

        // AZEL is a length-2 vector in the form of [azimuth; elevation] where
        // azimuth is within [-180 180] and elevation is within [-90 90]. AXES_NEW
        // has the same dimension as AXES_OLD, with each column representing the
        // rotated axis.

        // Example:
        // Rotate x axis by 90 degrees azimuth.
        // double[][] axis_new = rotazel(new double[][]{{1}, {0}, {0}}, new double[]{90, 0});

        double[][] rotaxes = multiplyMatrices(rotationMatrixForZ(azel[0]),
                multiplyMatrices(rotationMatrixForY(azel[1]), startaxes));

        return rotaxes;
    }

    private static double[][] rotationMatrixForZ(double gamma) {
        // rotate in the direction of x->y
        double u = Math.cos(Math.toRadians(gamma));
        double v = Math.sin(Math.toRadians(gamma));
        return new double[][]{{u, -v, 0}, {v, u, 0}, {0, 0, 1}};
    }

    private static double[][] rotationMatrixForY(double beta) {
        // rotate in the direction of x->z
        double u = Math.cos(Math.toRadians(beta));
        double w = Math.sin(Math.toRadians(beta));
        return new double[][]{{u, 0, -w}, {0, 1, 0}, {w, 0, u}};
    }

    private static double[][] multiplyMatrices(double[][] a, double[][] b) {
        int aRows = a.length;
        int aCols = a[0].length;
        int bCols = b[0].length;
        double[][] result = new double[aRows][bCols];

        for (int i = 0; i < aRows; i++) {
            for (int j = 0; j < bCols; j++) {
                for (int k = 0; k < aCols; k++) {
                    result[i][j] += a[i][k] * b[k][j];
                }
            }
        }
        return result;
    }

    public static double[][] azel2dirvec(double[][] azel) {
        // Convert degrees to radians
        for (int i = 0; i < azel.length; i++) {
            azel[i][0] = Math.toRadians(azel[i][0]);
            azel[i][1] = Math.toRadians(azel[i][1]);
        }

        double[][] dirvec = new double[3][azel[0].length];
        for (int j = 0; j < azel[0].length; j++) {
            dirvec[0][j] = Math.cos(azel[1][j]) * Math.cos(azel[0][j]);
            dirvec[1][j] = Math.cos(azel[1][j]) * Math.sin(azel[0][j]);
            dirvec[2][j] = Math.sin(azel[1][j]);
        }
        return dirvec;
    }

    public static double[][] dirvec2azel(double[][] dirvec) {
        int numCols = dirvec[0].length;
        double[][] azel = new double[2][numCols];

        boolean[] nzidx = new boolean[numCols];
        for (int i = 0; i < numCols; i++) {
            nzidx[i] = dirvec[0][i] != 0;
        }

        int[] nzidxTrue = IntStream.range(0, dirvec[0].length).filter(index -> nzidx[index]).toArray();
        double[] x = new double[nzidxTrue.length];
        double[] y = new double[nzidxTrue.length];
        double[] z = new double[nzidxTrue.length];
        for(int i = 0; i < nzidxTrue.length; i++) {
            x[i] = dirvec[0][i];
            y[i] = dirvec[1][i];
            z[i] = dirvec[2][i];
        }

        double[] th = new double[x.length];
        double[] phi = new double[y.length];
        for (int i = 0; i < x.length; i++) {
            double r = Math.sqrt(x[i] * x[i] + y[i] * y[i] + z[i] * z[i]);
            th[i] = Math.atan2(y[i], x[i]);
            phi[i] = Math.asin(z[i] / r);
        }

        for (int i = 0; i < th.length; i++) {
            azel[0][i] = th[i];
            azel[1][i] = phi[i];
        }

        double[] temp = new double[numCols];
        for (int i = 0; i < numCols; i++) {
            if (!nzidx[i]) {
                temp[i] = dirvec[2][i];
                azel[1][i] = Math.asin(Math.max(-1, Math.min(temp[i], 1)));  // guard valid sin value
                temp[i] = dirvec[1][i] / Math.cos(azel[1][i]);
                azel[0][i] = Math.asin(Math.max(-1, Math.min(temp[i], 1)));  // guard valid sin value
            }
        }

        azel = rad2deg(azel);
        return azel;
    }

    private static double[][] rad2deg(double[][] radians) {
        double[][] degrees = new double[radians.length][radians[0].length];
        for (int i = 0; i < radians.length; i++) {
            for (int j = 0; j < radians[i].length; j++) {
                degrees[i][j] = Math.toDegrees(radians[i][j]);
            }
        }
        return degrees;
    }

    public static void main(String[] args) {
        phitheta2azel(new double[][] {{30}, {10}});
    }
}
