package com.startel.middleware.utils;

import cn.hutool.core.img.ColorUtil;
import cn.hutool.core.io.FileUtil;
import com.startel.mesh.MeshBuilder;
import com.startel.mesh.MeshGltfWriter;
import com.startel.mesh.MeshVertex;
import de.javagl.jgltf.impl.v2.Material;
import de.javagl.jgltf.impl.v2.MaterialPbrMetallicRoughness;
import org.apache.commons.math3.util.MathArrays;

import javax.vecmath.Point3f;
import java.awt.*;
import java.io.File;
import java.nio.file.Paths;
import java.util.*;
import java.util.List;

/**
 * @Author：ranchl
 * @Date：2024/11/7 18:01
 */
public class AntennaPatternReader {
    public static Map<String, Object> read(String filePath) {
        Map<String, Object> map = new LinkedHashMap<>();
        List<String> lines = FileUtil.readLines(filePath, "UTF-8");
        int phiStartIndex = lines.indexOf("Horizontal Pattern:");
        int thetaStartIndex = lines.indexOf("Vertical Pattern:");
        Map<String, Object> result = new LinkedHashMap<>();
        Map<String, Object> props = new LinkedHashMap<>();
        for(int i = 0; i < phiStartIndex; i++) {
            String line = lines.get(i);
            if(line.split(":").length == 2) {
                props.put(line.split(":")[0], line.split(":")[1]);
            }
            else if(line.split(":").length == 1) {
                props.put(line.split(":")[0], "");
            }
        }
        result.put("properties", props);

        List<Double> listPhiAngles = new ArrayList<>();
        List<Double> listPhiPowers = new ArrayList<>();
        for(int i = phiStartIndex + 1; i < thetaStartIndex - 1; i++) {
            String line = lines.get(i);
            Double angle = Double.valueOf(line.split(" ")[0]);
            Double power = Double.valueOf(line.split(" ")[1]);
            listPhiAngles.add(angle);
            listPhiPowers.add(power);
        }
        result.put("phiAngles", listPhiAngles);
        result.put("phiPowers", listPhiPowers);

        List<Double> listThetaAngles = new ArrayList<>();
        List<Double> listThetaPowers = new ArrayList<>();
        for(int i = thetaStartIndex + 1; i < thetaStartIndex + 1 + 360; i++) {
            String line = lines.get(i);
            Double angle = Double.valueOf(line.split(" ")[0]);
            Double power = Double.valueOf(line.split(" ")[1]);
            listThetaAngles.add(angle);
            listThetaPowers.add(power);
        }
        result.put("thetaAngles", listThetaAngles);
        result.put("thetaPowers", listThetaPowers);

        return result;
    }

    public static void main(String[] args) throws Exception {
        Map<String, Object> result = read("/Users/<USER>/Downloads/Antenna Pattern Samples/Antenna Pattern Samples/06_AntPat (ana)/SP304X-SF1P65LDFch2_0425.ana");
        List<Double> listPhiAngles = (List<Double>)result.get("phiAngles");
        List<Double> listPhiPowers = (List<Double>)result.get("phiPowers");

        List<Double> listThetaAngles =(List<Double>)result.get("thetaAngles");
        List<Double> listThetaPowers = (List<Double>)result.get("thetaPowers");

        double[] phi = listPhiAngles.stream().mapToDouble(Double::doubleValue).toArray();
        double[] theta = listThetaAngles.stream().mapToDouble(Double::doubleValue).toArray();
        double[] phiGains = listPhiPowers.stream().mapToDouble(Double::doubleValue).toArray();
        double[] thetaGains = listThetaPowers.stream().mapToDouble(Double::doubleValue).toArray();

        double[][] power = new double[listPhiPowers.size()][listThetaPowers.size()];

        for (int i = 0; i < listPhiPowers.size(); i++) {
            for (int j = 0; j < listThetaPowers.size(); j++) {
                double phiPower = listPhiPowers.get(i);
                double thetaPower = listThetaPowers.get(j);

                double phiDb = 20 * Math.log10(Math.abs(phiPower));
                double thetaDb = 20 * Math.log10(Math.abs(thetaPower));

                double lerpGain = GainInterpolationUtil.interpolateGain(theta, thetaGains, phi, phiGains, listThetaAngles.get(j), listPhiAngles.get(i));
                double powerGain = Math.pow(10, lerpGain / 20);
                // 参考：https://ww2.mathworks.cn/help/antenna/ref/patternfromslices.html
                power[i][j] = powerGain;
            }
        }

        // Converting from spherical to cartesian coords
        List<Double> THETA = new ArrayList<>();
        List<Double> PHI = new ArrayList<>();
        List<Double> R = new ArrayList<>();

        for (int p = 0; p < phi.length; p++) {
            PHI.add(Math.toRadians(phi[p]));
        }
        for (int q = 0; q < theta.length; q++) {
            THETA.add(Math.toRadians(theta[q]));
        }

        for (int p = 0; p < phi.length; p++) {
            for (int q = 0; q < theta.length; q++) {
                R.add(power[p][q]);
            }
        }


        List<Double> X = new ArrayList<>();
        List<Double> Y = new ArrayList<>();
        List<Double> Z = new ArrayList<>();

        for(int i = 0; i < PHI.size(); i++) {
            for(int j = 0; j < THETA.size(); j++) {
                Double r = power[i][j];
                if(r.equals(Double.NaN))
                {
                    continue;
                }
                Double phiRadian = PHI.get(i);
                Double thetaRadian = THETA.get(j);
                X.add(r * Math.sin(thetaRadian) * Math.cos(phiRadian));
                Y.add(r * Math.sin(thetaRadian) * Math.sin(phiRadian));
                Z.add(r * Math.cos(thetaRadian));
            }

        }

        double minZ = Z.stream().min(Double::compare).get();
        double maxZ = Z.stream().max(Double::compare).get();

        MeshGltfWriter meshGltfWriter = new MeshGltfWriter();
        final MeshBuilder meshBuilder = new MeshBuilder("antenna-radiation-pattern");

        // grid to hold mesh points
        MeshVertex[][] meshGrid = new MeshVertex[361][181];
        String[] colors = new String[] {
                "#402AB4",
                "#4536D4",
                "#4752F4",
                "#4659F8",
                "#2F85F8",
                "#2795EC",
                "#17ABDD",
                "#0BB3D2",
                "#04B8C8",
                "#11BDB7",
                "#44CA89",
                "#73CC62",
                "#ADC637",
                "#DDBC28",
                "#FFC239",
                "#F6EF1F",
                "#FFB303",
                "#FF4700",
                "#C40001",
                "#8B0000"
        };
        colors = new String[] {
                "#001F98",
                "#003AFF",
                "#0173FF",
                "#48FFB8",
                "#FFF004",
                "#FF1500",
                "#A70001"
        };
        for(int i = 0; i < 361; i++) {
            for (int j = 0; j < 181; j++) {
                int idx = i * 181 + j;
                Double x = X.get(idx);
                Double y = Y.get(idx);
                Double z = Z.get(idx);

                if(x.equals(Double.NaN) || y.equals(Double.NaN) || z.equals(Double.NaN) ||  Double.isInfinite(x) ||  Double.isInfinite(y) ||  Double.isInfinite(z)) {
                    continue;
                }

                Point3f point = new Point3f(x.floatValue(), y.floatValue(), z.floatValue());
                MeshVertex meshVertex = meshBuilder.newVertex(point);

                // 对颜色值插值
                Integer colorIdxTopZ = (int) Math.ceil(Math.abs(((z - minZ) / (maxZ - minZ)) * colors.length));
                Integer colorIdxBottomZ = (int) Math.floor(Math.abs(((z - minZ) / (maxZ - minZ)) * colors.length));
                if (colorIdxTopZ == -1) colorIdxTopZ = 0;
                if (colorIdxTopZ == colors.length) colorIdxTopZ = colorIdxTopZ - 1;
                if (colorIdxBottomZ == -1) colorIdxBottomZ = 0;
                if (colorIdxBottomZ == colors.length) colorIdxBottomZ = colorIdxBottomZ - 1;

                if (colorIdxBottomZ.equals(colorIdxTopZ)) {
                    String hexColor = colors[colorIdxTopZ];
                    Color _color = ColorUtil.hexToColor(hexColor);
                    meshVertex.setColor(_color);
                } else {
                    double middleZ = Math.abs((z - minZ) / (maxZ - minZ)) * colors.length;
                    double factorZ = middleZ - colorIdxBottomZ;
                    Color fromColor = ColorUtil.hexToColor(colors[colorIdxBottomZ]);
                    Color toColor = ColorUtil.hexToColor(colors[colorIdxTopZ]);
                    Color color = new Color(
                            fromColor.getRed() + (int) ((toColor.getRed() - fromColor.getRed()) * factorZ),
                            fromColor.getGreen() + (int) ((toColor.getGreen() - fromColor.getGreen()) * factorZ),
                            fromColor.getBlue() + (int) ((toColor.getBlue() - fromColor.getBlue()) * factorZ),
                            255
                    );
                    meshVertex.setColor(color);
                }

                meshGrid[i][j] = meshVertex;
            }
        }

        String extension = "KHR_materials_unlit";
        Material material = meshGltfWriter.newDefaultMaterial();
        MaterialPbrMetallicRoughness materialPbrMetallicRoughness = new MaterialPbrMetallicRoughness();
        materialPbrMetallicRoughness.setBaseColorFactor(new float[]{ 1, 1, 1, 0.6f});
        materialPbrMetallicRoughness.setMetallicFactor(0.1f);
        materialPbrMetallicRoughness.setRoughnessFactor(0.5f);
        material.setPbrMetallicRoughness(materialPbrMetallicRoughness);
        material.addExtensions(extension, new HashMap<>());
        material.setAlphaMode("BLEND");
        material.setDoubleSided(true);
        meshBuilder.setMaterial(material);

        // render the vertices in the grid
        meshBuilder.addGrid(meshGrid, false, false, false);

        // generate gltf buffers
        meshBuilder.build(meshGltfWriter);

        meshGltfWriter.getGltf().addExtensionsUsed(extension);
        meshGltfWriter.getGltf().addExtensionsRequired(extension);
        meshGltfWriter.setCopyright("All Right Reserved ©️ StarTel Ltd.");
        meshGltfWriter.setMetaParam("author", "StarTel Ltd.");
        meshGltfWriter.setMetaParam("contact", "18302534286");
        meshGltfWriter.setMetaParam("properties", result.get("properties"));

        File _outFile = Paths.get("/Users/<USER>/Downloads",   "test-output.gltf").toFile();
        _outFile.getParentFile().mkdirs();
        meshGltfWriter.writeGltf(_outFile);
    }
}
