package com.startel.middleware.utils;

/**
 * @Author：ranchl
 * @Date：2024/11/2 02:55
 */
public class Magnitude2dB {
    public static double[] mag2db(double[] y) {
        if (y == null || y.length == 0) {
            return new double[0];
        }

        double[] ydb = new double[y.length];
        for (int i = 0; i < y.length; i++) {
            if (y[i] < 0) {
                ydb[i] = Double.NaN;
            } else {
                ydb[i] = 20 * Math.log10(y[i]);
            }
        }
        return ydb;
    }

    public static double[] db2mag(double[] ydb) {
        double[] y = new double[ydb.length];
        for (int i = 0; i < ydb.length; i++) {
            y[i] = Math.pow(10, ydb[i] / 20);
        }
        return y;
    }

    public static double mag2dbScalar(double y) {
        if (y < 0) {
            return Double.NaN;
        } else {
            return 20 * Math.log10(y);
        }
    }
}
