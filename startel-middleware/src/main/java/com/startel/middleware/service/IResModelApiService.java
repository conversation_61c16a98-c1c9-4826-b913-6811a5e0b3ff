package com.startel.middleware.service;

import com.startel.common.core.domain.AjaxResult;
import com.startel.common.core.page.TableDataInfo;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

public interface IResModelApiService {
    AjaxResult clearResmodelCache(Map<String, Object> params) throws Exception;
    AjaxResult queryResmodelMeta(Map<String, Object> params) throws Exception;
    AjaxResult queryResmodelRowDetail(Map<String, Object> params) throws Exception;
    AjaxResult initResmodelRowDetail(Map<String, Object> params) throws Exception;
    AjaxResult saveResmodelRowDetail(Map<String, Object> params) throws Exception;
    AjaxResult deleteResmodelRowDetail(Map<String, Object> params) throws Exception;
    TableDataInfo queryResmodelData(Map<String, Object> params) throws Exception;
    AjaxResult copyTaskResources(Map<String, Object> params) throws Exception;
}
