package com.startel.middleware.service.impl;

import cn.hutool.core.lang.UUID;
import cn.hutool.core.map.CaseInsensitiveLinkedMap;
import cn.hutool.json.JSONUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.startel.common.core.domain.AjaxResult;
import com.startel.common.core.page.PageDomain;
import com.startel.common.core.page.TableDataInfo;
import com.startel.common.utils.StringUtils;
import com.startel.common.utils.sql.SqlUtil;
import com.startel.middleware.mapper.BaseResFormMapper;
import com.startel.middleware.model.ModelTreeNode;
import com.startel.middleware.service.IResFormService;
import com.startel.middleware.service.IResModelAttrService;
import com.startel.middleware.service.IResModelManageService;
import com.startel.middleware.service.IResModelService;
import com.startel.middleware.utils.ParamCheckUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@Slf4j
public class ResModelManageServiceImpl implements IResModelManageService {
    @Autowired
    IResModelService middlewareResModelService;
    @Autowired
    IResModelAttrService middlewareRmaService;
    @Autowired
    IResFormService resFormService;

    @Override
    public TableDataInfo queryResModels(Map<String, Object> params) {
        TableDataInfo tableDataInfo = new TableDataInfo();
        PageDomain pageDomain = new PageDomain();
        pageDomain.setPageNum(MapUtils.getInteger(params, "pageNum", 1));
        pageDomain.setPageSize(MapUtils.getInteger(params, "pageSize", Integer.MAX_VALUE));
        pageDomain.setOrderByColumn(MapUtils.getString(params, "orderByColumn", ""));
        pageDomain.setIsAsc(MapUtils.getString(params, "orderBySort", ""));
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        if (StringUtils.isNotNull(pageNum) && StringUtils.isNotNull(pageSize)) {
            String orderBy = SqlUtil.escapeOrderBySql(pageDomain.getOrderBy());
            PageHelper.startPage(pageNum, pageSize, orderBy);
        }
        List list = middlewareResModelService.selectRowList(params);
        tableDataInfo.setCode(0);
        tableDataInfo.setRows(list);
        tableDataInfo.setTotal(new PageInfo(list).getTotal());
        return tableDataInfo;
    }

    @Override
    public AjaxResult queryResModelTrees(Map<String, Object> params) {
        PageDomain pageDomain = new PageDomain();
        pageDomain.setPageNum(MapUtils.getInteger(params, "pageNum", 1));
        pageDomain.setPageSize(MapUtils.getInteger(params, "pageSize", Integer.MAX_VALUE));
        pageDomain.setOrderByColumn(MapUtils.getString(params, "orderByColumn", "sort_order"));
        pageDomain.setIsAsc(MapUtils.getString(params, "orderBySort", "asc"));
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        List<String> listGroups = middlewareResModelService.selectGroupNameList(params);

        if (StringUtils.isNotNull(pageNum) && StringUtils.isNotNull(pageSize)) {
            String orderBy = SqlUtil.escapeOrderBySql(pageDomain.getOrderBy());
            PageHelper.startPage(pageNum, pageSize, orderBy);
        }
        List<Map> listModels = middlewareResModelService.selectRowList(params);
        Map<String, List<ModelTreeNode>> nodesMap = new LinkedHashMap<>();
        for(Map record : listModels)
        {
            ModelTreeNode node = new ModelTreeNode();
            node.setId(MapUtils.getString(record, "id"));
            node.setName(MapUtils.getString(record, "name"));
            node.setText(MapUtils.getString(record, "ch_name") + "(" + MapUtils.getString(record, "name") + ")");
            node.setImageUrl("img/res-icons/res-model.png");
            node.setNode_type(MapUtils.getString(record, "model"));
            node.setSort_order(MapUtils.getInteger(record, "sort_order", listModels.indexOf(record)));
            node.setItems(null);
            node.setNodeData(record);
            String groupName = MapUtils.getString(record, "group_name", "未知分组");
            if(nodesMap.containsKey(groupName))
            {
                nodesMap.get(groupName).add(node);
            }
            else
            {
                List<ModelTreeNode> listNodes = new ArrayList<>();
                listNodes.add(node);
                nodesMap.put(groupName, listNodes);
            }
        }

        if(!listGroups.contains("未知分组"))
        {
            listGroups.add("未知分组");
        }

        ModelTreeNode rootNode = new ModelTreeNode();
        rootNode.setId("root");
        rootNode.setName("root");
        rootNode.setText("模型树");
        rootNode.setImageUrl("img/res-icons/res-catagory.png");
        rootNode.setNode_type("root");
        rootNode.setSort_order(0);
        rootNode.setItems(new ArrayList<>());

        List<ModelTreeNode> listGroupNodes = new ArrayList<>();
        for(String groupName : listGroups)
        {
            ModelTreeNode node = new ModelTreeNode();
            node.setId(groupName);
            node.setName(groupName);
            node.setText(groupName);
            node.setImageUrl("img/res-icons/res-catagory.png");
            node.setNode_type("group");
            node.setSort_order(listGroups.indexOf(groupName));
            List<ModelTreeNode> listNodes = new ArrayList<>();
            if(nodesMap.containsKey(groupName)){
                listNodes = nodesMap.get(groupName);
            }
            node.setItems(listNodes);
            listGroupNodes.add(node);
        }
        rootNode.setItems(listGroupNodes);

        List<ModelTreeNode> listTreeNodes = new ArrayList<>();
        listTreeNodes.add(rootNode);

        return AjaxResult.success("查询成功", listTreeNodes);
    }

    @Override
    public AjaxResult detailResModel(Map<String, Object> params) {
        if(params.isEmpty())
        {
            return AjaxResult.error("参数核查不通过: 参数不允许为空。");
        }
        List<Map> list = middlewareResModelService.selectRowList(params);
        if(list.size() == 0)
        {
            return AjaxResult.error("未查找到对应的模型。");
        }
        Map modelRow = list.get(0);
        String id = MapUtils.getString(modelRow, "id");
        List<Map> listAttr = middlewareRmaService.selectModelAttrList(id);
        Map modelDetail = new CaseInsensitiveLinkedMap();
        modelDetail.put("model", modelRow);
        modelDetail.put("attrs", listAttr);
        return AjaxResult.success("查询成功", modelDetail);
    }

    @Override
    public AjaxResult saveResModel(Map<String, Object> params) {
        if(params.isEmpty())
        {
            return AjaxResult.error("参数核查不通过: 参数不允许为空。");
        }
        params.put("create_time", new Date());
        params.put("update_time", new Date());
        if(StringUtils.isNotEmpty(MapUtils.getString(params, "id", "")))
        {
            log.info("saveResModel处理后的入参：{}", JSONUtil.parse(params).toString());
            String id = MapUtils.getString(params, "id", "");
            Map record = middlewareResModelService.selectRowById(id);
            if(record != null)
            {
                middlewareResModelService.updateRow(params);
            }
            else
            {
                String check = ParamCheckUtil.checkMustFldn(params, "name,ch_name,attr_table,group_name");
                if(StringUtils.isNotEmpty(check))
                {
                    return AjaxResult.error(check);
                }
                String name = MapUtils.getString(params, "name");
                Map row = middlewareResModelService.selectRowByName(name);
                if(row != null && !row.isEmpty())
                {
                    return AjaxResult.error("该模型名称已经存在!");
                }
                middlewareResModelService.insertRow(params);
            }
        }
        else
        {
            String check = ParamCheckUtil.checkMustFldn(params, "name,ch_name,attr_table,group_name");
            if(StringUtils.isNotEmpty(check))
            {
                return AjaxResult.error(check);
            }
            String name = MapUtils.getString(params, "name");
            Map row = middlewareResModelService.selectRowByName(name);
            if(row != null && !row.isEmpty())
            {
                return AjaxResult.error("该模型名称已经存在!");
            }
            params.put("id", UUID.randomUUID().toString());
            log.info("saveResModel处理后的入参：{}", JSONUtil.parse(params).toString());
            middlewareResModelService.insertRow(params);
        }

        return AjaxResult.success("模型保存成功");
    }

    @Override
    public AjaxResult deleteResModel(Map<String, Object> params) {
        if(params.isEmpty())
        {
            return AjaxResult.error("参数核查不通过: 参数不允许为空。");
        }
        params.put("create_time", new Date());
        params.put("update_time", new Date());
        log.info("deleteResModel处理后的入参：{}", JSONUtil.parse(params).toString());
        String check = ParamCheckUtil.checkMustFldn(params, "id");
        if(StringUtils.isNotEmpty(check))
        {
            return AjaxResult.error(check);
        }

        String id = MapUtils.getString(params, "id", "");
        middlewareResModelService.deleteRowLogic(id);

        return AjaxResult.success("删除模型成功");
    }

    @Override
    public AjaxResult saveResModelAttr(Map<String, Object> params) {
        if(params.isEmpty())
        {
            return AjaxResult.error("参数核查不通过: 参数不允许为空。");
        }
        if(StringUtils.isNotEmpty(MapUtils.getString(params, "id", "")))
        {
            String id = MapUtils.getString(params, "id", "");
            Map record = middlewareRmaService.selectRowById(id);
            if(record != null)
            {
                middlewareRmaService.updateRow(params);
            }
            else
            {
                String check = ParamCheckUtil.checkMustFldn(params, "res_model_id,attr_name,attr_label,attr_type,field_name,field_type,allow_null,translate_type,usage_type,status");
                if(StringUtils.isNotEmpty(check))
                {
                    return AjaxResult.error(check);
                }
                String res_model_id = MapUtils.getString(params, "res_model_id");
                if(middlewareResModelService.selectRowById(res_model_id) == null)
                {
                    return AjaxResult.error("根据模型ID为找到模型。");
                }
                middlewareRmaService.insertRow(params);
            }
        }
        else
        {
            String check = ParamCheckUtil.checkMustFldn(params, "res_model_id,attr_name,attr_label,attr_type,field_name,field_type,allow_null,translate_type,usage_type,status");
            if(StringUtils.isNotEmpty(check))
            {
                return AjaxResult.error(check);
            }
            String res_model_id = MapUtils.getString(params, "res_model_id");
            if(middlewareResModelService.selectRowById(res_model_id) == null)
            {
                return AjaxResult.error("根据模型ID为找到模型。");
            }

            params.put("id", UUID.randomUUID().toString());
            log.info("saveResModelAttr处理后的入参：{}", JSONUtil.parse(params).toString());
            middlewareRmaService.insertRow(params);
        }

        return AjaxResult.success("模型保存成功");
    }

    @Override
    public AjaxResult saveResModelAttrBatch(Map<String, Object> params) {
        if(params.isEmpty())
        {
            return AjaxResult.error("参数核查不通过: 参数不允许为空。");
        }
        if(!params.containsKey("records") || params.get("records") == null || !(params.get("records") instanceof Collection))
        {
            return AjaxResult.error("参数核查不通过: 请传递records列表。");
        }
        Collection list = (Collection) params.get("records");
        //首先校验
        for(Object row : list)
        {
            Map<String, Object> rowMap = (Map<String, Object>)row;
            if(StringUtils.isNotEmpty(MapUtils.getString(rowMap, "id", "")))
            {
                String id = MapUtils.getString(rowMap, "id", "");
                Map record = middlewareRmaService.selectRowById(id);
                if(record == null)
                {
                    String check = ParamCheckUtil.checkMustFldn(rowMap, "res_model_id,attr_name,attr_label,attr_type,field_name,field_type,allow_null,translate_type,usage_type,status");
                    if(StringUtils.isNotEmpty(check))
                    {
                        return AjaxResult.error(check);
                    }
                    String res_model_id = MapUtils.getString(rowMap, "res_model_id");
                    if(middlewareResModelService.selectRowById(res_model_id) == null)
                    {
                        return AjaxResult.error("根据模型ID未找到模型。");
                    }
                }
            }
            else
            {
                String check = ParamCheckUtil.checkMustFldn(rowMap, "res_model_id,attr_name,attr_label,attr_type,field_name,field_type,allow_null,translate_type,usage_type,status");
                if(StringUtils.isNotEmpty(check))
                {
                    return AjaxResult.error(check);
                }
                String res_model_id = MapUtils.getString(rowMap, "res_model_id");
                if(middlewareResModelService.selectRowById(res_model_id) == null)
                {
                    return AjaxResult.error("根据模型ID未找到模型。");
                }
            }
        }

        //校验通过后，逐条执行更新或插入
        for(Object row : list)
        {
            Map<String, Object> rowMap = (Map<String, Object>)row;
            if(StringUtils.isNotEmpty(MapUtils.getString(rowMap, "id", "")))
            {
                String id = MapUtils.getString(rowMap, "id", "");
                Map record = middlewareRmaService.selectRowById(id);
                if(record != null)
                {
                    middlewareRmaService.updateRow(rowMap);
                }
                else
                {
                    middlewareRmaService.insertRow(rowMap);
                }
            }
            else
            {
                rowMap.put("id", UUID.randomUUID().toString());
                log.info("saveResModelAttrBatch处理后的入参：{}", JSONUtil.parse(rowMap).toString());
                middlewareRmaService.insertRow(rowMap);
            }
        }

        return AjaxResult.success("模型保存成功");
    }

    @Override
    public AjaxResult deleteResModelAttr(Map<String, Object> params) {
        if(params.isEmpty())
        {
            return AjaxResult.error("参数核查不通过: 参数不允许为空。");
        }
        String check = ParamCheckUtil.checkMustFldn(params, "id");
        if(StringUtils.isNotEmpty(check))
        {
            return AjaxResult.error(check);
        }

        if(params.get("id") instanceof Collection)
        {
            middlewareRmaService.deleteRowBatchLogic(params);
        }
        else if(MapUtils.getString(params, "id").contains(","))
        {
            params.put("id", MapUtils.getString(params, "id").split(","));
            middlewareRmaService.deleteRowBatchLogic(params);
        }
        else
        {
            String id = MapUtils.getString(params, "id", "");
            middlewareRmaService.deleteRowLogic(id);
        }

        return AjaxResult.success("删除模型属性成功");
    }

    @Override
    public AjaxResult queryResModelForm(Map<String, Object> params) {
        if(params.isEmpty())
        {
            return AjaxResult.error("参数核查不通过: 参数不允许为空。");
        }
        String check = ParamCheckUtil.checkMustFldn(params, "model_name");
        if(StringUtils.isNotEmpty(check))
        {
            return AjaxResult.error(check);
        }
        List<Map> list = resFormService.selectRowList(params);
        return AjaxResult.success("查询成功", list);
    }

    @Override
    public AjaxResult saveResModelForm(Map<String, Object> params)
    {
        if(params.isEmpty())
        {
            return AjaxResult.error("参数核查不通过: 参数不允许为空。");
        }
        String check = ParamCheckUtil.checkMustFldn(params, "model_name,form_template_domid,form_template_title,form_template,default_attributes");
        if(StringUtils.isNotEmpty(check))
        {
            return AjaxResult.error(check);
        }
        params.put("op_time", new Date());
        if(StringUtils.isNotEmpty(MapUtils.getString(params, "form_id", "")))
        {
            String form_id = MapUtils.getString(params, "form_id", "");
            Map record = resFormService.selectRowById(form_id);
            if(record != null)
            {
                resFormService.updateRow(params);
            }
            else
            {
                resFormService.insertRow(params);
            }
        }
        else
        {
            params.put("form_id", "form-" + UUID.randomUUID().toString());
            resFormService.insertRow(params);
        }
        return AjaxResult.success("模型保存成功");
    }

    @Override
    public AjaxResult deleteResModelForm(Map<String, Object> params)
    {
        if(params.isEmpty())
        {
            return AjaxResult.error("参数核查不通过: 参数不允许为空。");
        }
        String check = ParamCheckUtil.checkMustFldn(params, "form_id");
        if(StringUtils.isNotEmpty(check))
        {
            return AjaxResult.error(check);
        }

        if(params.get("form_id") instanceof Collection)
        {
            resFormService.deleteRowByIds((String[])((Collection<String>)params.get("form_id")).toArray());
        }
        else if(MapUtils.getString(params, "form_id").contains(","))
        {
            String form_id = MapUtils.getString(params, "form_id", "");
            resFormService.deleteRowByIds(form_id.split(","));
        }
        else
        {
            String form_id = MapUtils.getString(params, "form_id", "");
            resFormService.deleteRowById(form_id);
        }
        return AjaxResult.success("删除模型表单成功");
    }
}
