package com.startel.middleware.service.impl;

import cn.hutool.json.JSONUtil;
import com.esri.core.geometry.*;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.startel.common.core.domain.AjaxResult;
import com.startel.common.core.page.PageDomain;
import com.startel.common.core.page.TableDataInfo;
import com.startel.common.utils.DateUtils;
import com.startel.common.utils.StringUtils;
import com.startel.common.utils.sql.SqlUtil;
import com.startel.middleware.cache.ResModelCacheProvider;
import com.startel.middleware.cache.TranslateCacheProvider;
import com.startel.middleware.service.*;
import com.startel.middleware.utils.ParamCheckUtil;
import com.startel.middleware.utils.SqlEscapeUtil;
import com.startel.middleware.utils.TextUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
@Slf4j
public class ResModelApiServiceImpl implements IResModelApiService {
    @Autowired
    ICommonTableOperate commonTableOperate;
    @Autowired
    JdbcTemplate jdbcTemplate;
    @Autowired
    IResModelRelationsService resModelRelationsService;
    @Autowired
    IResModelService resModelService;
    @Autowired
    IResModelAttrService resModelAttrService;

    public AjaxResult clearResmodelCache(Map<String, Object> params) throws Exception {
        log.info("参数: {}", JSONUtil.parse(params).toString());
        String modelName = MapUtils.getString(params,"model_name");
        if(TextUtil.isNotNull(modelName))
        {
            Map modelDetail = ResModelCacheProvider.getCachedBaseModel(modelName);
            if(modelDetail == null || modelDetail.isEmpty())
            {
                return AjaxResult.error("模型名称[" + modelName + "]不存在");
            }

            ResModelCacheProvider.removeBaseModelCache(modelName);
            TranslateCacheProvider.clearResmodelCache(modelName);

            modelDetail = ResModelCacheProvider.getCachedBaseModel(modelName);
            return AjaxResult.success("清理成功", modelDetail);
        }
        else
        {
            ResModelCacheProvider.clearBaseModelCache();
            TranslateCacheProvider.clearAllCache();

            return AjaxResult.success("清理成功");
        }
    }

    public AjaxResult queryResmodelMeta(Map<String, Object> params) throws Exception {
        String check = ParamCheckUtil.checkMustFldn(params, "model_name");
        if(StringUtils.isNotEmpty(check))
        {
            return AjaxResult.error(check);
        }
        log.info("查询参数: {}", JSONUtil.parse(params).toString());
        String modelName = MapUtils.getString(params,"model_name");
        Map modelDetail = ResModelCacheProvider.getCachedBaseModel(modelName);
        if(modelDetail == null || modelDetail.isEmpty())
        {
            return AjaxResult.error("模型名称[" + modelName + "]不存在");
        }
        return AjaxResult.success("查询成功", modelDetail);
    }

    public AjaxResult queryResmodelRowDetail(Map<String, Object> params) throws Exception {
        String check = ParamCheckUtil.checkMustFldn(params, "model_name,resid");
        if(StringUtils.isNotEmpty(check))
        {
            return AjaxResult.error(check);
        }
        log.info("查询参数: {}", JSONUtil.parse(params).toString());
        String modelName = MapUtils.getString(params,"model_name");
        String resid = MapUtils.getString(params, "resid");
        Map modelDetail = ResModelCacheProvider.getCachedBaseModel(modelName);
        if(modelDetail == null || modelDetail.isEmpty())
        {
            return AjaxResult.error("模型名称[" + modelName + "]不存在");
        }
        Map modelInfo = (Map<String, Object>)modelDetail.get("model");
        List<Map<String, Object>> attrs = (List<Map<String, Object>>) modelDetail.get("attrs");
        String tableName = MapUtils.getString(modelInfo, "attr_table");

        //查找资源表主键
        Map physicColumnsInfo = ResModelCacheProvider.getModelPhysicColumns(modelName);
        String pk_fldn = MapUtils.getString(physicColumnsInfo, "attr_pk_fldn");
        List<Map> listColsInfo = (List<Map>)physicColumnsInfo.get("attr_columns");

        Map<String, Object> parameters = new HashMap<>();
        parameters.put("table_name", tableName);
        parameters.put("conditions", pk_fldn + "='" + resid + "'");
        List listRows = commonTableOperate.queryTableDatas(parameters);

        if(listRows == null || listRows.size() == 0)
        {
            return AjaxResult.error("未查找到资源记录[" + resid + "]");
        }

        try
        {
            // 执行翻译特性
            TranslateCacheProvider.decorateRecords(modelName, listRows);
        }
        catch (Exception ex)
        {
            log.error("翻译记录发生异常： {}" + ex.getMessage());
            ex.printStackTrace();
        }

        Map resRow = (Map) listRows.get(0);
        for(Map attrMap : attrs)
        {
            String attrFldn = MapUtils.getString(attrMap, "field_name", "").toLowerCase();
            if(resRow.containsKey(attrFldn))
            {
                attrMap.put("field_value", resRow.get(attrFldn));
            }
            if(resRow.containsKey(attrFldn + "_transName"))
            {
                attrMap.put("field_transValue", resRow.get(attrFldn + "_transName"));
            }
            else
            {
                attrMap.put("field_transValue", resRow.get(attrFldn));
            }
        }

        Map<String, Object> resultData = new HashMap<>();
        resultData.put("attrs", attrs);
        resultData.put("attributes", resRow);
        resultData.put("model_detail", modelDetail);
//            resultData.put("physic_columns", listColsInfo);

        //查询嵌套关系: 下级嵌套资源列表查询
        if(MapUtils.getString(params, "query_child_res", "").equals("true"))
        {
            Map<String, Map<String, Object>> subResMap = new LinkedHashMap<>();
            List<Map> listChildRelations = resModelRelationsService.selectChildRelations(modelName);
            for(Map relation : listChildRelations)
            {
                Map<String, Object> subModelResMap = new LinkedHashMap<>();
                String parent_model_name = MapUtils.getString(relation, "parent_model_name", "");
                String child_model_name = MapUtils.getString(relation, "child_model_name", "");
                String child_model_fldn = MapUtils.getString(relation, "child_model_fldn", "");
                if(TextUtil.isNull(child_model_name) || TextUtil.isNull(child_model_fldn))
                {
                    subModelResMap.put("relation", relation);
                    subModelResMap.put("subModel", null);
                    subModelResMap.put("records", new ArrayList<>());
                    subResMap.put(child_model_name, subModelResMap);
                    continue;
                }
                Map subModelDetail = ResModelCacheProvider.getCachedBaseModel(child_model_name);
                if(subModelDetail == null || subModelDetail.isEmpty() || subModelDetail.get("model") == null)
                {
                    subModelResMap.put("relation", relation);
                    subModelResMap.put("subModel", subModelDetail);
                    subModelResMap.put("records", new ArrayList<>());
                    subResMap.put(child_model_name, subModelResMap);
                    continue;
                }
                Map subModelInfo = (Map<String, Object>)subModelDetail.get("model");
                String subTableName = MapUtils.getString(subModelInfo, "attr_table");
                try
                {
                    String sql = "select * from " + subTableName + " where " + child_model_fldn + "='" + resid + "' limit 1000";
                    List<Map> listSubRecords = commonTableOperate.queryBySQL(sql);
                    subModelResMap.put("relation", relation);
                    subModelResMap.put("subModel", subModelDetail);
                    subModelResMap.put("records", listSubRecords);
                    subResMap.put(child_model_name, subModelResMap);
                }
                catch (Exception ex)
                {
                    subModelResMap.put("relation", relation);
                    subModelResMap.put("subModel", subModelDetail);
                    subModelResMap.put("records", new ArrayList<>());
                    subResMap.put(child_model_name, subModelResMap);
                    log.error("查询嵌套资源失败：" + ex.getMessage());
                }

            }
            resultData.put("child_resmap", subResMap);
        }



        return AjaxResult.success("查询成功", resultData);
    }

    public AjaxResult initResmodelRowDetail(Map<String, Object> params) throws Exception {
        String check = ParamCheckUtil.checkMustFldn(params, "model_name");
        if(StringUtils.isNotEmpty(check))
        {
            return AjaxResult.error(check);
        }
        log.info("查询参数: {}", JSONUtil.parse(params).toString());
        String modelName = MapUtils.getString(params,"model_name");
        Map modelDetail = ResModelCacheProvider.getCachedBaseModel(modelName);
        if(modelDetail == null || modelDetail.isEmpty())
        {
            return AjaxResult.error("模型名称[" + modelName + "]不存在");
        }
        Map modelInfo = (Map<String, Object>)modelDetail.get("model");
        List<Map<String, Object>> attrs = (List<Map<String, Object>>) modelDetail.get("attrs");
        String tableName = MapUtils.getString(modelInfo, "attr_table");

        //查找资源表主键
        Map physicColumnsInfo = ResModelCacheProvider.getModelPhysicColumns(modelName);
        String pk_fldn = MapUtils.getString(physicColumnsInfo, "attr_pk_fldn");
        List<Map> listColsInfo = (List<Map>)physicColumnsInfo.get("attr_columns");
        Map<String, Map> colsInfoMap = new HashMap<>();
        for(Map colInfo : listColsInfo)
        {
            String fldn = MapUtils.getString(colInfo, "column_name");
            colsInfoMap.put(fldn, colInfo);
        }

        //模板模型属性约束特性: 如果存在则覆盖基础模型约束规则
        String templateId = MapUtils.getString(params, "template_id", "");
        Map<String, Map> templateAttrMap = new HashMap<>();
        Map<String, String> templateDictAttrs = new HashMap<>();
        if(TextUtil.isNotNull(templateId))
        {
            String resmodelId = MapUtils.getString(modelInfo, "id", "");
            String sql = "select * from res_template_model where res_template_id='" + templateId + "' and res_model_id='" + resmodelId + "' and status='1'";
            List<Map> listTemplateModel = commonTableOperate.queryBySQL(sql);
            if(listTemplateModel.size() > 0)
            {
                Map templateModel = listTemplateModel.get(0);
                String templateModelId = MapUtils.getString(templateModel, "id", "");
                sql = "select * from res_template_model_attr a where a.res_template_model_id = '" + templateModelId + "' order by display_order";
                List<Map> listTemplateModelAttr = commonTableOperate.queryBySQL(sql);
                if(listTemplateModelAttr.size() > 0)
                {
                    for(Map colInfo : listTemplateModelAttr)
                    {
                        String fldn = MapUtils.getString(colInfo, "field_name");
                        String translate_type = MapUtils.getString(colInfo, "translate_type");
                        String dict_type = MapUtils.getString(colInfo, "dict_table", "");
                        templateAttrMap.put(fldn, colInfo);
                        if("1".equals(translate_type) && TextUtil.isNotNull(dict_type))
                        {
                            templateDictAttrs.put(fldn, dict_type);
                        }
                    }
                }
            }
        }

        //默认值对象
        Map<String, Object> resRow = new HashMap<>();
        for(Map attrMap : attrs)
        {
            String attrFldn = MapUtils.getString(attrMap, "field_name", "").toLowerCase();
            Object defaultVal = null;
            // 个别字段设置有问题的时候可能引发异常，容错处理
            try
            {
                if(colsInfoMap.containsKey(attrFldn))
                {
                    Map colInfoMap = colsInfoMap.get(attrFldn);
                    String column_type = MapUtils.getString(colInfoMap, "column_type"); //float8,numeric,st_multilinestring,int4,int8,st_point,date,st_multipolygon,interval,bpchar,timestamp,int2,varchar,text
                    if(TextUtil.isNull(column_type))
                    {
                        column_type = MapUtils.getString(attrMap, "field_type", "");
                    }
                    //分类： 字符、数值、日期(时间)、几何图形
                    if(column_type.equals("float8") || column_type.equals("numeric"))
                    {
                        defaultVal = attrMap.get("default_value") == null ? null : MapUtils.getDouble(attrMap, "default_value");
                    }
                    else if(column_type.equals("int4") || column_type.equals("int8") || column_type.equals("int2"))
                    {
                        defaultVal = attrMap.get("default_value") == null ? null : MapUtils.getLong(attrMap, "default_value");
                    }
                    else if(column_type.equals("bpchar") || column_type.equals("varchar") || column_type.equals("text"))
                    {
                        defaultVal = attrMap.get("default_value") == null ? "" : MapUtils.getString(attrMap, "default_value", "");
                    }
                    else if(column_type.equals("date") || column_type.equals("timestamp"))
                    {
                        defaultVal = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", new Date());
                    }
                    else
                    {
                        defaultVal = attrMap.get("default_value") == null ? null : MapUtils.getString(attrMap, "default_value", "");
                    }

                    //模板模型属性默认值覆盖基础模型： 如果设置有值则覆盖
                    if(templateAttrMap.containsKey(attrFldn))
                    {
                        Map templateAttr = templateAttrMap.get(attrFldn);
                        //分类： 字符、数值、日期(时间)、几何图形
                        if(column_type.equals("float8") || column_type.equals("numeric"))
                        {
                            defaultVal = templateAttr.get("default_value") == null ? defaultVal : MapUtils.getDouble(templateAttr, "default_value");
                        }
                        else if(column_type.equals("int4") || column_type.equals("int8") || column_type.equals("int2"))
                        {
                            defaultVal = templateAttr.get("default_value") == null ? defaultVal : MapUtils.getLong(templateAttr, "default_value");
                        }
                        else if(column_type.equals("bpchar") || column_type.equals("varchar") || column_type.equals("text"))
                        {
                            defaultVal = templateAttr.get("default_value") == null ? defaultVal : MapUtils.getString(templateAttr, "default_value", "");
                        }
                        else if(column_type.equals("date") || column_type.equals("timestamp"))
                        {
                            defaultVal = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", new Date());
                        }
                        else
                        {
                            defaultVal = templateAttr.get("default_value") == null ? defaultVal : MapUtils.getString(templateAttr, "default_value", "");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                log.error("处理字段【" + attrFldn + "】默认值发生异常：" + ex.getMessage());
                ex.printStackTrace();
            }

            resRow.put(attrFldn, defaultVal);
        }

        //字典类型转换
        Map<String, Map<String, Object>> dictionary = TranslateCacheProvider.getResmodelDicTransData(modelName);
        Map<String, List<Map<String, Object>>> dictionaryData = new HashMap<>();
        for(String key : dictionary.keySet())
        {
            List<Map<String, Object>> dicList = new ArrayList<>();
            for (String dictVal : dictionary.get(key).keySet())
            {
                Map<String, Object> map = new LinkedHashMap<>();
                map.put("value", dictVal);
                map.put("label", dictionary.get(key).get(dictVal));
                dicList.add(map);
            }
            dictionaryData.put(key, dicList);
        }

        //补充模板内的字典翻译类型数据: 覆盖基础模型
        if(!templateAttrMap.isEmpty())
        {
            Map<String, String> baseModelDictAttrs = TranslateCacheProvider.getResModelDicTransAttrs(modelName);
            for(String fldn : templateDictAttrs.keySet())
            {
                if(!baseModelDictAttrs.containsKey(fldn) || !baseModelDictAttrs.get(fldn).equals(templateDictAttrs.get(fldn)))
                {
                    Map<String, Object> value2labelMap = TranslateCacheProvider.queryDicTransRecord(templateDictAttrs.get(fldn));
                    List<Map<String, Object>> dicList = new ArrayList<>();
                    for (String dictVal : value2labelMap.keySet())
                    {
                        Map<String, Object> map = new LinkedHashMap<>();
                        map.put("value", dictVal);
                        map.put("label", value2labelMap.get(dictVal));
                        dicList.add(map);
                    }
                    dictionaryData.put(fldn, dicList);
                }
            }
        }

        //嵌套资源关系
        List<Map> listRelations = resModelRelationsService.selectAllRelations(modelName);
        List<Map> listParentRelations = new ArrayList<>();
        List<Map> listChildRelations = new ArrayList<>();
        for(Map relation : listRelations)
        {
            String parent_model_name = MapUtils.getString(relation, "parent_model_name", "");
            String child_model_name = MapUtils.getString(relation, "child_model_name", "");
            if(modelName.equals(parent_model_name))
            {
                listChildRelations.add(relation);
            }
            else if(modelName.equals(child_model_name))
            {
                listParentRelations.add(relation);
            }
        }

        Map<String, Object> resultData = new HashMap<>();
        resultData.put("attrs", attrs);
        resultData.put("attributes", resRow);
        resultData.put("model_detail", modelDetail);
        resultData.put("dictionary", dictionaryData);
        resultData.put("relation_expr", TranslateCacheProvider.getResModelSQLTransAttrs(modelName));
        resultData.put("all_relations", listRelations);
        resultData.put("parent_relations", listParentRelations);
        resultData.put("child_relations", listChildRelations);
        return AjaxResult.success("查询成功", resultData);
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public AjaxResult saveResmodelRowDetail(Map<String, Object> params) throws Exception {
        String check = ParamCheckUtil.checkMustFldn(params, "records");
        if(StringUtils.isNotEmpty(check))
        {
            return AjaxResult.error(check);
        }
        if(!(params.get("records") instanceof Collection))
        {
            return AjaxResult.error("数据记录[records]应该是数组结构");
        }

        log.info("参数: {}", JSONUtil.parse(params).toString());

        Map tableName2ModelName = ResModelCacheProvider.getTablenameToModelnameMap();

        Collection records = (Collection) params.get("records");

        // 先做校验
        for(Object record : records)
        {
            Map recordMap = (Map) record;
            Map attributesMap = (Map) recordMap.get("attributes");

            if(attributesMap == null || attributesMap.isEmpty())
            {
                return AjaxResult.error("存在记录的属性信息为空，请检测!");
            }

            String modelName = MapUtils.getString(recordMap,"model_name");
            Map modelDetail = ResModelCacheProvider.getCachedBaseModel(modelName);
            if(modelDetail == null || modelDetail.isEmpty())
            {
                return AjaxResult.error("记录中的模型名称[" + modelName + "]不存在");
            }
            Map modelInfo = (Map<String, Object>)modelDetail.get("model");
            List<Map<String, Object>> attrs = (List<Map<String, Object>>) modelDetail.get("attrs");
            String tableName = MapUtils.getString(modelInfo, "attr_table");
            String geoTableName = MapUtils.getString(modelInfo, "geo_table");
            Map physicColumnsInfo = ResModelCacheProvider.getModelPhysicColumns(modelName);
            String pk_fldn = MapUtils.getString(physicColumnsInfo, "attr_pk_fldn");
            List<Map> listColsInfo = (List<Map>)physicColumnsInfo.get("attr_columns");
            Map<String, Map> colsInfoMap = new HashMap<>();
            for(Map colInfo : listColsInfo)
            {
                String fldn = MapUtils.getString(colInfo, "column_name");
                colsInfoMap.put(fldn, colInfo);
            }
            Map<String, Map<String, Object>> dictEnumsInfo = TranslateCacheProvider.getResmodelDicTransData(modelName);

            String pk = MapUtils.getString(attributesMap, pk_fldn, "");
            if(TextUtil.isNull(pk))
            {
                return AjaxResult.error("保存失败：校验不通过，模型[" + modelName + "]记录中有主键[" + pk_fldn + "]缺失的记录，请检查!");
            }

            List list = new ArrayList();
            list.add(attributesMap);
            Map<String, Map<String, Object>> transSqlData = TranslateCacheProvider.getResmodelTransSqlData(modelName, list);

            for(Map<String, Object> attr : attrs)
            {
                String fldn = MapUtils.getString(attr, "field_name");
                String allow_null = MapUtils.getString(attr, "allow_null", ""); //是否允许为空: 1-允许 0-不允许
                Object fldv = attributesMap.get(fldn);
                String translate_type = MapUtils.getString(attr, "translate_type", "");
                String translate_expr = MapUtils.getString(attr, "translate_expr", "");

                // 模板内配置的非空校验
                if(allow_null.equalsIgnoreCase("0") && attributesMap.containsKey(fldn) && (fldv == null || TextUtil.isNull(fldv.toString())))
                {
                    return AjaxResult.error("保存失败：校验不通过，模型[" + modelName + "]存在字段[" + fldn + "]非空校验未通过的记录。");
                }
                // 数据库约束的非空校验
                if(colsInfoMap.containsKey(fldn))
                {
                    Map colInfoMap = colsInfoMap.get(fldn);
                    String cannull = MapUtils.getString(colInfoMap, "cannull");
                    if(cannull.equalsIgnoreCase("0") && (fldv == null || TextUtil.isNull(fldv.toString())))
                    {
                        return AjaxResult.error("保存失败：校验不通过，模型[" + modelName + "]存在字段[" + fldn + "]非空校验未通过的记录。");
                    }
                }

                // 枚举值校验
                if(translate_type.equalsIgnoreCase("1"))
                {
                    //字典翻译枚举值校验
//                    Map<String, Object> dictEnums = dictEnumsInfo.get(fldn);
//                    if(dictEnums != null && !dictEnums.isEmpty() && (!TextUtil.isNull(fldv) && TextUtil.isNotNull(fldv.toString()) && !dictEnums.containsKey(fldv.toString())))
//                    {
//                        return AjaxResult.error("保存失败：校验不通过，模型[" + modelName + "]存在字段[" + fldn + "], 字段值[" + fldv + "]不在字典枚举值范围内。");
//                    }
                }
                else if(translate_type.equalsIgnoreCase("2"))
                {
                    //关联翻译校验： 存量记录和提交的批次记录都要纳入关联目标校验
//                    Boolean hasPeerData = false;
//                    if(fldv != null && TextUtil.isNotNull(fldv.toString()))
//                    {
//                        // 检测数据库内的记录
//                        // TODO: 由于加载的是缓存记录进行验证，如果存在数据库记录删除，但是缓存记录未删除，此处易出现校验与真实不一致，这是使用缓存的缺陷
//                        if(transSqlData.containsKey(fldn))
//                        {
//                            Map<String, Object> conjunctData = transSqlData.get(fldn);
//                            if(conjunctData != null && conjunctData.containsKey(fldv.toString()))
//                            {
//                                hasPeerData = true;
//                            }
//                        }
//
//                        // 使用阿里druid内置的sqlparser解析SQL 获取sql的语法信息 进而获取关联的数据模型 查找提交的批次记录内是否有包含关联记录
//                        if(!hasPeerData)
//                        {
//                            SQLStatement statement = SQLUtils.parseSingleStatement(translate_expr, JdbcConstants.POSTGRESQL);
//                            PGSchemaStatVisitor visitor = new PGSchemaStatVisitor();
//                            statement.accept(visitor);
//                            Map<TableStat.Name, TableStat> tabmap = visitor.getTables();
//                            for (TableStat.Name tableNameObj : tabmap.keySet()) {
//                                String tableNameConjunct = tableNameObj.getName();
//                                if(tableName2ModelName.containsKey(tableNameConjunct))
//                                {
//                                    String conjunctModelName = MapUtils.getString(tableName2ModelName, tableNameConjunct);
//                                    Map conjunctModelColumnsInfo = ResModelCacheProvider.getModelPhysicColumns(conjunctModelName);
//                                    String conjunct_pk_fldn = MapUtils.getString(conjunctModelColumnsInfo, "attr_pk_fldn");
//                                    hasPeerData = records.stream().anyMatch(item -> MapUtils.getString((Map)item, "model_name", "").equals(conjunctModelName) && MapUtils.getString((Map)((Map)item).get("attributes"), conjunct_pk_fldn, "").equals(fldv.toString()));
//                                    if(hasPeerData)
//                                    {
//                                        break;
//                                    }
//                                }
//                            }
//                        }
//
//                        // 暴力查找记录校验
//                        if(!hasPeerData)
//                        {
//                            //默认约束： 所有记录的主键都是resid, 如果有模型不按此约束，必然导致异常
//                            String conjunct_pk_fldn = "resid";
//                            hasPeerData = records.stream().anyMatch(item -> MapUtils.getString((Map)((Map)item).get("attributes"), conjunct_pk_fldn, "").equals(fldv.toString()));
//                        }
//
//                        if(!hasPeerData)
//                        {
//                            return AjaxResult.error("保存失败： 校验不通过，模型[" + modelName + "][" + fldn + "]关联的记录[" + fldv.toString() + "]不存在");
//                        }
//                    }
                }
            }
            // 几何图形数据校验
//                if(TextUtil.isNotNull(geoTableName) && !recordMap.containsKey("geometry"))
//                {
//                    return AjaxResult.error("几何图形数据不存在。");
//                }
//                if(recordMap.containsKey("geometry"))
//                {
//                    Map geometryMap = (Map) recordMap.get("geometry");
//                    MapGeometry geometry = OperatorImportFromJson.local().execute(Geometry.Type.Unknown, JSONUtil.parse(geometryMap).toString());
//                    String wkt = OperatorExportToWkt.local().execute(0, geometry.getGeometry(), null);
//                }
        }

        Integer updateOrInsertCnt = 0;
        //开始保存记录
        for(Object record : records) {
            Map recordMap = (Map) record;
            Map attributesMap = (Map) recordMap.get("attributes");
            String modelName = MapUtils.getString(recordMap,"model_name");
            Map modelDetail = ResModelCacheProvider.getCachedBaseModel(modelName);
            Map modelInfo = (Map<String, Object>)modelDetail.get("model");
            List<Map<String, Object>> attrs = (List<Map<String, Object>>) modelDetail.get("attrs");
            String tableName = MapUtils.getString(modelInfo, "attr_table");
            String geoTableName = MapUtils.getString(modelInfo, "geo_table");
            Map physicColumnsInfo = ResModelCacheProvider.getModelPhysicColumns(modelName);
            String pk_fldn = MapUtils.getString(physicColumnsInfo, "attr_pk_fldn");
            List<Map> listColsInfo = (List<Map>)physicColumnsInfo.get("attr_columns");
            Map<String, Map> colsInfoMap = new HashMap<>();
            for(Map colInfo : listColsInfo)
            {
                String fldn = MapUtils.getString(colInfo, "column_name");
                colsInfoMap.put(fldn, colInfo);
            }
            String pk = MapUtils.getString(attributesMap, pk_fldn, "");
            String sql = "select " + pk_fldn + " from " + tableName + " where " + pk_fldn + " = '" + pk + "'";
            List<Map> list = commonTableOperate.queryBySQL(sql);
            if(list.size() == 0)
            {
                //新增
                String field_part = "";
                String value_part = "";
                List<Object> listParams = new ArrayList<>();
                // 属性表记录
                for(Map<String, Object> attr : attrs) {
                    String fldn = MapUtils.getString(attr, "field_name");
                    Object fldv = attributesMap.get(fldn);
                    String allow_null = MapUtils.getString(attr, "allow_null", ""); //是否允许为空: 1-允许 0-不允许
                    // 模板内配置的非空校验: 新增时比较严格，属性列表有无属性都有必要校验，相对来说变更的情况下，如果必填字段没有出现在字段列表，就不发生更新，因此校验也可以放行
                    if(allow_null.equalsIgnoreCase("0") && (fldv == null || TextUtil.isNull(fldv.toString())))
                    {
                        return AjaxResult.error("保存失败：校验不通过，模型[" + modelName + "]存在字段[" + fldn + "]非空校验未通过的记录。");
                    }

                    if(colsInfoMap.containsKey(fldn) && fldv != null)
                    {
                        if(TextUtil.isNotNull(field_part))
                        {
                            field_part += "," + fldn;
                        }
                        else
                        {
                            field_part += fldn;
                        }
                        if(TextUtil.isNotNull(value_part))
                        {
                            value_part += ",?";
                        }
                        else
                        {
                            value_part += "?";
                        }
                        Map colInfoMap = colsInfoMap.get(fldn);
                        String column_type = MapUtils.getString(colInfoMap, "column_type", ""); //float8,numeric,st_multilinestring,int4,int8,st_point,date,st_multipolygon,interval,bpchar,timestamp,int2,varchar,text
                        if(TextUtil.isNull(column_type))
                        {
                            column_type = MapUtils.getString(attr, "field_type", "");
                        }
                        //分类： 字符、数值、日期(时间)、几何图形
                        if(column_type.equals("float8") || column_type.equals("numeric"))
                        {
                            listParams.add(Double.parseDouble(fldv.toString()));
                        }
                        else if(column_type.equals("int4") || column_type.equals("int8") || column_type.equals("int2"))
                        {
                            listParams.add(Long.parseLong(fldv.toString()));
                        }
                        else if(column_type.equals("bpchar") || column_type.equals("varchar") || column_type.equals("text"))
                        {
                            listParams.add(fldv.toString());
                        }
                        else if(column_type.equals("date") || column_type.equals("timestamp"))
                        {
                            listParams.add(DateUtils.parseDate(fldv.toString(), "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd"));
                        }
                        else
                        {
                            listParams.add(fldv);
                        }
                    }
                }
                updateOrInsertCnt += jdbcTemplate.update("insert into " + tableName + " (" + field_part + ") values (" + value_part + ")", listParams.toArray());

                // 图形表记录
                Integer srid = 4326;
                if(TextUtil.isNotNull(geoTableName) && recordMap.containsKey("geometry") && recordMap.get("geometry") != null && recordMap.get("geometry") instanceof Map)
                {
                    List<Map> listGeoColsInfo = (List<Map>)physicColumnsInfo.get("geo_columns");
                    String geo_pk_fldn = MapUtils.getString(physicColumnsInfo, "geo_pk_fldn");
                    // 选择几何列的图形类型并且搜集几何表字段到对应的
                    Geometry.Type geometryType = Geometry.Type.Unknown;
                    field_part = "";
                    value_part = "";
                    listParams = new ArrayList<>();
                    for(Map colInfo : listGeoColsInfo)
                    {
                        String column_name = MapUtils.getString(colInfo, "column_name", "");
                        String column_type = MapUtils.getString(colInfo, "column_type",  ""); //float8,numeric,st_multilinestring,int4,int8,st_point,date,st_multipolygon,interval,bpchar,timestamp,int2,varchar,text
                        if(TextUtil.isNull(column_type))
                        {
                            for(Map<String, Object> attr : attrs) {
                                if(MapUtils.getString(attr, "field_name", "").equals(column_name))
                                {
                                    column_type = MapUtils.getString(attr, "field_type", "");
                                    break;
                                }
                            }
                        }
                        String defaultval = MapUtils.getString(colInfo, "defaultval");
                        Object fldv = attributesMap.get(column_name);

                        if(column_name.equals(geo_pk_fldn) && TextUtil.isNotNull(defaultval))
                        {
                            //跳过objectid字段，数据库默认设有序列值
                            continue;
                        }
                        else if(column_name.equals(geo_pk_fldn) && column_type.startsWith("st_")) //此处新增记录，不管fldv有没有值，必须重新获取rowid，否则容易出现主键冲突的问题
                        {
                            //未设置objectid字段默认值，需要按照sde的规则来赋值
                            if(TextUtil.isNotNull(field_part))
                            {
                                field_part += "," + column_name;
                            }
                            else
                            {
                                field_part += column_name;
                            }
                            if(TextUtil.isNotNull(value_part))
                            {
                                // 函数语法： <geodatabase administrator schema>.next_rowid (<table owner>, <table name>)
                                // 参考： https://resources.arcgis.com/zh-cn/help/main/10.1/index.html#/na/006z000000w8000000/
                                value_part += ",sde.next_rowid(?, ?)";
                            }
                            else
                            {
                                value_part += "sde.next_rowid(?, ?)";
                            }
                            listParams.add("sde"); //这里的参数是sde.sde_table_registry内的owner字段值，不一定是实际拥有者
                            listParams.add(geoTableName);
                        }
                        else if(column_name.equals(geo_pk_fldn) && column_type.equals("geometry"))
                        {
                            //使用序列： 约束 序列名称为 表名称_seq
                            if(TextUtil.isNotNull(field_part))
                            {
                                field_part += "," + column_name;
                            }
                            else
                            {
                                field_part += column_name;
                            }
                            if(TextUtil.isNotNull(value_part))
                            {
                                value_part += ",nextval('" + geoTableName  + "_seq')";
                            }
                            else
                            {
                                value_part += "nextval('" + geoTableName  + "_seq')";
                            }
                        }

                        if(fldv == null && !column_type.startsWith("st_") && !column_name.equals("shape") && !column_type.equals("geometry"))
                        {
                            continue;
                        }
                        // shape字段
                        if(column_type.startsWith("st_"))
                        {
                            if(column_type.equals("st_point") || column_type.equals("st_multipoint"))
                            {
                                geometryType = Geometry.Type.Point;
                            }
                            else if(column_type.equals("st_linestring") || column_type.equals("st_multilinestring"))
                            {
                                geometryType = Geometry.Type.Polyline;
                            }
                            else if(column_type.equals("st_polygon") || column_type.equals("st_multipolygon"))
                            {
                                geometryType = Geometry.Type.Polygon;
                            }
                            else if(column_type.equals("st_geometry"))
                            {
                                //这种情形下，通过sde登记表内获取的几何类型作为几何类型
                                geometryType = Geometry.Type.Unknown;
                                if(physicColumnsInfo.containsKey("esri_geo_shape_type"))
                                {
                                    geometryType = (Geometry.Type) physicColumnsInfo.get("esri_geo_shape_type");
                                }
                            }
                            Map geometryMap = (Map) recordMap.get("geometry");
                            MapGeometry geometry = OperatorImportFromJson.local().execute(geometryType, JSONUtil.parse(geometryMap).toString());
                            //如果通过以上方法都没有正确获取几何转换结果，通过枚举类型穷举测试转换
                            if(geometry == null)
                            {
                                geometry = OperatorImportFromJson.local().execute(Geometry.Type.Point, JSONUtil.parse(geometryMap).toString());
                                if(geometry == null)
                                {
                                    geometry = OperatorImportFromJson.local().execute(Geometry.Type.Polyline, JSONUtil.parse(geometryMap).toString());
                                    if(geometry == null)
                                    {
                                        geometry = OperatorImportFromJson.local().execute(Geometry.Type.Polygon, JSONUtil.parse(geometryMap).toString());
                                    }
                                }
                            }
                            if(geometry == null)
                            {
                                return AjaxResult.error("保存失败： 图形参数值不对或者图形所属表字段类型与本地提交的图形不一致。");
                            }
                            String wkt = OperatorExportToWkt.local().execute(0, geometry.getGeometry(), null);
                            if(TextUtil.isNotNull(field_part))
                            {
                                field_part += "," + column_name;
                            }
                            else
                            {
                                field_part += column_name;
                            }
                            if(TextUtil.isNotNull(value_part))
                            {
                                value_part += ",sde.st_geometry(?, ?)";
                            }
                            else
                            {
                                value_part += "sde.st_geometry(?, ?)";
                            }
                            listParams.add(wkt);
                            listParams.add(srid);
                        }
                        else if(column_type.equals("geometry"))
                        {
                            //这种情形下，通过sde登记表内获取的几何类型作为几何类型
                            geometryType = Geometry.Type.Unknown;
                            if(physicColumnsInfo.containsKey("esri_geo_shape_type"))
                            {
                                geometryType = (Geometry.Type) physicColumnsInfo.get("esri_geo_shape_type");
                            }
                            Map geometryMap = (Map) recordMap.get("geometry");
                            MapGeometry geometry = OperatorImportFromJson.local().execute(geometryType, JSONUtil.parse(geometryMap).toString());
                            //如果通过以上方法都没有正确获取几何转换结果，通过枚举类型穷举测试转换
                            if(geometry == null)
                            {
                                geometry = OperatorImportFromJson.local().execute(Geometry.Type.Point, JSONUtil.parse(geometryMap).toString());
                                if(geometry == null)
                                {
                                    geometry = OperatorImportFromJson.local().execute(Geometry.Type.Polyline, JSONUtil.parse(geometryMap).toString());
                                    if(geometry == null)
                                    {
                                        geometry = OperatorImportFromJson.local().execute(Geometry.Type.Polygon, JSONUtil.parse(geometryMap).toString());
                                    }
                                }
                            }
                            if(geometry == null)
                            {
                                return AjaxResult.error("保存失败： 图形参数值不对或者图形所属表字段类型与本地提交的图形不一致。");
                            }
                            String wkt = OperatorExportToWkt.local().execute(0, geometry.getGeometry(), null);
                            if(TextUtil.isNotNull(field_part))
                            {
                                field_part += "," + column_name;
                            }
                            else
                            {
                                field_part += column_name;
                            }
                            if(TextUtil.isNotNull(value_part))
                            {
                                value_part += ",public.st_geomfromtext(?, ?)";
                            }
                            else
                            {
                                value_part += "public.st_geomfromtext(?, ?)";
                            }
                            listParams.add(wkt);
                            listParams.add(srid);
                        }
                        else
                        {
                            if(TextUtil.isNotNull(field_part))
                            {
                                field_part += "," + column_name;
                            }
                            else
                            {
                                field_part += column_name;
                            }

                            if(TextUtil.isNotNull(value_part))
                            {
                                value_part += ",?";
                            }
                            else
                            {
                                value_part += "?";
                            }

                            if(column_type.equals("float8") || column_type.equals("numeric"))
                            {
                                listParams.add(Double.parseDouble(fldv.toString()));
                            }
                            else if(column_type.equals("int4") || column_type.equals("int8") || column_type.equals("int2"))
                            {
                                listParams.add(Long.parseLong(fldv.toString()));
                            }
                            else if(column_type.equals("bpchar") || column_type.equals("varchar") || column_type.equals("text"))
                            {
                                listParams.add(fldv.toString());
                            }
                            else if(column_type.equals("date") || column_type.equals("timestamp"))
                            {
                                listParams.add(DateUtils.parseDate(fldv.toString(), "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd"));
                            }
                            else
                            {
                                listParams.add(fldv);
                            }
                        }
                    }
                    updateOrInsertCnt += jdbcTemplate.update("insert into " + geoTableName + " (" + field_part + ") values (" + value_part + ")", listParams.toArray());
                }
            }
            else
            {
                //修改
                String updateColvalPart = "";
                List<Object> listParams = new ArrayList<>();
                // 属性表记录
                for(Map<String, Object> attr : attrs) {
                    String fldn = MapUtils.getString(attr, "field_name");
                    Object fldv = attributesMap.get(fldn);
                    if(colsInfoMap.containsKey(fldn) && attributesMap.containsKey(fldn) && fldv != null && !fldn.equals(pk_fldn))
                    {
                        if(TextUtil.isNotNull(updateColvalPart))
                        {
                            updateColvalPart += "," + fldn + "=?";
                        }
                        else
                        {
                            updateColvalPart += fldn + "=?";
                        }
                        Map colInfoMap = colsInfoMap.get(fldn);
                        String column_type = MapUtils.getString(colInfoMap, "column_type"); //float8,numeric,st_multilinestring,int4,int8,st_point,date,st_multipolygon,interval,bpchar,timestamp,int2,varchar,text
                        //分类： 字符、数值、日期(时间)、几何图形
                        if(column_type.equals("float8") || column_type.equals("numeric"))
                        {
                            listParams.add(Double.parseDouble(fldv.toString()));
                        }
                        else if(column_type.equals("int4") || column_type.equals("int8") || column_type.equals("int2"))
                        {
                            listParams.add(Long.parseLong(fldv.toString()));
                        }
                        else if(column_type.equals("bpchar") || column_type.equals("varchar") || column_type.equals("text"))
                        {
                            listParams.add(fldv.toString());
                        }
                        else if(column_type.equals("date") || column_type.equals("timestamp"))
                        {
                            listParams.add(DateUtils.parseDate(fldv.toString(), "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd"));
                        }
                        else
                        {
                            listParams.add(fldv);
                        }
                    }
                    else if(colsInfoMap.containsKey(fldn) && attributesMap.containsKey(fldn) && fldv == null && !fldn.equals(pk_fldn))
                    {
                        //有意清理属性值为空值的情况
                        if(TextUtil.isNotNull(updateColvalPart))
                        {
                            updateColvalPart += "," + fldn + "=null";
                        }
                        else
                        {
                            updateColvalPart += fldn + "=null";
                        }
                    }
                }
                listParams.add(pk);
                updateOrInsertCnt += jdbcTemplate.update("update " + tableName + " set " + updateColvalPart + " where " + pk_fldn + "=?", listParams.toArray());

                // 图形表记录更新： 有一个约束，图形表的属性主键字段需要与属性表的主键名称保持一致
                if(TextUtil.isNotNull(geoTableName))
                {
                    List<Map> listGeoColsInfo = (List<Map>)physicColumnsInfo.get("geo_columns");
                    String geo_pk_fldn = MapUtils.getString(physicColumnsInfo, "geo_pk_fldn");
                    // 选择几何列的图形类型并且搜集几何表字段到对应的
                    Geometry.Type geometryType = Geometry.Type.Unknown;
                    updateColvalPart = "";
                    listParams = new ArrayList<>();
                    for(Map colInfo : listGeoColsInfo)
                    {
                        String column_name = MapUtils.getString(colInfo, "column_name");
                        String column_type = MapUtils.getString(colInfo, "column_type"); //float8,numeric,st_multilinestring,int4,int8,st_point,date,st_multipolygon,interval,bpchar,timestamp,int2,varchar,text
                        String defaultval = MapUtils.getString(colInfo, "defaultval");
                        Object fldv = attributesMap.get(column_name);

                        if(column_name.equals(geo_pk_fldn))
                        {
                            // 跳过objectid字段，使用resid字段关联更新
                            continue;
                        }
                        if(column_name.equals(pk_fldn))
                        {
                            // 跳过属性主键
                            continue;
                        }
                        if(!attributesMap.containsKey(column_name) && !column_type.startsWith("st_") && !column_name.equals("shape") && !column_type.equals("geometry"))
                        {
                            // 如果不是shape字段且属性列表内没有该字段，则跳过更新该字段
                            continue;
                        }
                        // shape字段
                        if((column_type.startsWith("st_") || column_name.equals("shape")) && recordMap.containsKey("geometry") && recordMap.get("geometry") != null && recordMap.get("geometry") instanceof Map)
                        {
                            if(column_type.equals("st_point") || column_type.equals("st_multipoint"))
                            {
                                geometryType = Geometry.Type.Point;
                            }
                            else if(column_type.equals("st_linestring") || column_type.equals("st_multilinestring"))
                            {
                                geometryType = Geometry.Type.Polyline;
                            }
                            else if(column_type.equals("st_polygon") || column_type.equals("st_multipolygon"))
                            {
                                geometryType = Geometry.Type.Polygon;
                            }
                            else if(column_type.equals("st_geometry"))
                            {
                                geometryType = Geometry.Type.Unknown;
                                if(physicColumnsInfo.containsKey("esri_geo_shape_type"))
                                {
                                    geometryType = (Geometry.Type) physicColumnsInfo.get("esri_geo_shape_type");
                                }
                            }
                            Map geometryMap = (Map) recordMap.get("geometry");
                            MapGeometry geometry = OperatorImportFromJson.local().execute(geometryType, JSONUtil.parse(geometryMap).toString());
                            //如果通过以上方法都没有正确获取几何转换结果，通过穷举测试转换
                            if(geometry == null)
                            {
                                geometry = OperatorImportFromJson.local().execute(Geometry.Type.Point, JSONUtil.parse(geometryMap).toString());
                                if(geometry == null)
                                {
                                    geometry = OperatorImportFromJson.local().execute(Geometry.Type.Polyline, JSONUtil.parse(geometryMap).toString());
                                    if(geometry == null)
                                    {
                                        geometry = OperatorImportFromJson.local().execute(Geometry.Type.Polygon, JSONUtil.parse(geometryMap).toString());
                                    }
                                }
                            }
                            String wkt = OperatorExportToWkt.local().execute(0, geometry.getGeometry(), null);
                            if(TextUtil.isNotNull(updateColvalPart))
                            {
                                updateColvalPart += "," + column_name + "=sde.st_geometry(?, ?)";
                            }
                            else
                            {
                                updateColvalPart += column_name + "=sde.st_geometry(?, ?)";
                            }
                            listParams.add(wkt);
                            listParams.add(4326);
                        }
                        else if(column_type.equals("geometry") && recordMap.containsKey("geometry") && recordMap.get("geometry") != null && recordMap.get("geometry") instanceof Map)
                        {
                            //这种情形下，通过sde登记表内获取的几何类型作为几何类型
                            geometryType = Geometry.Type.Unknown;
                            if(physicColumnsInfo.containsKey("esri_geo_shape_type"))
                            {
                                geometryType = (Geometry.Type) physicColumnsInfo.get("esri_geo_shape_type");
                            }
                            Map geometryMap = (Map) recordMap.get("geometry");
                            MapGeometry geometry = OperatorImportFromJson.local().execute(geometryType, JSONUtil.parse(geometryMap).toString());
                            //如果通过以上方法都没有正确获取几何转换结果，通过枚举类型穷举测试转换
                            if(geometry == null)
                            {
                                geometry = OperatorImportFromJson.local().execute(Geometry.Type.Point, JSONUtil.parse(geometryMap).toString());
                                if(geometry == null)
                                {
                                    geometry = OperatorImportFromJson.local().execute(Geometry.Type.Polyline, JSONUtil.parse(geometryMap).toString());
                                    if(geometry == null)
                                    {
                                        geometry = OperatorImportFromJson.local().execute(Geometry.Type.Polygon, JSONUtil.parse(geometryMap).toString());
                                    }
                                }
                            }
                            if(geometry == null)
                            {
                                return AjaxResult.error("保存失败： 图形参数值不对或者图形所属表字段类型与本地提交的图形不一致。");
                            }
                            String wkt = OperatorExportToWkt.local().execute(0, geometry.getGeometry(), null);
                            if(TextUtil.isNotNull(updateColvalPart))
                            {
                                updateColvalPart += "," + column_name + "=public.st_geomfromtext(?, ?)";
                            }
                            else
                            {
                                updateColvalPart += column_name + "=public.st_geomfromtext(?, ?)";
                            }
                            listParams.add(wkt);
                            listParams.add(4326);
                        }
                        else if(!column_type.startsWith("st_") && !column_name.equals("shape") && !column_type.equals("geometry"))
                        {
                            if(fldv == null)
                            {
                                if(TextUtil.isNotNull(updateColvalPart))
                                {
                                    updateColvalPart += "," + column_name + "=null";
                                }
                                else
                                {
                                    updateColvalPart += column_name + "=null";
                                }
                                continue;
                            }

                            if(TextUtil.isNotNull(updateColvalPart))
                            {
                                updateColvalPart += "," + column_name + "=?";
                            }
                            else
                            {
                                updateColvalPart += column_name + "=?";
                            }
                            if(column_type.equals("float8") || column_type.equals("numeric"))
                            {
                                listParams.add(Double.parseDouble(fldv.toString()));
                            }
                            else if(column_type.equals("int4") || column_type.equals("int8") || column_type.equals("int2"))
                            {
                                listParams.add(Long.parseLong(fldv.toString()));
                            }
                            else if(column_type.equals("bpchar") || column_type.equals("varchar") || column_type.equals("text"))
                            {
                                listParams.add(fldv.toString());
                            }
                            else if(column_type.equals("date") || column_type.equals("timestamp"))
                            {
                                listParams.add(DateUtils.parseDate(fldv.toString(), "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd"));
                            }
                            else
                            {
                                listParams.add(fldv);
                            }
                        }
                    }
                    listParams.add(pk);
                    updateOrInsertCnt += jdbcTemplate.update("update " + geoTableName + " set " + updateColvalPart + " where " + pk_fldn + "=?", listParams.toArray());
                }
            }
        }
        return AjaxResult.success("保存成功, 总记录数: " + records.size() + ", 新增或者更新记录数(含图形数据记录):" + updateOrInsertCnt);
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public AjaxResult deleteResmodelRowDetail(Map<String, Object> params) throws Exception {
        String check = ParamCheckUtil.checkMustFldn(params, "records");
        if(StringUtils.isNotEmpty(check))
        {
            return AjaxResult.error(check);
        }
        if(!(params.get("records") instanceof Collection))
        {
            return AjaxResult.error("数据记录[records]应该是数组结构");
        }

        log.info("参数: {}", JSONUtil.parse(params).toString());

        Map tableName2ModelName = ResModelCacheProvider.getTablenameToModelnameMap();

        Collection records = (Collection) params.get("records");

        // 先做校验
        for(Object record : records)
        {
            Map recordMap = (Map) record;
            Map attributesMap = (Map) recordMap.get("attributes");

            if(attributesMap == null || attributesMap.isEmpty())
            {
                return AjaxResult.error("存在记录的属性信息为空，请检测!");
            }

            String modelName = MapUtils.getString(recordMap,"model_name");
            Map modelDetail = ResModelCacheProvider.getCachedBaseModel(modelName);
            if(modelDetail == null || modelDetail.isEmpty())
            {
                return AjaxResult.error("记录中的模型名称[" + modelName + "]不存在");
            }
            Map modelInfo = (Map<String, Object>)modelDetail.get("model");
            List<Map<String, Object>> attrs = (List<Map<String, Object>>) modelDetail.get("attrs");
            String tableName = MapUtils.getString(modelInfo, "attr_table");
            String geoTableName = MapUtils.getString(modelInfo, "geo_table");
            Map physicColumnsInfo = ResModelCacheProvider.getModelPhysicColumns(modelName);
            String pk_fldn = MapUtils.getString(physicColumnsInfo, "attr_pk_fldn");
            String pk = MapUtils.getString(attributesMap, pk_fldn, "");
            if(TextUtil.isNull(pk))
            {
                return AjaxResult.error("删除失败：校验不通过，记录中有主键[" + pk_fldn + "]缺失的记录，请检查!");
            }
        }
        // 删除操作
        Integer deleteCnt = 0;
        for(Object record : records)
        {
            Map recordMap = (Map) record;
            Map attributesMap = (Map) recordMap.get("attributes");
            String modelName = MapUtils.getString(recordMap,"model_name");
            Map modelDetail = ResModelCacheProvider.getCachedBaseModel(modelName);
            Map modelInfo = (Map<String, Object>)modelDetail.get("model");
            List<Map<String, Object>> attrs = (List<Map<String, Object>>) modelDetail.get("attrs");
            String tableName = MapUtils.getString(modelInfo, "attr_table");
            String geoTableName = MapUtils.getString(modelInfo, "geo_table");
            Map physicColumnsInfo = ResModelCacheProvider.getModelPhysicColumns(modelName);
            String pk_fldn = MapUtils.getString(physicColumnsInfo, "attr_pk_fldn");
            String pk = MapUtils.getString(attributesMap, pk_fldn, "");
            String sql = "delete from " + tableName + " where " + pk_fldn + "=?";
            deleteCnt += jdbcTemplate.update(sql, pk);

            if(TextUtil.isNotNull(geoTableName))
            {
                sql = "delete from " + geoTableName + " where " + pk_fldn + "=?";
                deleteCnt += jdbcTemplate.update(sql, pk);
            }

            //关联删除直接嵌套资源
            Map<String, List<Map>>  relationsMap = ResModelCacheProvider.getResRelationsMap(modelName);
            List<Map> childRelations = (List<Map>)relationsMap.get("child");
            for(Map relationRecord : childRelations)
            {
                String childModelName = MapUtils.getString(relationRecord, "child_model_name", "");
                String childModelFldn = MapUtils.getString(relationRecord, "child_model_fldn", "");
                modelDetail = ResModelCacheProvider.getCachedBaseModel(childModelName);
                modelInfo = (Map<String, Object>)modelDetail.get("model");
                physicColumnsInfo = ResModelCacheProvider.getModelPhysicColumns(childModelName);
                String child_pk_fldn = MapUtils.getString(physicColumnsInfo, "attr_pk_fldn");
                String childTableName = MapUtils.getString(modelInfo, "attr_table");
                String childGeoTableName = MapUtils.getString(modelInfo, "geo_table");
                if(TextUtil.isNotNull(childGeoTableName))
                {
                    sql = "delete from " + childGeoTableName + " where " + child_pk_fldn + " in (select " + child_pk_fldn + " from " + childTableName + " where " + child_pk_fldn + "=?)";
                    deleteCnt += jdbcTemplate.update(sql, pk);
                }
                sql = "delete from " + childTableName + " where " + childModelFldn + "=?";
                deleteCnt += jdbcTemplate.update(sql, pk);
            }

        }
        return AjaxResult.success("删除成功, 总记录数: " + records.size() + ", 删除记录数(含图形数据、嵌套资源记录):" + deleteCnt);
    }

    public TableDataInfo queryResmodelData(Map<String, Object> params) throws Exception {
        TableDataInfo tableDataInfo = new TableDataInfo();
        String check = ParamCheckUtil.checkMustFldn(params, "model_name");
        if(StringUtils.isNotEmpty(check))
        {
            tableDataInfo.setCode(500);
            tableDataInfo.setMsg(check);
            return tableDataInfo;
        }
        log.info("查询参数: {}", JSONUtil.parse(params).toString());
        String modelName = MapUtils.getString(params,"model_name");
        Map modelDetail = ResModelCacheProvider.getCachedBaseModel(modelName);
        if(modelDetail == null || modelDetail.isEmpty())
        {
            tableDataInfo.setCode(500);
            tableDataInfo.setMsg("根据模型名称[" + modelName + "]未找到对应模型");
            return tableDataInfo;
        }
        Map modelInfo = (Map<String, Object>)modelDetail.get("model");
        List<Map<String, Object>> attrs = (List<Map<String, Object>>) modelDetail.get("attrs");
        String tableName = MapUtils.getString(modelInfo, "attr_table");
        params.put("table_name", tableName);
        // 参数值合法化处理 1.处理非法符合 2.拦截Sql Injection
        String orderBySort = MapUtils.getString(params, "orderBySort", "");
        if(StringUtils.isNotEmpty(orderBySort) && !(orderBySort.equalsIgnoreCase("asc") || orderBySort.equalsIgnoreCase("desc"))) {
            tableDataInfo.setCode(500);
            tableDataInfo.setMsg("排序参数非法：只允许asc或者desc");
            return tableDataInfo;
        }

        //结构化参数查询：
        //结构体：
        /**
         * {
         *   "filter": {
         *     "logic": "and",
         *     "filters": [
         *         { "field": "x1", "value": "ew", "operator": "eq" },
         *         { "field": "x2", "value": "eew", "operator": "startswith" },
         *         {
         *             "filter": {
         *                 "logic": "or",
         *                 "filters": [
         *                     { "field": "x3", "value": "sqwqas", "operator": "eq" },
         *                     { "field": "x4", "value": "eeew", "operator": "contains" }
         *                 ]
         *             }
         *         }
         *     ]
         *   }
         * }
         */
        //枚举定义：
        //opertators:
        /**
         * 所有数据类型通用：
         * filterable.operators.enums.eq
         * filterable.operators.enums.neq
         * filterable.operators.enums.isnull
         * filterable.operators.enums.isnotnull
         * 日期类型操作符：
         * filterable.operators.date.eq
         * filterable.operators.date.neq
         * filterable.operators.date.isnull
         * filterable.operators.date.isnotnull
         * filterable.operators.date.gte
         * filterable.operators.date.gt
         * filterable.operators.date.lte
         * filterable.operators.date.lt
         * 数值类型操作符：
         * filterable.operators.number.eq
         * filterable.operators.number.neq
         * filterable.operators.number.isnull
         * filterable.operators.number.isnotnull
         * filterable.operators.number.gte
         * filterable.operators.number.gt
         * filterable.operators.number.lte
         * filterable.operators.number.lt
         * 字符串类型操作符：
         * filterable.operators.string.eq
         * filterable.operators.string.neq
         * filterable.operators.string.isnull
         * filterable.operators.string.isnotnull
         * filterable.operators.string.isempty
         * filterable.operators.string.isnotempty
         * filterable.operators.string.startswith
         * filterable.operators.string.doesnotstartwith
         * filterable.operators.string.contains
         * filterable.operators.string.doesnotcontain
         * filterable.operators.string.endswith
         * filterable.operators.string.doesnotendwith
         */


//            SqlEscapeUtil.escapeValuePartSqlBatch(params, "table_name,conditions");
        if(params.containsKey("filter"))
        {

        }
        else if(!params.containsKey("conditions"))
        {
            StringBuffer conditions = new StringBuffer(" 1=1 ");
            attrs.stream().forEach(attr -> {
                if("1".equals(attr.get("is_query")) && StringUtils.isNotEmpty(MapUtils.getString(params, String.valueOf(attr.get("attr_name"))))){
                    String attrValue = MapUtils.getString(params, String.valueOf(attr.get("attr_name")));
                    switch (String.valueOf(attr.get("field_type"))) {
                        case "varchar":
                            conditions.append(" and " + String.valueOf(attr.get("field_name")) + ("0".equals(String.valueOf(attr.get("translate_type"))) ? " like '%" + attrValue + "%' " : " = '" + attrValue + "' "));
                            break;
                        case "timestamp":
                            conditions.append(" and " + String.valueOf(attr.get("field_name")) + " >= to_timestamp('" + attrValue + "','yyyy-mm-dd hh24:mi:ss')  ");
                            break;
                        case "float8":
                            conditions.append(" and " + String.valueOf(attr.get("field_name")) + " = " + Float.parseFloat(attrValue) + " ");
                            break;
                        case "float4":
                            conditions.append(" and " + String.valueOf(attr.get("field_name")) + " = " + Float.parseFloat(attrValue) + " ");
                            break;
                        case "int2":
                            conditions.append(" and " + String.valueOf(attr.get("field_name")) + " = " + Integer.parseInt(attrValue) + " ");
                            break;
                        case "int4":
                            conditions.append(" and " + String.valueOf(attr.get("field_name")) + " = " + Integer.parseInt(attrValue) + " ");
                            break;
                        case "int8":
                            conditions.append(" and " + String.valueOf(attr.get("field_name")) + " = " + Integer.parseInt(attrValue) + " ");
                            break;
                        case "text":
                            conditions.append(" and " + String.valueOf(attr.get("field_name")) + " like '%" + attrValue + "%' ");
                            break;
                    }
                }
            });
            params.put("conditions", conditions.toString());
        }



        SqlEscapeUtil.escapeFieldPartSqlBatch(params, "fields,orderByColumn,orderbycol,groupbycol");
        log.info("系统合法化处理后的查询参数: {}", JSONUtil.parse(params).toString());

        PageDomain pageDomain = new PageDomain();
        pageDomain.setPageNum(MapUtils.getInteger(params, "pageNum", 0));
        pageDomain.setPageSize(MapUtils.getInteger(params, "pageSize", Integer.MAX_VALUE));
        pageDomain.setOrderByColumn(MapUtils.getString(params, "orderByColumn", ""));
        pageDomain.setIsAsc(MapUtils.getString(params, "orderBySort", ""));
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        if (StringUtils.isNotNull(pageNum) && StringUtils.isNotNull(pageSize)) {
            String orderBy = SqlUtil.escapeOrderBySql(pageDomain.getOrderBy());
            PageHelper.startPage(pageNum, pageSize, orderBy).setReasonable(false);
        }
        List list = commonTableOperate.queryTableDatas(params);
        try
        {
            if(params.containsKey("exec_translate") && MapUtils.getString(params, "exec_translate", "").equalsIgnoreCase("true"))
            {
                // 执行翻译特性
                TranslateCacheProvider.decorateRecords(modelName, list);
            }
        }
        catch (Exception ex)
        {
            log.error("翻译记录发生异常： {}" + ex.getMessage());
            ex.printStackTrace();
        }

        tableDataInfo.setCode(0);
        tableDataInfo.setRows(list);
        tableDataInfo.setTotal(new PageInfo(list).getTotal());
        return tableDataInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult copyTaskResources(Map<String, Object> params) {
        String sourceTaskId = MapUtils.getString(params,"sourceTaskId");
        String targetTaskId = MapUtils.getString(params,"targetTaskId");
        String attachment = MapUtils.getString(params,"attachment");
        if (StringUtils.isEmpty(sourceTaskId) || StringUtils.isEmpty(targetTaskId)){
            return AjaxResult.error("导入失败: 工单Id并不能为空！");
        }
        try {
            //查询模型表
            List<Map> modelList = resModelService.selectRowList(new HashMap());
            for(Map model : modelList)
            {
                String modelName = MapUtils.getString(model, "name");
                //获取模型属性
                List<Map> modelAttrList = resModelAttrService.selectModelAttrList(MapUtils.getString(model, "id"));
                //获取模型嵌套关系
                List<Map> relationsList = resModelRelationsService.selectParentRelations(modelName);
                if(CollectionUtils.isNotEmpty(modelAttrList)){
                    //拷贝g表数据
                    String geo_table = MapUtils.getString(model, "geo_table");
                    if(StringUtils.isNotEmpty(geo_table)){
                        //获取g表物理表字段
                        Map paramMap = new HashMap();
                        paramMap.put("table_name", geo_table);
                        List<Map> tableColumnList = resModelAttrService.selectTableColumnList(paramMap);
                        if(CollectionUtils.isNotEmpty(tableColumnList) && matchMapValue(tableColumnList, "attname", "taskid")){
                            String copyGeoTableSql = "insert into " + geo_table + " (";
                            String selectSql = "select ";
                            for(Map modelAttr : modelAttrList){
                                String attrName = MapUtils.getString(modelAttr, "attr_name");
                                if("objectid".equals(attrName) || "resid".equals(attrName) || "taskid".equals(attrName)){
                                    continue;
                                }
                                if(matchMapValue(relationsList, "child_model_fldn", attrName)){
                                    continue;
                                }
                                if(matchMapValue(tableColumnList, "attname", attrName)){
                                    copyGeoTableSql += attrName + ", ";
                                    selectSql += attrName + ", ";
                                }
                            }
                            if(CollectionUtils.isNotEmpty(relationsList)){
                                for(Map relation : relationsList){
                                    if(matchMapValue(tableColumnList, "attname", MapUtils.getString(relation, "child_model_fldn")) && !copyGeoTableSql.contains(MapUtils.getString(relation, "child_model_fldn"))){
                                        copyGeoTableSql += MapUtils.getString(relation, "child_model_fldn") + ", ";
                                        selectSql += MapUtils.getString(relation, "child_model_fldn") + " || '-c' , ";
                                    }
                                }
                            }
                            if(matchMapValue(tableColumnList, "attname", "objectid")){
                                copyGeoTableSql += " objectid, ";
                                selectSql += " sde.next_rowid('sde', '"+geo_table+"'), ";
                            }
                            if(matchMapValue(tableColumnList, "attname", "resid")){
                                copyGeoTableSql += " resid, ";
                                selectSql += " resid || '-c', ";
                            }
                            if(matchMapValue(tableColumnList, "attname", "taskid")){
                                copyGeoTableSql += " taskid, ";
                                selectSql += " '"+targetTaskId+"', ";
                            }
                            if(matchMapValue(tableColumnList, "attname", "shape")){
                                copyGeoTableSql += " shape, ";
                                selectSql += " shape, ";
                            }
                            if(copyGeoTableSql.endsWith(", ")){
                                copyGeoTableSql = copyGeoTableSql.substring(0, copyGeoTableSql.length() - 2);
                            }
                            if(selectSql.endsWith(", ")){
                                selectSql = selectSql.substring(0, selectSql.length() - 2);
                            }
                            copyGeoTableSql += ") ";
                            selectSql += " from " + geo_table + " where taskid = '"+sourceTaskId+"'";
                            jdbcTemplate.update(copyGeoTableSql + selectSql);
                        }
                        if("true".equals(attachment)){
                            //拷贝多媒体数据
                            String attachmentSql = " insert into res_attachment " +
                                    " select 'attach' || '-' || uuid_generate_v4(), file_name, file_path, file_type, rel_resid || '-c', file_remark, upload_user_id, upload_time, ext_info1, ext_info2, ext_info3 " +
                                    " from res_attachment t " +
                                    " where exists (select 1 from "+geo_table+" t1 where t1.resid = t.rel_resid and t1.taskid = '"+sourceTaskId+"') ";
                            jdbcTemplate.update(attachmentSql);
                        }
                    }
                    //拷贝t表数据
                    String attr_table = MapUtils.getString(model, "attr_table");
                    if(StringUtils.isNotEmpty(attr_table) && StringUtils.isNotEmpty(geo_table)){
                        //获取t表物理表字段
                        Map paramMap = new HashMap();
                        paramMap.put("table_name", attr_table);
                        List<Map> tableColumnList = resModelAttrService.selectTableColumnList(paramMap);
                        if(CollectionUtils.isNotEmpty(tableColumnList) && matchMapValue(tableColumnList, "attname", "taskid")){
                            String copyAttrTableSql = "insert into " + attr_table + " (";
                            String selectSql = "select ";
                            for(Map modelAttr : modelAttrList){
                                String attrName = MapUtils.getString(modelAttr, "attr_name");
                                if("objectid".equals(attrName) || "resid".equals(attrName) || "taskid".equals(attrName)){
                                    continue;
                                }
                                if(matchMapValue(relationsList, "child_model_fldn", attrName)){
                                    continue;
                                }
                                if(matchMapValue(tableColumnList, "attname", attrName)){
                                    copyAttrTableSql += attrName + ", ";
                                    selectSql += attrName + ", ";
                                }
                            }
                            if(CollectionUtils.isNotEmpty(relationsList)){
                                for(Map relation : relationsList){
                                    if(matchMapValue(tableColumnList, "attname", MapUtils.getString(relation, "child_model_fldn")) && !copyAttrTableSql.contains(MapUtils.getString(relation, "child_model_fldn"))){
                                        copyAttrTableSql += MapUtils.getString(relation, "child_model_fldn") + ", ";
                                        selectSql += MapUtils.getString(relation, "child_model_fldn") + " || '-c' , ";
                                    }
                                }
                            }
                            if(matchMapValue(tableColumnList, "attname", "resid")){
                                copyAttrTableSql += " resid, ";
                                selectSql += " resid || '-c', ";
                            }
                            if(matchMapValue(tableColumnList, "attname", "taskid")){
                                copyAttrTableSql += " taskid, ";
                                selectSql += " '"+targetTaskId+"', ";
                            }
                            if(copyAttrTableSql.endsWith(", ")){
                                copyAttrTableSql = copyAttrTableSql.substring(0, copyAttrTableSql.length() - 2);
                            }
                            if(selectSql.endsWith(", ")){
                                selectSql = selectSql.substring(0, selectSql.length() - 2);
                            }
                            copyAttrTableSql += ") ";
                            selectSql += " from " + attr_table + " where resid in (select resid from "+geo_table+" where taskid = '"+sourceTaskId+"' )";
                            jdbcTemplate.update(copyAttrTableSql + selectSql);
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("导入失败，发生未知错误："+e.getMessage());
        }
        return AjaxResult.success();
    }

    private boolean matchMapValue(List<Map> valueList, String findValueKey, String matchValue) {
        boolean find = false;
        if(CollectionUtils.isNotEmpty(valueList)){
            for(Map map : valueList){
                if(matchValue.equals(MapUtils.getString(map, findValueKey))){
                    find = true;
                }
            }
        }
        return find;
    }
}
