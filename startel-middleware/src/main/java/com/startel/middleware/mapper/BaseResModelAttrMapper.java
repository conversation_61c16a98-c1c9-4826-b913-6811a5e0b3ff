package com.startel.middleware.mapper;

import java.util.List;
import java.util.Map;

public interface BaseResModelAttrMapper {

    public Map selectRowById(String id);
    public List<Map> selectRowList(Map<String, Object> parameters);
    public int insertRow(Map<String, Object> parameters);
    public int updateRow(Map<String, Object> parameters);
    public int deleteRowLogic(String id);
    public int deleteRowBatchLogic(Map<String, Object> parameters);
    public int deleteRowById(String id);
    public int deleteRowByIds(String[] ids);
    public List<Map> selectModelAttrList(Map<String, Object> parameters);
    public List<Map> selectTableColumnList(Map<String, Object> parameter);

}
