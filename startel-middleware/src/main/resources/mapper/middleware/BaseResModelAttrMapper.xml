<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.startel.middleware.mapper.BaseResModelAttrMapper">
    <select id="selectRowById" resultType="cn.hutool.core.map.CaseInsensitiveLinkedMap" parameterType="String">
        select * from res_model_attr where id = #{id}
    </select>

    <select id="selectRowList" resultType="cn.hutool.core.map.CaseInsensitiveLinkedMap" parameterType="map">
        select * from res_model_attr
        <where>
            <if test="id != null  and id != ''"> and id = #{id}</if>
            <if test="res_model_id != null  and res_model_id != ''"> and res_model_id = #{res_model_id}</if>
            <if test="attr_name != null  and attr_name != ''"> and attr_name = #{attr_name}</if>
            <if test="attr_label != null and attr_label != ''"> and attr_label like concat('%', #{attr_label}, '%')</if>
            <if test="attr_type != null and attr_type != ''"> and attr_type = #{attr_type}</if>
            <!-- 默认查询启用的模型 如果加了status参数,就按status参数查询,否则查询启用状态的属性 -->
            <if test="status != null and status != ''"> and status = #{status}</if>
            <if test="status == null or status == ''"> and status = '1'</if>
            <if test="field_name != null  and field_name != ''"> and field_name = #{field_name}</if>
        </where>
    </select>

    <insert id="insertRow" parameterType="map">
        insert into res_model_attr
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="res_model_id != null">res_model_id,</if>
            <if test="attr_name != null">attr_name,</if>
            <if test="attr_label != null">attr_label,</if>
            <if test="attr_type != null">attr_type,</if>
            <if test="field_name != null">field_name,</if>
            <if test="field_type != null">  field_type,</if>
            <if test="allow_null != null">allow_null,</if>
            <if test="translate_type != null">translate_type,</if>
            <if test="translate_expr != null">translate_expr,</if>
            <if test="display_order != null">display_order,</if>
            <if test="usage_type != null">usage_type,</if>
            <if test="status != null">status,</if>
            <if test="default_value != null">default_value,</if>
            <if test="conjunct_model != null">conjunct_model,</if>
            <if test="cascade_function != null">cascade_function,</if>
            <if test="cascade_type != null">cascade_type,</if>
            <if test="editor_type != null">editor_type,</if>
            <if test="editor_tip != null">editor_tip,</if>
            <if test="is_display_visible != null">is_display_visible,</if>
            <if test="is_editable != null">is_editable,</if>
            <if test="is_edit_visible != null">is_edit_visible,</if>
            <if test="validate_regex != null">validate_regex,</if>
            <if test="validate_regex_msg != null">validate_regex_msg,</if>
            <if test="validate_rules != null">validate_rules,</if>
            <if test="is_batch_edit != null">is_batch_edit,</if>
            <if test="is_query != null">is_query,</if>
            <if test="is_table_visible != null">is_table_visible,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="res_model_id != null">#{res_model_id},</if>
            <if test="attr_name != null">#{attr_name},</if>
            <if test="attr_label != null">#{attr_label},</if>
            <if test="attr_type != null">#{attr_type},</if>
            <if test="field_name != null">#{field_name},</if>
            <if test="field_type != null">#{field_type},</if>
            <if test="allow_null != null">#{allow_null},</if>
            <if test="translate_type != null">#{translate_type},</if>
            <if test="translate_expr != null">#{translate_expr},</if>
            <if test="display_order != null">#{display_order},</if>
            <if test="usage_type != null">#{usage_type},</if>
            <if test="status != null">#{status},</if>
            <if test="default_value != null">#{default_value},</if>
            <if test="conjunct_model != null">#{conjunct_model},</if>
            <if test="cascade_function != null">#{cascade_function},</if>
            <if test="cascade_type != null">#{cascade_type},</if>
            <if test="editor_type != null">#{editor_type},</if>
            <if test="editor_tip != null">#{editor_tip},</if>
            <if test="is_display_visible != null">#{is_display_visible},</if>
            <if test="is_edit_visible != null">#{is_edit_visible},</if>
            <if test="is_editable != null">#{is_editable},</if>
            <if test="validate_regex != null">#{validate_regex},</if>
            <if test="validate_regex_msg != null">#{validate_regex_msg},</if>
            <if test="validate_rules != null">#{validate_rules},</if>
            <if test="is_batch_edit != null">#{is_batch_edit},</if>
            <if test="is_query != null">#{is_query},</if>
            <if test="is_table_visible != null">#{is_table_visible},</if>
        </trim>
    </insert>

    <update id="updateRow" parameterType="map">
        update res_model_attr
        <trim prefix="SET" suffixOverrides=",">
            <if test="res_model_id != null">res_model_id = #{res_model_id},</if>
            <if test="attr_name != null">attr_name = #{attr_name},</if>
            <if test="attr_label != null">attr_label = #{attr_label},</if>
            <if test="attr_type != null">attr_type = #{attr_type},</if>
            <if test="field_name != null">field_name = #{field_name},</if>
            <if test="field_type != null">field_type = #{field_type},</if>
            <if test="allow_null != null">allow_null = #{allow_null},</if>
            <if test="translate_type != null">translate_type = #{translate_type},</if>
            <if test="translate_expr != null">translate_expr = #{translate_expr},</if>
            <if test="display_order != null">display_order = #{display_order},</if>
            <if test="usage_type != null">usage_type = #{usage_type},</if>
            <if test="status != null">status = #{status},</if>
            <if test="default_value != null">default_value = #{default_value},</if>
            <if test="conjunct_model != null">conjunct_model = #{conjunct_model},</if>
            <if test="cascade_function != null">cascade_function = #{cascade_function},</if>
            <if test="cascade_type != null">cascade_type = #{cascade_type},</if>
            <if test="editor_type != null">editor_type = #{editor_type},</if>
            <if test="editor_tip != null">editor_tip = #{editor_tip},</if>
            <if test="is_display_visible != null">is_display_visible = #{is_display_visible},</if>
            <if test="is_edit_visible != null">is_edit_visible = #{is_edit_visible},</if>
            <if test="is_editable != null">is_editable = #{is_editable},</if>
            <if test="validate_regex != null">validate_regex = #{validate_regex},</if>
            <if test="validate_regex_msg != null">validate_regex_msg = #{validate_regex_msg},</if>
            <if test="validate_rules != null">validate_rules = #{validate_rules},</if>
            <if test="is_batch_edit != null">is_batch_edit = #{is_batch_edit},</if>
            <if test="is_query != null">is_query = #{is_query},</if>
            <if test="is_table_visible != null">is_table_visible = #{is_table_visible},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteRowLogic" parameterType="String">
        update res_model_attr set status = '0' where id = #{id}
    </update>

    <update id="deleteRowBatchLogic" parameterType="map">
        update res_model_attr set status = '0' where id in
        <foreach item="item" index="index" collection="id" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <delete id="deleteRowById" parameterType="String">
        delete from res_model_attr where id = #{id}
    </delete>

    <delete id="deleteRowByIds" parameterType="String">
        delete from res_model_attr where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectModelAttrList" resultType="cn.hutool.core.map.CaseInsensitiveLinkedMap" parameterType="map">
        select * from res_model_attr
        <where>
            <if test="res_model_id != null  and res_model_id != ''"> and res_model_id = #{res_model_id}</if>
            <!-- 默认查询启用的模型 如果加了status参数,就按status参数查询,否则查询启用状态的属性 -->
            <if test="status != null and status != ''"> and status = #{status}</if>
            <if test="status == null or status == ''"> and status = '1'</if>
        </where>
        order by display_order
    </select>

    <select id="selectTableColumnList" resultType="cn.hutool.core.map.CaseInsensitiveLinkedMap" parameterType="map">
        select t.attname from pg_attribute t where exists (select 1 from pg_class t1 where t1.oid = t.attrelid and t1.relname = #{table_name})
    </select>
</mapper>