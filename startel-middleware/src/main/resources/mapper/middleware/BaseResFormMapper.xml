<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.startel.middleware.mapper.BaseResFormMapper">
    <select id="selectRowById" resultType="cn.hutool.core.map.CaseInsensitiveLinkedMap" parameterType="String">
        select * from res_model_form where form_id = #{form_id}
    </select>

    <select id="selectRowByName" resultType="cn.hutool.core.map.CaseInsensitiveLinkedMap" parameterType="String">
        select * from res_model_form where model_name = #{model_name}
    </select>

    <select id="selectRowList" resultType="cn.hutool.core.map.CaseInsensitiveLinkedMap" parameterType="map">
        select * from res_model_form
        <where>
            <if test="form_id != null and form_id != ''"> and form_id = #{form_id}</if>
            <if test="model_name != null and model_name != ''"> and model_name = #{model_name}</if>
            <if test="form_template_title != null and form_template_title != ''"> and form_template_title like concat('%', #{form_template_title}, '%')</if>
            <if test="form_template_domid != null and form_template_domid != ''"> and form_template_domid = #{form_template_domid}</if>
        </where>
    </select>

    <insert id="insertRow" parameterType="map">
        insert into res_model_form
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="form_id != null">form_id,</if>
            <if test="model_name != null">model_name,</if>
            <if test="form_template_domid != null">form_template_domid,</if>
            <if test="form_template_title != null">form_template_title,</if>
            <if test="form_template != null">form_template,</if>
            <if test="default_attributes != null">default_attributes,</if>
            <if test="op_user != null">op_user,</if>
            <if test="op_time != null">op_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="form_id != null">#{form_id},</if>
            <if test="model_name != null">#{model_name},</if>
            <if test="form_template_domid != null">#{form_template_domid},</if>
            <if test="form_template_title != null">#{form_template_title},</if>
            <if test="form_template != null">#{form_template},</if>
            <if test="default_attributes != null">#{default_attributes},</if>
            <if test="op_user != null">#{op_user},</if>
            <if test="op_time != null">#{op_time},</if>
        </trim>
    </insert>

    <update id="updateRow" parameterType="map">
        update res_model_form
        <trim prefix="SET" suffixOverrides=",">
            <if test="model_name != null">model_name = #{model_name},</if>
            <if test="form_template_domid != null">form_template_domid = #{form_template_domid},</if>
            <if test="form_template_title != null">form_template_title = #{form_template_title},</if>
            <if test="form_template != null">form_template = #{form_template},</if>
            <if test="default_attributes != null">default_attributes = #{default_attributes},</if>
            <if test="op_user != null">op_user = #{op_user},</if>
            <if test="op_time != null">op_time = #{op_time},</if>
        </trim>
        where form_id = #{form_id}
    </update>

    <delete id="deleteRowById" parameterType="String">
        delete from res_model_form where form_id = #{form_id}
    </delete>

    <delete id="deleteRowByIds" parameterType="String">
        delete from res_model_form where form_id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>