<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.startel.middleware.mapper.BaseResModelMapper">
    <select id="selectRowById" resultType="cn.hutool.core.map.CaseInsensitiveLinkedMap" parameterType="String">
        select * from res_model where id = #{id}
    </select>

    <select id="selectRowByName" resultType="cn.hutool.core.map.CaseInsensitiveLinkedMap" parameterType="String">
        select * from res_model where status = '1' and name = #{name}
    </select>

    <select id="selectRowList" resultType="cn.hutool.core.map.CaseInsensitiveLinkedMap" parameterType="map">
        select * from res_model
        <where>
            <if test="id != null  and id != ''"> and id = #{id}</if>
            <if test="name != null  and name != ''"> and name = #{name}</if>
            <if test="ch_name != null and ch_name != ''"> and ch_name like concat('%', #{ch_name}, '%')</if>
            <if test="keyword != null and keyword != ''"> and (ch_name like concat('%', #{keyword}, '%') or name like concat('%', #{keyword}, '%'))</if>
            <if test="group_name != null and group_name != ''"> and group_name = #{group_name}</if>
            <!-- 默认查询启用的模型 如果加了status参数,就按status参数查询,否则查询启用状态的模型 -->
            <if test="status != null and status != ''"> and status = #{status}</if>
            <if test="status == null or status == ''"> and status = '1'</if>
            <if test="geo_table != null  and geo_table != ''"> and geo_table = #{geo_table}</if>
            <if test="attr_table != null  and attr_table != ''"> and attr_table = #{attr_table}</if>
        </where>
        order by sort_order
    </select>

    <insert id="insertRow" parameterType="map">
        insert into res_model
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="ch_name != null">ch_name,</if>
            <if test="group_name != null">group_name,</if>
            <if test="status != null">status,</if>
            <if test="create_by != null">create_by,</if>
            <if test="create_time != null">create_time,</if>
            <if test="update_by != null">update_by,</if>
            <if test="update_time != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="geo_table != null">geo_table,</if>
            <if test="attr_table != null">attr_table,</if>
            <if test="sort_order != null">sort_order,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="ch_name != null">#{ch_name},</if>
            <if test="group_name != null">#{group_name},</if>
            <if test="status != null">#{status},</if>
            <if test="create_by != null">#{create_by},</if>
            <if test="create_time != null">#{create_time},</if>
            <if test="update_by != null">#{update_by},</if>
            <if test="update_time != null">#{update_time},</if>
            <if test="remark != null">#{remark},</if>
            <if test="geo_table != null">#{geo_table},</if>
            <if test="attr_table != null">#{attr_table},</if>
            <if test="sort_order != null">#{sort_order},</if>
        </trim>
    </insert>

    <update id="updateRow" parameterType="map">
        update res_model
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="ch_name != null">ch_name = #{ch_name},</if>
            <if test="group_name != null">group_name = #{group_name},</if>
            <if test="status != null">status = #{status},</if>
            <if test="create_by != null">create_by = #{create_by},</if>
            <if test="create_time != null">create_time = #{create_time},</if>
            <if test="update_by != null">update_by = #{update_by},</if>
            <if test="update_time != null">update_time = #{update_time},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="geo_table != null">geo_table = #{geo_table},</if>
            <if test="attr_table != null">attr_table = #{attr_table},</if>
            <if test="sort_order != null">sort_order = #{sort_order},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteRowLogic" parameterType="String">
        update res_model set status = '0' where id = #{id}
    </update>

    <delete id="deleteRowById" parameterType="String">
        delete from res_model where id = #{id}
    </delete>

    <delete id="deleteRowByIds" parameterType="String">
        delete from res_model where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectGroupNameList" resultType="String" parameterType="map">
        select distinct COALESCE(group_name, '未知分组') as group_name from res_model where status = '1'
    </select>
</mapper>