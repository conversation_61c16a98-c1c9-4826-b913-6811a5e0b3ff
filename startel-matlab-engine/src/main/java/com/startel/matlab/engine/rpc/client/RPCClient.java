package com.startel.matlab.engine.rpc.client;

import com.startel.matlab.engine.common.Client;
import com.startel.matlab.engine.rpc.core.RPCConsumer;

/**
 * PRC客户端
 *
 * <AUTHOR>
 */
public class RPCClient extends Client {
    public RPCClient(String host, Integer port) {
        super(host, port);
    }

    @Override
    public <T> T get(Class<T> interfaceClass) throws Exception {
        return RPCConsumer.get(host, port, interfaceClass);
    }
}
