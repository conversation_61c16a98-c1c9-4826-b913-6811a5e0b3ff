package com.startel.digital.utils;

import com.alibaba.fastjson.JSON;
import com.startel.antenna.commons.ThetaPhi;
import com.startel.antenna.model.AntennaModel;

import java.util.List;

/**
 * @Author：ranchl
 * @Date：2024/11/5 17:36
 * (theta, phi) 和 (az, el) 坐标系互转
 * ## Overview
 * Both (theta, phi) and (azimuth, elevation) are spherical coordinate systems.
 * The first is used commonly in antenna pattern measurements, while the second
 * is used extensively in ground-based astronomy. Conversion between the two
 * essentially requires moving the location of the pole from one axis to another.
 * In (theta, phi) coordinates, phi is the angle from the y-axis toward the z-axis,
 * as measured from the yz plane. Phi runs [0, 360) degrees. Theta runs [0, 180).
 * In (azimuth, elevation) coordinates, azimuth is the angle from the x-axis
 * toward  y-ax, as measured from the xy plane. Azimuth runs [-180, 180) degrees.
 * Elevation runs [-90 to 90) degrees.
 * A more complete explanation of the coordinate systems can be found
 * in [1] and [2]. Coordinate transforms, as coded up, are based on the
 * formulas presented in [1].
 * ## SUPER IMPORTANT NOTE TO READ BEFORE USE
 * When writing some unit tests, I noticed that a round-trip conversion does
 * not return the identical angle pair that you started with. My first thought
 * was that this is due to the interval bounds on inverse functions (arcsin etc),
 * but some things seem a little odd still. So I'm releasing this as a gist, so
 * people can decide whether it's a useful start, and hopefully someone will
 * point to the problem and fix it one day.
 * ## References
 * [1] phitheta2azel MATLAB documentation, Mathworks.
 *     url: http://www.mathworks.com/help/phased/ref/phitheta2azel.html
 * [2] COORDINATE SYSTEM PLOTTING FOR ANTENNA MEASUREMENTS, Gregory F. Masters
 *     and Stuart F. Gregson. Tech. Report, Nearfield Systems, 2007.
 *     url: http://ww2.nearfield.com/amta/AMTA07-0092-GFM_SFG.pdf
 * """
 */
public class PhiThetaToAzelUtil {
    public static double[] azelToThetaPhi(double az, double el) {
        double cosTheta = Math.cos(el) * Math.cos(az);
        double tanPhi = Math.tan(el) / Math.sin(az);
        double theta = Math.acos(cosTheta);
        double phi = Math.atan2(Math.tan(el), Math.sin(az));
        phi = (phi + 2 * Math.PI) % (2 * Math.PI);

        return new double[]{theta, phi};
    }

    public static double[] thetaPhiToAzel(double theta, double phi) {
        double sinEl = Math.sin(phi) * Math.sin(theta);
        double tanAz = Math.cos(phi) * Math.tan(theta);
        double el = Math.asin(sinEl);
        double az = Math.atan(tanAz);

        return new double[]{az, el};
    }

    public static double[] thetaPhiToUV(double theta, double phi) {
        double u = Math.sin(theta) * Math.cos(phi);
        double v = Math.sin(theta) * Math.sin(phi);
        double w = Math.cos(theta);

        return new double[]{u, v};
    }

    public static double[] azelToUV(double az, double el) {
        double u = Math.cos(el) * Math.sin(az);
        double v = Math.sin(el);
        double w = Math.cos(az) * Math.cos(el);

        return new double[]{u, v};
    }

    public static double[] uvToThetaPhi(double u, double v) {
        double theta = Math.asin(Math.sqrt(u * u + v * v));
        double phi = Math.atan2(u, v);

        phi = (phi + 2 * Math.PI) % (2 * Math.PI);

        return new double[]{theta, phi};
    }

    public static double[] uvToAzel(double u, double v) {
        double az = Math.atan2(u, Math.sqrt(1 - u * u - v * v));
        double el = Math.asin(v);

        return new double[]{az, el};
    }

    public static void main(String[] args) {

        // thetaPhi -> uv -> azel
        double[] uv = thetaPhiToUV(Math.toRadians(10), Math.toRadians(30));
        double[] uvDegrees = new double[] { Math.toDegrees(uv[0]), Math.toDegrees(uv[1]) };
        System.out.println("thetaPhi -> uv: " + JSON.toJSONString(uvDegrees));
        System.out.println("");

        double[] azel = uvToAzel(uv[0], uv[1]);
        azel = new double[] { Math.toDegrees(azel[0]), Math.toDegrees(azel[1]) };
        System.out.println("uv -> azel: " + JSON.toJSONString(azel));
        System.out.println("");

        // thetaPhi -> azel
        azel = thetaPhiToAzel(Math.toRadians(10), Math.toRadians(30));
        azel = new double[] { Math.toDegrees(azel[0]), Math.toDegrees(azel[1]) };
        System.out.println("thetaPhi -> azel: " + JSON.toJSONString(azel));


        AntennaModel model = AntennaModel.newGaussianAntennaModelBuilder()
                .setHorizontalHalfPowerBeamWidth(65)
                .setVerticalHalfPowerBeamWidth(35)
                .build();
        // Angles to generate antenna model at
        List<ThetaPhi> angleList = ThetaPhi.equallySpacedSphere(1);
        for(ThetaPhi thetaPhi : angleList) {
            azel = thetaPhiToAzel(thetaPhi.getTheta(), thetaPhi.getPhi());
            azel = new double[] { Math.toDegrees(azel[0]), Math.toDegrees(azel[1]) };
            System.out.println(JSON.toJSONString(azel));
        }
    }
}
