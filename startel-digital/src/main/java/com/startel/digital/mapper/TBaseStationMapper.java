package com.startel.digital.mapper;

import java.util.List;
import com.startel.digital.domain.TBaseStation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 基站Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-11-29
 */
@Mapper
public interface TBaseStationMapper 
{
    /**
     * 查询基站
     * 
     * @param id 基站主键
     * @return 基站
     */
    public TBaseStation selectTBaseStationById(String id);

    /**
     * 查询基站列表
     * 
     * @param tBaseStation 基站
     * @return 基站集合
     */
    public List<TBaseStation> selectTBaseStationList(TBaseStation tBaseStation);

    /**
     * 新增基站
     * 
     * @param tBaseStation 基站
     * @return 结果
     */
    public int insertTBaseStation(TBaseStation tBaseStation);
    public int insertTBaseStations(@Param("tBaseStation") List<TBaseStation> tBaseStation);

    /**
     * 修改基站
     * 
     * @param tBaseStation 基站
     * @return 结果
     */
    public int updateTBaseStation(TBaseStation tBaseStation);

    /**
     * 删除基站
     * 
     * @param id 基站主键
     * @return 结果
     */
    public int deleteTBaseStationById(String id);

    /**
     * 批量删除基站
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTBaseStationByIds(String[] ids);
}
