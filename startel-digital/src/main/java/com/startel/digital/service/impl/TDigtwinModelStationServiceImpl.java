package com.startel.digital.service.impl;

import java.util.List;
import com.startel.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.startel.digital.mapper.TDigtwinModelStationMapper;
import com.startel.digital.domain.TDigtwinModelStation;
import com.startel.digital.service.ITDigtwinModelStationService;

/**
 * 系统孪生体基站Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-11-30
 */
@Service
public class TDigtwinModelStationServiceImpl implements ITDigtwinModelStationService 
{
    @Autowired
    private TDigtwinModelStationMapper tDigtwinModelStationMapper;

    /**
     * 查询系统孪生体基站
     * 
     * @param id 系统孪生体基站主键
     * @return 系统孪生体基站
     */
    @Override
    public TDigtwinModelStation selectTDigtwinModelStationById(String id)
    {
        return tDigtwinModelStationMapper.selectTDigtwinModelStationById(id);
    }

    /**
     * 查询系统孪生体基站列表
     * 
     * @param tDigtwinModelStation 系统孪生体基站
     * @return 系统孪生体基站
     */
    @Override
    public List<TDigtwinModelStation> selectTDigtwinModelStationList(TDigtwinModelStation tDigtwinModelStation)
    {
        return tDigtwinModelStationMapper.selectTDigtwinModelStationList(tDigtwinModelStation);
    }

    /**
     * 新增系统孪生体基站
     * 
     * @param tDigtwinModelStation 系统孪生体基站
     * @return 结果
     */
    @Override
    public int insertTDigtwinModelStation(TDigtwinModelStation tDigtwinModelStation)
    {
        tDigtwinModelStation.setCreateTime(DateUtils.getNowDate());
        return tDigtwinModelStationMapper.insertTDigtwinModelStation(tDigtwinModelStation);
    }

    /**
     * 修改系统孪生体基站
     * 
     * @param tDigtwinModelStation 系统孪生体基站
     * @return 结果
     */
    @Override
    public int updateTDigtwinModelStation(TDigtwinModelStation tDigtwinModelStation)
    {
        tDigtwinModelStation.setUpdateTime(DateUtils.getNowDate());
        return tDigtwinModelStationMapper.updateTDigtwinModelStation(tDigtwinModelStation);
    }

    /**
     * 批量删除系统孪生体基站
     * 
     * @param ids 需要删除的系统孪生体基站主键
     * @return 结果
     */
    @Override
    public int deleteTDigtwinModelStationByIds(String[] ids)
    {
        return tDigtwinModelStationMapper.deleteTDigtwinModelStationByIds(ids);
    }

    /**
     * 删除系统孪生体基站信息
     * 
     * @param id 系统孪生体基站主键
     * @return 结果
     */
    @Override
    public int deleteTDigtwinModelStationById(String id)
    {
        return tDigtwinModelStationMapper.deleteTDigtwinModelStationById(id);
    }
}
