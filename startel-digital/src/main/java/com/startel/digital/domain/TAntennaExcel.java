package com.startel.digital.domain;

import com.startel.common.annotation.Excel;
import com.startel.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * digital对象 t_antenna
 * 
 * <AUTHOR>
 * @date 2024-11-29
 */
@Data
public class TAntennaExcel extends BaseEntity
{
    private String site;
    private String transmitte;
    private String active;
    private String antenna;
    private String height;
    private String azimuth;
    private String mechanical;
    private String comments;
    private String longitude;
    private String latitude;
    /** 安装方式 */
    @Excel(name = "安装方式")
    private String wayInstall;
    /** 类型 */
    @Excel(name = "类型")
    private String type;

    private String name;

}
