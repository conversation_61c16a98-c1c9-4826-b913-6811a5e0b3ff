package com.startel.digital.controller;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.startel.common.annotation.Log;
import com.startel.common.core.controller.BaseController;
import com.startel.common.core.domain.AjaxResult;
import com.startel.common.enums.BusinessType;
import com.startel.digital.domain.TDigtwinModelList;
import com.startel.digital.service.ITDigtwinModelListService;
import com.startel.common.utils.poi.ExcelUtil;
import com.startel.common.core.page.TableDataInfo;

/**
 * 系统孪生体模型清单Controller
 * 
 * <AUTHOR>
 * @date 2024-11-21
 */
@RestController
@RequestMapping("/digital/list")
public class TDigtwinModelListController extends BaseController
{
    @Autowired
    private ITDigtwinModelListService tDigtwinModelListService;

    /**
     * 查询系统孪生体模型清单列表
     */
    @PostMapping("/listData")
    public TableDataInfo list(@RequestBody TDigtwinModelList tDigtwinModelList)
    {
        startPage();
        List<TDigtwinModelList> list = tDigtwinModelListService.selectTDigtwinModelListList(tDigtwinModelList);
        return getDataTable(list);
    }
    /**
     * 按照面查询
     */
    @PostMapping("/crownHeight")
    public TableDataInfo crownHeight(@RequestBody Map<String, Object> params)
    {
        startPage();
        List<String> list = tDigtwinModelListService.crownHeight(params);
        return getDataTable(list);
    }

    /**
     * 导出系统孪生体模型清单列表
     */
    @Log(title = "系统孪生体模型清单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TDigtwinModelList tDigtwinModelList)
    {
        List<TDigtwinModelList> list = tDigtwinModelListService.selectTDigtwinModelListList(tDigtwinModelList);
        ExcelUtil<TDigtwinModelList> util = new ExcelUtil<TDigtwinModelList>(TDigtwinModelList.class);
        util.exportExcel(response, list, "系统孪生体模型清单数据");
    }

    /**
     * 获取系统孪生体模型清单详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return AjaxResult.success(tDigtwinModelListService.selectTDigtwinModelListById(id));
    }

    /**
     * 新增系统孪生体模型清单
     */
    @Log(title = "系统孪生体模型清单", businessType = BusinessType.INSERT)
    @PostMapping("/insert")
    public AjaxResult add(@RequestBody TDigtwinModelList tDigtwinModelList)
    {
        return toAjax(tDigtwinModelListService.insertTDigtwinModelList(tDigtwinModelList));
    }

    /**
     * 修改系统孪生体模型清单
     */
    @Log(title = "系统孪生体模型清单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TDigtwinModelList tDigtwinModelList)
    {
        return toAjax(tDigtwinModelListService.updateTDigtwinModelList(tDigtwinModelList));
    }

    /**
     * 删除系统孪生体模型清单
     */
    @Log(title = "系统孪生体模型清单", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(tDigtwinModelListService.deleteTDigtwinModelListByIds(ids));
    }
}
