<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.startel.digital.mapper.TAntennaMapper">
    
    <resultMap type="com.startel.digital.domain.TAntenna" id="TAntennaResult">
        <result property="id"    column="id"    />
        <result property="site"    column="site"    />
        <result property="transmitte"    column="transmitte"    />
        <result property="active"    column="active"    />
        <result property="antenna"    column="antenna"    />
        <result property="height"    column="height"    />
        <result property="azimuth"    column="azimuth"    />
        <result property="mechanical"    column="mechanical"    />
        <result property="comments"    column="comments"    />
        <result property="longitude"    column="longitude"    />
        <result property="latitude"    column="latitude"    />
        <result property="wayInstall"    column="way_install"    />
        <result property="type"    column="type"    />
        <result property="name"    column="name"    />
        <result property="fileId"    column="file_id"    />
    </resultMap>
    <sql id="selectTAntennaVo">
        select id, site, transmitte, active, antenna, height, azimuth, mechanical, comments, longitude, latitude, way_install, type, name,file_id from t_antenna
    </sql>

    <select id="selectTAntennaList" parameterType="com.startel.digital.domain.TAntenna" resultMap="TAntennaResult">
        <include refid="selectTAntennaVo"/>
        <where>  
            <if test="site != null  and site != ''"> and site = #{site}</if>
            <if test="transmitte != null  and transmitte != ''"> and transmitte = #{transmitte}</if>
            <if test="active != null  and active != ''"> and active = #{active}</if>
            <if test="antenna != null  and antenna != ''"> and antenna = #{antenna}</if>
            <if test="height != null  and height != ''"> and height = #{height}</if>
            <if test="azimuth != null  and azimuth != ''"> and azimuth = #{azimuth}</if>
            <if test="mechanical != null  and mechanical != ''"> and mechanical = #{mechanical}</if>
            <if test="comments != null  and comments != ''"> and comments = #{comments}</if>
            <if test="longitude != null  and longitude != ''"> and longitude = #{longitude}</if>
            <if test="latitude != null  and latitude != ''"> and latitude = #{latitude}</if>
            <if test="wayInstall != null  and wayInstall != ''"> and way_install = #{wayInstall}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="fileId != null  and fileId != ''"> and file_id = #{fileId}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
        </where>
    </select>
    
    <select id="selectTAntennaById" parameterType="String" resultMap="TAntennaResult">
        <include refid="selectTAntennaVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTAntenna" parameterType="com.startel.digital.domain.TAntenna">
        insert into t_antenna
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="site != null">site,</if>
            <if test="transmitte != null">transmitte,</if>
            <if test="active != null">active,</if>
            <if test="antenna != null">antenna,</if>
            <if test="height != null">height,</if>
            <if test="azimuth != null">azimuth,</if>
            <if test="mechanical != null">mechanical,</if>
            <if test="comments != null">comments,</if>
            <if test="longitude != null">longitude,</if>
            <if test="latitude != null">latitude,</if>
            <if test="wayInstall != null">way_install,</if>
            <if test="type != null">type,</if>
            <if test="name != null">name,</if>
            <if test="fileId != null">file_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="site != null">#{site},</if>
            <if test="transmitte != null">#{transmitte},</if>
            <if test="active != null">#{active},</if>
            <if test="antenna != null">#{antenna},</if>
            <if test="height != null">#{height},</if>
            <if test="azimuth != null">#{azimuth},</if>
            <if test="mechanical != null">#{mechanical},</if>
            <if test="comments != null">#{comments},</if>
            <if test="longitude != null">#{longitude},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="wayInstall != null">#{wayInstall},</if>
            <if test="type != null">#{type},</if>
            <if test="name != null">#{name},</if>
            <if test="fileId != null">#{fileId},</if>
         </trim>
    </insert>

    <insert id="insertTAntennas" parameterType="com.startel.digital.domain.TAntenna">
            insert into t_antenna
            (
            site,
            transmitte,
            active,
            antenna,
            height,
            azimuth,
            mechanical,
            comments,
            longitude,
            latitude,
            way_install,
            type,
            name,
            file_id
            )
            values
            <foreach collection="tAntenna" item="item" separator="," >
                (
                #{item.site},
                #{item.transmitte},
                #{item.active},
                #{item.antenna},
                #{item.height},
                #{item.azimuth},
                #{item.mechanical},
                #{item.comments},
                #{item.longitude},
                #{item.latitude},
                #{item.wayInstall},
                #{item.type},
                #{item.name},
                #{item.fileId}
                )
            </foreach>
        ON CONFLICT (transmitte)
        DO UPDATE SET
        site = EXCLUDED.site,
        active = EXCLUDED.active,
        antenna = EXCLUDED.antenna,
        height = EXCLUDED.height,
        azimuth = EXCLUDED.azimuth,
        mechanical = EXCLUDED.mechanical,
        comments = EXCLUDED.comments,
        longitude = EXCLUDED.longitude,
        latitude = EXCLUDED.latitude,
        way_install = EXCLUDED.way_install,
        type = EXCLUDED.type,
        name = EXCLUDED.name,
        file_id = EXCLUDED.file_id
    </insert>

    <update id="updateTAntenna" parameterType="com.startel.digital.domain.TAntenna">
        update t_antenna
        <trim prefix="SET" suffixOverrides=",">
            <if test="site != null">site = #{site},</if>
            <if test="transmitte != null">transmitte = #{transmitte},</if>
            <if test="active != null">active = #{active},</if>
            <if test="antenna != null">antenna = #{antenna},</if>
            <if test="height != null">height = #{height},</if>
            <if test="azimuth != null">azimuth = #{azimuth},</if>
            <if test="mechanical != null">mechanical = #{mechanical},</if>
            <if test="comments != null">comments = #{comments},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="wayInstall != null">way_install = #{wayInstall},</if>
            <if test="type != null">type = #{type},</if>
            <if test="name != null">name = #{name},</if>
            <if test="fileId != null">file_id = #{fileId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTAntennaById" parameterType="String">
        delete from t_antenna where id = #{id}
    </delete>

    <delete id="deleteTAntennaByIds" parameterType="String">
        delete from t_antenna where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>