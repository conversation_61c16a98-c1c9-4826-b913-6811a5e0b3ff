# 项目相关配置
startel:
  # 名称
  name: StarTel
  # 版本
  version: 1.0.0
  # 版权年份
  copyrightYear: 2022
  # 实例演示开关
  demoEnabled: true
  # 文件路径 示例（ Windows配置D:/startel/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: D:/startel/uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数组计算 char 字符验证
  captchaType: math
  #静态资源图片上传
  uploadStaticFile:
    uploadStaticPictuer: /mnt/apps/nginx-root/3dmodels/assets/images/else/
    uploadStaticGlbGltf: /mnt/apps/nginx-root/3dmodels/assets/images/gltf/
    uploadStaticGeoJson: /mnt/apps/nginx-root/3dmodels/assets/images/geojson/
    uploadStaticExcel: /mnt/apps/nginx-root/3dmodels/assets/images/excel/
    uploadStaticJson: /mnt/apps/nginx-root/3dmodels/assets/images/json/
    uploadStaticZip: /mnt/apps/nginx-root/3dmodels/assets/images/zip/
    uploadStaticKmz: /mnt/apps/nginx-root/3dmodels/assets/images/kmz/

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 9086
  servlet:
    # 应用的访问路径
    context-path: /digital3d-api/
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 日志配置
logging:
  level:
    com.startel: debug
    org.springframework: warn
    org.springframework.jdbc.core.JdbcTemplate: debug

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles: 
    active: druid
  # 文件上传
  servlet:
     multipart:
       # 单个文件大小
       max-file-size:  50MB
       # 设置总上传的文件大小
       max-request-size:  100MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  # 日期格式化设置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8

# token配置
token:
    # 令牌自定义标识
    header: Authorization
    # 令牌密钥
    secret: abcdefghijklmnopqrstuvwxyz
    # 令牌有效期（默认30分钟）
    expireTime: 30
  
# MyBatis配置
mybatis:
    # 搜索指定包别名
    typeAliasesPackage: com.startel.**.domain
    # 配置mapper的扫描，找到所有的mapper.xml映射文件
    mapperLocations: classpath*:mapper/**/*Mapper.xml
    # 加载全局的配置文件
    configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper分页插件
pagehelper: 
  helperDialect: postgresql
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql 

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /dev-api
  title: STARTEL系统接口文档
  description: 用于管理STARTEL系统的开发接口

# 防止XSS攻击
xss: 
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

minio:
  url: http://127.0.0.1:9000
  #用户名
  accessKey: minioadmin
  #密码
  secretKey: minioadmin
  #桶名称
  bucketName: geologybucket

matlab:
  rmiServer:
    ip: 127.0.0.1
    port: 10986