package com.startel.antenna.commons;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import jdk.nashorn.internal.parser.JSONParser;
import org.apache.commons.math3.complex.Complex;
import org.apache.commons.math3.linear.*;

import java.lang.reflect.Array;
import java.util.Arrays;

/**
 * @Author：ranchl
 * @Date：2024/11/13 14:59
 */
public class PolarizationSignature {
    public static Object[] polsignature(double[][] sMat, String type, double[] epsilon, double[] tau) {
        if (sMat.length != 2 || sMat[0].length != 2) {
            throw new IllegalArgumentException("sMat must be a 2x2 scattering matrix.");
        }

        if (type == null) {
            type = "c";
        }

        if (epsilon == null) {
            epsilon = new double[91];
            for (int i = 0; i <= 90; i++) {
                epsilon[i] = i - 45;
            }
        }

        if (tau == null) {
            tau = new double[181];
            for (int i = 0; i <= 180; i++) {
                tau[i] = i - 90;
            }
        }

        int numPts = tau.length * epsilon.length;
        Complex[] pResp = new Complex[numPts];

        double[][] egrid = new double[tau.length][epsilon.length];
        double[][] tgrid = new double[tau.length][epsilon.length];
        for (int i = 0; i < tau.length; i++) {
            for (int j = 0; j < epsilon.length; j++) {
                egrid[i][j] = epsilon[j];
                tgrid[i][j] = tau[i];
            }
        }

        System.out.println(new Gson().toJson(egrid).toString());
        System.out.println(new Gson().toJson(tgrid).toString());

        double[][] egrid2 = new double[1][egrid.length * egrid[0].length];
        for(int i = 0; i < egrid[0].length; i++) {
            for(int j = 0; j < egrid.length; j++) {
                egrid2[0][i * egrid.length + j] = egrid[j][i];
            }
        }
//        egrid2 = transpose(egrid2);
        System.out.println(new Gson().toJson(egrid2).toString());

        double[][] tgrid2 = new double[1][tgrid.length * tgrid[0].length];
        for(int i = 0; i < tgrid[0].length; i++) {
            for(int j = 0; j < tgrid.length; j++) {
                tgrid2[0][i * tgrid.length + j] = tgrid[j][i];
            }
        }
//        tgrid2 = transpose(tgrid2);
        System.out.println(new Gson().toJson(tgrid2).toString());

        Complex[][] Ei = ellipseAnglesToFieldComplex(tgrid2[0], egrid2[0]);
        Complex[][] Es = multiplyMatrices(sMat, Ei);

        Complex[][] Er;
        String respType;
        if (type.startsWith("c")) {
            Er = Ei;
            respType = "Co-Pol";
        } else {
            Er = conjugate(flipud(Ei));
            Er[0] = negate(Er[0]);
            respType = "Cross-Pol";
        }

        for (int m = 0; m < numPts; m++) {
            Complex[][] ErTransPosed = transpose(Er, m);
            Complex[] EsColum = MatrixUtils.createFieldMatrix(Es).getColumn(m);
            FieldMatrix<Complex> fieldMatrix = MatrixUtils.createFieldMatrix(ErTransPosed).multiply(MatrixUtils.createColumnFieldMatrix(EsColum));
            pResp[m] = fieldMatrix.getEntry(0,0);
        }
        double[] result = new double[pResp.length];
        for(int i = 0; i < pResp.length; i++) {
            result[i] = pResp[i].abs();
        }
        double maxVal = Arrays.stream(result).max().getAsDouble();
        for(int i = 0; i < result.length; i++) {
            result[i] = result[i] / maxVal;
        }

        double[][] resultMartrix = new double[1][result.length];
        for(int i = 0; i < result.length; i++) {
            resultMartrix[0][i] = result[i];
        }
        double[][] finalResult = matrixReshape(resultMartrix, tau.length);
        return new Object[]{finalResult, epsilon, tau};
    }

    public static double[][] matrixReshape(double[][] nums, int r) {
        int totalElements = nums.length * nums[0].length;
        int c = totalElements / r;
        final double[][] result = new double[r][c];
        int newR = 0;
        int newC = 0;
        for (int i = 0; i < nums.length; i++) {
            for (int j = 0; j < nums[i].length; j++) {
                result[newR][newC] = nums[i][j];
                newC++;
                if (newC == c) {
                    newC = 0;
                    newR++;
                }
            }
        }
        return result;
    }

    private static Complex[][] transpose(Complex[][] array, int col) {
        return MatrixUtils.createFieldMatrix(array).getColumnMatrix(col).transpose().getData();
    }

    private static double[][] transpose(double[][] array) {
        return MatrixUtils.createRealMatrix(array).transpose().getData();
    }

    private static double[][] ellipseAnglesToField(double[] tau, double[] epsilon) {
        double[][] eField = new double[2][tau.length];
        for (int i = 0; i < tau.length; i++) {
            double t = Math.toRadians(tau[i]);
            double e = Math.toRadians(epsilon[i]);
            eField[0][i] = Math.cos(t) * Math.cos(e) - i * Math.sin(t) * Math.sin(e);
            eField[1][i] = Math.sin(t) * Math.cos(e) + i * Math.cos(t) * Math.sin(e);
        }
        return eField;
    }

    private static Complex[][] ellipseAnglesToFieldComplex(double[] tau, double[] epsilon) {
        Complex[][] eField = new Complex[2][tau.length];
        for (int i = 0; i < tau.length; i++) {
            double t = Math.toRadians(tau[i]);
            double e = Math.toRadians(epsilon[i]);
            eField[0][i] = new Complex(Math.cos(t) * Math.cos(e), -1 * Math.sin(t) * Math.sin(e));
            eField[1][i] = new Complex(Math.sin(t) * Math.cos(e), Math.cos(t) * Math.sin(e));
        }
        return eField;
    }

    private static Complex[][] multiplyMatrices(double[][] firstMatrix, Complex[][] secondMatrix) {
        Complex[][] result = new Complex[firstMatrix.length][secondMatrix[0].length];
        for (int row = 0; row < result.length; row++) {
            for (int col = 0; col < result[row].length; col++) {
                result[row][col] = multiplyMatricesCell(firstMatrix, secondMatrix, row, col);
            }
        }
        return result;
    }

    private static Complex multiplyMatricesCell(double[][] firstMatrix, Complex[][] secondMatrix, int row, int col) {
        Complex cell = Complex.ZERO;
        for (int i = 0; i < secondMatrix.length; i++) {
            cell = cell.add(secondMatrix[i][col].multiply(firstMatrix[row][i]));
        }
        return cell;
    }

    private static double[][] multiplyMatrix(double[][] a, double[][] b) {
        RealMatrix matrixA = new Array2DRowRealMatrix(a);
        RealMatrix matrixB = new Array2DRowRealMatrix(b);
        RealMatrix matrixC = matrixA.multiply(matrixB);

        // 输出结果
        return matrixC.getData();
    }

    /**
     * 矩阵转置
     * @param matrix
     * @return
     */
    private static double[][] conjugate(double[][] matrix) {
        double[][] result = new double[matrix[0].length][matrix.length];
        for(int i=0; i<matrix.length; i++) {
            for(int j=0; j<matrix[i].length; j++) {
                result[j][i] = matrix[i][j];
            }
        }
        return result;
    }

    private static Complex[][] conjugate(Complex[][] matrix) {
        Complex[][] result = new Complex[matrix[0].length][matrix.length];
        for(int i=0; i<matrix.length; i++) {
            for(int j=0; j<matrix[i].length; j++) {
                result[j][i] = matrix[i][j];
            }
        }
        return result;
    }

    private static Complex[] negate(Complex[] array) {
        // Implement negation logic
        Complex[] result = new Complex[array.length];
        for(int i = 0; i < array.length; i++) {
            result[i] = array[i].multiply(-1);
        }
        return result;
    }

    private static double dotProduct(double[] a, double[] b) {
        RealVector realVector1 = MatrixUtils.createRealVector(a);
        RealVector realVector2 = MatrixUtils.createRealVector(b);
        return realVector1.dotProduct(realVector2);
    }

    private static double[][] flipud(double[][] array) {
        int rows = array.length;
        int cols = array[0].length;
        double[][] flipped = new double[rows][cols];

        for (int i = 0; i < rows; i++) {
            flipped[i] = array[rows - 1 - i];
        }

        return flipped;
    }

    private static Complex[][] flipud(Complex[][] array) {
        int rows = array.length;
        int cols = array[0].length;
        Complex[][] flipped = new Complex[rows][cols];

        for (int i = 0; i < rows; i++) {
            flipped[i] = array[rows - 1 - i];
        }

        return flipped;
    }

    public static void main(String[] args) {
        double[][] sMat = new double[][] {{-1.0, 0.0}, { 0.0, 1.0}};
        System.out.println(new Gson().toJson(sMat).toString());
        double[] epsilon = new double[] { -1.0, 0.0, 1.0};
        double[] tau = new double[] { -2.0, -1.0, 0.0, 1.0, 2.0};
        Object[] result = polsignature(sMat, "c", epsilon, tau);
        System.out.println(new Gson().toJson(result[0]).toString());
    }
}
