
package com.startel.antenna.phasedarray.config;

/**
 * Default configuration for phase array antenna calculations.<br>
 * Default configuration will provide all processing resources available in system.
 *
 */
public class DefaultConfig implements Config {

    DefaultConfig() {
        /* Package Constructor */
    }

    @Override
    public int getNumberOfThreads() {
        return Runtime.getRuntime().availableProcessors();
    }

}
