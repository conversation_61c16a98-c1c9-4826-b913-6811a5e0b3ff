/*
 * Copyright 2021 Christian He<PERSON>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package examples;

import java.io.IOException;

import com.startel.antenna.commons.Field;

/**
 * Save field to file example
 * 
 
 */
@SuppressWarnings("javadoc")
public class LoadField {

    private static final String FILENAME = "src/test/resources/example_load_field.json";

    public static void main(String[] args) {
        try {
            Field field = Field.loadJson(FILENAME);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

}
