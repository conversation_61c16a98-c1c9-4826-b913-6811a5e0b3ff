<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.startel.rf.propagation.mapper.CellMapper">
  <resultMap id="BaseResultMap" type="com.startel.rf.propagation.domain.CellEntity">
    <id column="cell_name" property="cellName" jdbcType="VARCHAR"/>
    <result column="transmitter_id" property="transmitterId" jdbcType="VARCHAR"/>
    <result column="carrier" property="carrier" jdbcType="VARCHAR"/>
    <result column="physical_cell_id" property="physicalCellId" jdbcType="INTEGER"/>
    <result column="pss_id" property="pssId" jdbcType="INTEGER"/>
    <result column="sss_id" property="sssId" jdbcType="INTEGER"/>
    <result column="cell_individual_offset_db" property="cellIndividualOffsetDb" jdbcType="INTEGER"/>
    <result column="cell_selection_threshold_db" property="cellSelectionThresholdDb" jdbcType="INTEGER"/>
    <result column="diversity_support_dl" property="diversitySupportDl" jdbcType="INTEGER"/>
    <result column="additional_dl_noise_rise_db" property="additionalDlNoiseRiseDb" jdbcType="INTEGER"/>
    <result column="traffic_load_dl_percent" property="trafficLoadDlPercent" jdbcType="INTEGER"/>
    <result column="max_traffic_load_dl_percent" property="maxTrafficLoadDlPercent" jdbcType="INTEGER"/>
    <result column="num_mu_mimo_users_dl" property="numMuMimoUsersDl" jdbcType="INTEGER"/>
    <result column="num_users_dl" property="numUsersDl" jdbcType="INTEGER"/>
    <result column="radio_equipment" property="radioEquipment" jdbcType="VARCHAR"/>
    <result column="handover_margin_db" property="handoverMarginDb" jdbcType="INTEGER"/>
    <result column="layer" property="layer" jdbcType="VARCHAR"/>
    <result column="max_power_dbm" property="maxPowerDbm" jdbcType="INTEGER"/>
    <result column="min_ss_rsrp_dbm" property="minSsRsrpDbm" jdbcType="INTEGER"/>
    <result column="sss_epre_dbm" property="sssEpreDbm" jdbcType="DOUBLE"/>
    <result column="pss_epre_offset_sss_db" property="pssEpreOffsetSssDb" jdbcType="INTEGER"/>
    <result column="pbch_epre_offset_sss_db" property="pbchEpreOffsetSssDb" jdbcType="INTEGER"/>
    <result column="pdcch_epre_offset_sss_db" property="pdcchEpreOffsetSssDb" jdbcType="INTEGER"/>
    <result column="pdcch_overhead_ofdm_symbols" property="pdcchOverheadOfdmSymbols" jdbcType="INTEGER"/>
    <result column="pdsch_csi_rs_epre_offset_sss_db" property="pdschCsiRsEpreOffsetSssDb" jdbcType="INTEGER"/>
    <result column="num_required_prach_rsi" property="numRequiredPrachRsi" jdbcType="INTEGER"/>
    <result column="scheduler" property="scheduler" jdbcType="VARCHAR"/>
    <result column="diversity_support_ul" property="diversitySupportUl" jdbcType="INTEGER"/>
    <result column="additional_ul_noise_rise_db" property="additionalUlNoiseRiseDb" jdbcType="INTEGER"/>
    <result column="fractional_power_control_factor" property="fractionalPowerControlFactor" jdbcType="DOUBLE"/>
    <result column="traffic_load_ul_percent" property="trafficLoadUlPercent" jdbcType="INTEGER"/>
    <result column="max_traffic_load_ul_percent" property="maxTrafficLoadUlPercent" jdbcType="INTEGER"/>
    <result column="num_mu_mimo_users_ul" property="numMuMimoUsersUl" jdbcType="INTEGER"/>
    <result column="ul_noise_rise_db" property="ulNoiseRiseDb" jdbcType="INTEGER"/>
    <result column="num_users_ul" property="numUsersUl" jdbcType="INTEGER"/>
    <result column="ss_pbch_numerology" property="ssPbchNumerology" jdbcType="VARCHAR"/>
    <result column="ss_pbch_periodicity" property="ssPbchPeriodicity" jdbcType="VARCHAR"/>
    <result column="ss_pbch_ofdm_symbols" property="ssPbchOfdmSymbols" jdbcType="VARCHAR"/>
    <result column="traffic_numerology" property="trafficNumerology" jdbcType="VARCHAR"/>
    <result column="tdd_dl_ofdm_symbols_percent" property="tddDlOfdmSymbolsPercent" jdbcType="INTEGER"/>
  </resultMap>
</mapper>