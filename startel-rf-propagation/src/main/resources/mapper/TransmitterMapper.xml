<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.startel.rf.propagation.mapper.TransmitterMapper">
  <resultMap id="TransmitterWithCells" type="com.startel.rf.propagation.model.Transmitter">
    <!-- 必需字段映射 -->
    <result column="longitude" property="longitude" jdbcType="VARCHAR" javaType="Double" typeHandler="org.apache.ibatis.type.DoubleTypeHandler"/>
    <result column="latitude" property="latitude" jdbcType="VARCHAR" javaType="Double" typeHandler="org.apache.ibatis.type.DoubleTypeHandler"/>
    <result column="height_m" property="height" jdbcType="INTEGER" javaType="Double" typeHandler="org.apache.ibatis.type.DoubleTypeHandler"/>
    <result column="azimuth_degree" property="azimuth" jdbcType="INTEGER" javaType="Double" typeHandler="org.apache.ibatis.type.DoubleTypeHandler"/>
    <result column="mechanical_downtilt_degree" property="mechanicalTilt" jdbcType="INTEGER" javaType="Double" typeHandler="org.apache.ibatis.type.DoubleTypeHandler"/>
    <result column="additional_electrical_downtilt_degree" property="electricalTilt" jdbcType="INTEGER" javaType="Double" typeHandler="org.apache.ibatis.type.DoubleTypeHandler"/>
    <result column="transmission_feeder_length_m" property="transFeederLength" jdbcType="INTEGER" javaType="Double" typeHandler="org.apache.ibatis.type.DoubleTypeHandler"/>
    <result column="reception_feeder_length_m" property="receptionFeederLength" jdbcType="INTEGER" javaType="Double" typeHandler="org.apache.ibatis.type.DoubleTypeHandler"/>
    <result column="miscellaneous_transmission_losses_db" property="miscellaneousTransLoss" jdbcType="INTEGER" javaType="Double" typeHandler="org.apache.ibatis.type.DoubleTypeHandler"/>
    <result column="miscellaneous_reception_losses_db" property="miscellaneousReceptionLoss" jdbcType="INTEGER" javaType="Double" typeHandler="org.apache.ibatis.type.DoubleTypeHandler"/>
    <result column="transmission_losses_db" property="transmissionLoss" jdbcType="INTEGER" javaType="Double" typeHandler="org.apache.ibatis.type.DoubleTypeHandler"/>
    <result column="reception_losses_db" property="receptionLoss" jdbcType="INTEGER" javaType="Double" typeHandler="org.apache.ibatis.type.DoubleTypeHandler"/>
    <result column="noise_figure_db" property="noiseFigure" jdbcType="INTEGER" javaType="Double" typeHandler="org.apache.ibatis.type.DoubleTypeHandler"/>
    <result column="main_calculation_radius_m" property="mainCalcRadius" jdbcType="INTEGER" javaType="Double" typeHandler="org.apache.ibatis.type.DoubleTypeHandler"/>
    <result column="main_resolution_m" property="mainResolution" jdbcType="INTEGER" javaType="Double" typeHandler="org.apache.ibatis.type.DoubleTypeHandler"/>
    <result column="beamforming_model" property="beamformingModel" jdbcType="VARCHAR"/>
    <result column="max_power_dbm" property="power" jdbcType="DOUBLE" javaType="Double" typeHandler="org.apache.ibatis.type.DoubleTypeHandler"/>
    <result column="traffic_load_ul_percent" property="trafficLoad" jdbcType="DOUBLE" javaType="Double" typeHandler="org.apache.ibatis.type.DoubleTypeHandler"/>
  </resultMap>

  <select id="selectByLocationRadius" resultMap="TransmitterWithCells">
    <![CDATA[
    SELECT
    replace(t.longitude, 'E', '') as longitude,
    replace(t.latitude, 'N', '') as latitude,
    t.height_m,
    t.azimuth_degree,
    t.mechanical_downtilt_degree,
    t.additional_electrical_downtilt_degree,
    t.transmission_feeder_length_m,
    t.reception_feeder_length_m,
    t.miscellaneous_transmission_losses_db,
    t.miscellaneous_reception_losses_db,
    t.transmission_losses_db,
    t.reception_losses_db,
    t.noise_figure_db,
    t.main_calculation_radius_m,
    t.main_resolution_m,
    t.beamforming_model,
    c.max_power_dbm,
    c.traffic_load_ul_percent
FROM
    transmitters t
LEFT JOIN sites s ON
    t.site_name = s.site_name
LEFT JOIN cells c ON
    t.transmitter_id = c.transmitter_id
    WHERE ST_Distance(
        ST_MakePoint(
        CAST(replace(s.longitude, 'E', '') AS DOUBLE PRECISION),
        CAST(replace(s.latitude, 'N', '') AS DOUBLE PRECISION)
        )::geography,
        ST_MakePoint(#{longitude}, #{latitude})::geography
    ) <= #{radius} * 1000
    ]]>
  </select>
</mapper>
