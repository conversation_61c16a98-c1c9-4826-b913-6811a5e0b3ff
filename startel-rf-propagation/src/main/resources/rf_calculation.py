#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
射频传播计算脚本
用于演示在数字孪生系统中进行射频相关计算
"""

import sys
import json
import math
import argparse

def calculate_free_space_path_loss(frequency_mhz, distance_km):
    """
    计算自由空间路径损耗
    
    Args:
        frequency_mhz: 频率(MHz)
        distance_km: 距离(km)
    
    Returns:
        路径损耗(dB)
    """
    if frequency_mhz <= 0 or distance_km <= 0:
        raise ValueError("频率和距离必须大于0")
    
    # 自由空间路径损耗公式: FSPL = 20*log10(d) + 20*log10(f) + 32.45
    # d: 距离(km), f: 频率(MHz)
    fspl = 20 * math.log10(distance_km) + 20 * math.log10(frequency_mhz) + 32.45
    return fspl

def calculate_received_power(tx_power_dbm, tx_gain_db, rx_gain_db, path_loss_db):
    """
    计算接收功率
    
    Args:
        tx_power_dbm: 发射功率(dBm)
        tx_gain_db: 发射天线增益(dB)
        rx_gain_db: 接收天线增益(dB)
        path_loss_db: 路径损耗(dB)
    
    Returns:
        接收功率(dBm)
    """
    rx_power = tx_power_dbm + tx_gain_db + rx_gain_db - path_loss_db
    return rx_power

def calculate_coverage_radius(tx_power_dbm, tx_gain_db, rx_gain_db, frequency_mhz, min_rx_power_dbm):
    """
    计算覆盖半径
    
    Args:
        tx_power_dbm: 发射功率(dBm)
        tx_gain_db: 发射天线增益(dB)
        rx_gain_db: 接收天线增益(dB)
        frequency_mhz: 频率(MHz)
        min_rx_power_dbm: 最小接收功率(dBm)
    
    Returns:
        覆盖半径(km)
    """
    # 根据最小接收功率计算最大允许路径损耗
    max_path_loss = tx_power_dbm + tx_gain_db + rx_gain_db - min_rx_power_dbm
    
    # 根据自由空间路径损耗公式反推距离
    # FSPL = 20*log10(d) + 20*log10(f) + 32.45
    # d = 10^((FSPL - 20*log10(f) - 32.45) / 20)
    distance_km = 10 ** ((max_path_loss - 20 * math.log10(frequency_mhz) - 32.45) / 20)
    return distance_km

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='射频传播计算脚本')
    parser.add_argument('--mode', choices=['path_loss', 'rx_power', 'coverage'], 
                       required=True, help='计算模式')
    parser.add_argument('--frequency', type=float, required=True, help='频率(MHz)')
    parser.add_argument('--distance', type=float, help='距离(km)')
    parser.add_argument('--tx_power', type=float, help='发射功率(dBm)')
    parser.add_argument('--tx_gain', type=float, default=0, help='发射天线增益(dB)')
    parser.add_argument('--rx_gain', type=float, default=0, help='接收天线增益(dB)')
    parser.add_argument('--min_rx_power', type=float, help='最小接收功率(dBm)')
    
    try:
        args = parser.parse_args()
        
        result = {
            "status": "success",
            "mode": args.mode,
            "input_parameters": {
                "frequency_mhz": args.frequency,
                "distance_km": args.distance,
                "tx_power_dbm": args.tx_power,
                "tx_gain_db": args.tx_gain,
                "rx_gain_db": args.rx_gain,
                "min_rx_power_dbm": args.min_rx_power
            }
        }
        
        if args.mode == 'path_loss':
            if args.distance is None:
                raise ValueError("路径损耗计算需要距离参数")
            
            path_loss = calculate_free_space_path_loss(args.frequency, args.distance)
            result["calculation_result"] = {
                "path_loss_db": round(path_loss, 2)
            }
            
        elif args.mode == 'rx_power':
            if None in [args.distance, args.tx_power]:
                raise ValueError("接收功率计算需要距离和发射功率参数")
            
            path_loss = calculate_free_space_path_loss(args.frequency, args.distance)
            rx_power = calculate_received_power(args.tx_power, args.tx_gain, args.rx_gain, path_loss)
            
            result["calculation_result"] = {
                "path_loss_db": round(path_loss, 2),
                "rx_power_dbm": round(rx_power, 2)
            }
            
        elif args.mode == 'coverage':
            if None in [args.tx_power, args.min_rx_power]:
                raise ValueError("覆盖半径计算需要发射功率和最小接收功率参数")
            
            coverage_radius = calculate_coverage_radius(
                args.tx_power, args.tx_gain, args.rx_gain, 
                args.frequency, args.min_rx_power
            )
            
            result["calculation_result"] = {
                "coverage_radius_km": round(coverage_radius, 3)
            }
        
        # 输出JSON结果
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
    except Exception as e:
        error_result = {
            "status": "error",
            "message": str(e),
            "error_type": type(e).__name__
        }
        print(json.dumps(error_result, indent=2, ensure_ascii=False))
        sys.exit(1)

if __name__ == "__main__":
    main()
