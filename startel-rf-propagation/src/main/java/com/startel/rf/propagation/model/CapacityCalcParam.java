package com.startel.rf.propagation.model;

import lombok.Data;

/**
 * @Author：ranchl
 * @Date：2025/4/22 15:21
 */
@Data
public class CapacityCalcParam {
    /**
     * 带宽（MHz),取值范围 [10, 15, 20, 25, 30, 40, 50, 60, 80, 90, 100]
     */
    Double bandwidth;
    /**
     * 子载波间隔（kHz）,取值范围 取值范围 [15, 30, 60]
     */
    Double subcarrierSpacing;
    /**
     * 每RB子载波数量, 5G固定设置值：12
     */
    Integer numSubcarriers;
    /**
     * 频段开销指数， 取值： FR1, FR2
     */
    String fr;
    /**
     * 单双周期， 取值： 单周期， 双周期
     */
    String cycleType;
    /**
     * 特殊子帧时隙配比， 取值： 10:2:2, 6:4:4, 11:1:2
     */
    String specialSubframeSlot;
    /**
     * 调制方式,取值范围 [256QAM, 64QAM, 16QAM, QPSK]
     */
    String modulation;
    /**
     * 编码率，取值： 0.9258， 0.87
     */
    Double codingRate;
    /**
     * MIMO配置， 取值： 1T1R， 2T2R, 4T4R
     */
    String mimoConfiguration;
    /**
     * 功率配置(单天线口）（W), 取值： 60
     */
    Double power;


}
