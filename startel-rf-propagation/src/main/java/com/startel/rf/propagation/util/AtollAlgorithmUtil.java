package com.startel.rf.propagation.util;

import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;

import java.io.BufferedReader;
import java.nio.charset.Charset;

/**
 * @Author：ranchl
 * @Date：2025/4/18 14:48
 */
public class AtollAlgorithmUtil {
    public static double[] calcAzEl(double azTx, double elTx, double azRx, double elRx) {
        double[] azEl = new double[2];

        double azTxRadian = Math.toRadians(azTx);
        double elTxRadian = Math.toRadians(elTx);
        double azRxRadian = Math.toRadians(azRx);
        double elRxRadian = Math.toRadians(elRx);

        if (azTx == azRx) {
            azEl[0] = 0.0;
            azEl[1] = Math.toDegrees(elRxRadian - elTxRadian);
        } else {
            double az = Math.atan(1.0 / (Math.cos(elTxRadian) / Math.tan(azRxRadian - azTxRadian) + Math.sin(elTxRadian) * Math.tan(elRxRadian) / Math.sin(azRxRadian - azTxRadian)));
            double el = Math.atan(Math.sin(az) * (-Math.sin(elTxRadian) / Math.tan(azRxRadian - azTxRadian) + Math.cos(elTxRadian) * Math.tan(elRxRadian) / Math.sin(azRxRadian - azTxRadian)));
            if (Math.sin(az) * Math.sin(azRxRadian - azTxRadian) < 0) {
                az = az + Math.PI;
            }
            azEl[0] = Math.toDegrees(az);
            azEl[1] = Math.toDegrees(el);
        }

        if (azEl[0] < 0) {
            azEl[0] = azEl[0] + 360.0;
            if(azEl[0]==360.0){
                azEl[0] = 359.0;
            }
        }
        if (azEl[1] < 0) {
            azEl[1] = azEl[1] + 360.0;
            if(azEl[1]==360.0){
                azEl[1] = 359.0;
            }
        }
        return azEl;
    }

    public static double antennaGain(double[] H, double[] V, double a0, double az, double el) {
        // if el != 90: LantTx = H(az) - [(180 - |az - a0|)/180 * (H(a0) - V(el)) + |az - a0|/180 * (H(180 + a0) - V(180-el))]
        // else: LantTx = V(el)
        if (Math.abs(el) == 90) {
            return getNearestData(V, el, true); // 下倾角为90度时，直接返回垂直方向上的增益
        } else {
            return getNearestData(H, az, true) - (((180.0 - Math.abs(az - a0)) / 180.0) * (getNearestData(H, a0, true) - getNearestData(V, el, true)) + (Math.abs(az - a0) / 180.0) * (getNearestData(H, 180 + a0, true) - getNearestData(V, 180 - el, true)));
        }
    }

    private static double getNearestData(double[] data, double idx, Boolean avg) {
        if (idx < 0) {
            idx = 360.0 + idx;
        }
        if (!avg) {
            return data[(int) idx];
        } else if (idx < 360.0 && idx >= 359.0) {
            return (data[(int) idx] + data[0]) / 2.0;
        } else {
            return (data[(int) idx] + data[(int) idx + 1]) / 2.0;
        }
    }

    public static void main(String[] args) {
        double[] azel = calcAzEl(120.0, 30.0, 0.0, 0.0);
        System.out.println(JSON.toJSON(azel).toString());

//        BufferedReader reader = ResourceUtil.getReader("4T4R-16.csv", Charset.forName("UTF-8"));
        BufferedReader reader = ResourceUtil.getReader("8T8R-4.csv", Charset.forName("UTF-8"));
        double[] H = new double[360];
        double[] V = new double[360];
        final int[] i = {0};
        reader.lines().forEach(line -> {
            String[] data = line.split(",");
            if (NumberUtil.isNumber(data[0])) {
                H[i[0]] = Double.parseDouble(data[1]);
                V[i[0]] = Double.parseDouble(data[3]);
                i[0] += 1;
            }
        });
        System.out.println(getNearestData(H, 120.0, true));
        System.out.println(getNearestData(H, 120.0, false));
        System.out.println(antennaGain(H, V, 120.0, azel[0], azel[1]));
    }


}
