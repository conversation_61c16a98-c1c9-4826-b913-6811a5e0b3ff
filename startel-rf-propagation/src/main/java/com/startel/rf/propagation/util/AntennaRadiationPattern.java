package com.startel.rf.propagation.util;

/**
 * @Author：ranchl
 * @Date：2025/2/16 16:56
 */
public class AntennaRadiationPattern {

    // Convert dB to linear scale
    public static double dBToLinear(double dB) {
        return Math.pow(10, dB / 10);
    }

    // Convert linear scale to dB
    public static double linearTodB(double linear) {
        return 10 * Math.log10(linear);
    }

    // Reconstruct 3D pattern with combination method
    public static double[][] reconstruct3DPattern(
            double[] horizontalPattern,
            double[] verticalPattern,
            boolean useCrossWeighted // true=product, false=sum
    ) {
        int degrees = 360;
        double[][] pattern3D = new double[degrees][degrees];

        // Convert patterns to linear scale
        double[] horizontalLinear = new double[degrees];
        double[] verticalLinear = new double[degrees];
        for (int i = 0; i < degrees; i++) {
            horizontalLinear[i] = dBToLinear(horizontalPattern[i]);
            verticalLinear[i] = dBToLinear(verticalPattern[i]);
        }

        // Compute 3D pattern
        for (int theta = 0; theta < degrees; theta++) {
            for (int phi = 0; phi < degrees; phi++) {
                double h = horizontalLinear[phi]; // Horizontal depends on azimuth (phi)
                double v = verticalLinear[theta];  // Vertical depends on elevation (theta)

                double combined;
                if (useCrossWeighted) {
                    combined = h * v; // Cross-weighted (product)
                } else {
                    combined = h + v; // Summing (additive)
                }

                pattern3D[theta][phi] = linearTodB(combined);
            }
        }

        return pattern3D;
    }

    // Example usage
    public static void main(String[] args) {
        // Sample data (replace with actual measurements)
//        double[] horizontalPattern = new double[360];
//        double[] verticalPattern = new double[360];
//        for (int i = 0; i < 360; i++) {
//            horizontalPattern[i] = 20 * Math.cos(Math.toRadians(i)); // Example horizontal
//            verticalPattern[i] = 20 * Math.sin(Math.toRadians(i));   // Example vertical
//        }

        double[] horizontalPattern = new double[]{11.25,11.22,11.18,11.15,11.12,11.08,11.04,11.01,10.98,10.94,10.9,10.87,10.83,10.79,10.74,10.69,10.64,10.58,10.51,10.43,10.35,10.26,10.15,10.04,9.92,9.8,9.67,9.52,9.36,9.21,9.04,8.87,8.69,8.51,8.32,8.18,8.04,7.89,7.73,7.58,7.42,7.27,7.1,6.93,6.77,6.6,6.43,6.25,6.06,5.88,5.69,5.49,5.3,5.14,4.9,4.68,4.43,4.2,4,3.75,3.57,3.31,3.05,2.82,2.57,2.33,2.05,1.76,1.44,1.14,0.8,0.46,0.1,-0.25,-0.6,-0.96,-1.34,-1.69,-2.05,-2.42,-2.78,-3.19,-3.47,-3.83,-4.17,-4.5,-4.82,-5.15,-5.48,-5.78,-6.11,-6.41,-6.73,-7.04,-7.37,-7.69,-8.03,-8.38,-8.76,-9.19,-9.64,-9.91,-10.37,-10.81,-11.27,-11.72,-12.24,-12.72,-13.16,-13.62,-14.03,-14.44,-14.68,-15.01,-15.11,-15.06,-14.99,-14.82,-14.68,-14.48,-14.32,-14.09,-13.91,-13.77,-13.65,-13.61,-13.63,-13.7,-13.83,-14.04,-14.33,-14.67,-15.13,-15.67,-16.45,-17.16,-18.14,-19.29,-20.53,-22.1,-23.98,-25.97,-28.28,-30.06,-30.21,-28.89,-27.43,-25.85,-24.86,-24.01,-23.38,-23.15,-23.07,-23.35,-23.61,-24.19,-24.87,-25.62,-26.23,-26.43,-26.16,-25.16,-23.86,-22.52,-21.31,-20.13,-19.15,-18.37,-17.7,-17.22,-16.81,-16.55,-16.39,-16.36,-16.46,-16.68,-17.02,-17.5,-18.12,-18.89,-19.82,-21.1,-22.43,-24.17,-26.2,-29.09,-32.72,-38.27,-43.16,-38.91,-34.74,-31.91,-30.11,-29.06,-28.56,-28.17,-28.1,-28.14,-28.27,-28.24,-27.87,-27.38,-26.69,-25.69,-24.57,-23.52,-22.6,-21.65,-20.87,-20.14,-19.47,-18.94,-18.45,-18.05,-17.72,-17.45,-17.26,-17.11,-16.99,-16.99,-17,-17.05,-17.14,-17.32,-17.52,-17.78,-18.04,-18.39,-18.87,-19.34,-19.86,-20.55,-21.42,-22.34,-23.45,-24.86,-26.72,-29.01,-32.59,-38.97,-45.95,-38.15,-31.71,-28.26,-25.71,-23.7,-22.08,-20.65,-19.53,-18.45,-17.46,-16.62,-15.83,-15.08,-14.45,-13.84,-13.22,-12.69,-12.16,-11.7,-11.23,-10.76,-10.36,-9.92,-9.51,-9.1,-8.7,-8.31,-7.89,-7.5,-7.14,-6.75,-6.37,-6,-5.64,-5.28,-4.9,-4.55,-4.21,-3.85,-3.49,-3.15,-2.82,-2.49,-2.13,-1.8,-1.49,-1.16,-0.83,-0.5,-0.18,0.13,0.46,0.77,1.08,1.38,1.67,1.94,2.2,2.46,2.72,2.95,3.19,3.43,3.68,3.91,4.14,4.36,4.58,4.8,5.02,5.22,5.43,5.64,5.84,6.04,6.23,6.42,6.6,6.79,6.96,7.14,7.3,7.47,7.65,7.81,7.96,8.11,8.27,8.44,8.65,8.85,9.05,9.25,9.43,9.61,9.78,9.95,10.11,10.26,10.4,10.54,10.66,10.78,10.88,10.97,11.06,11.13,11.2,11.25,11.29,11.32,11.34,11.36,11.36,11.36,11.34,11.33,11.31,11.31,11.25};
        double[] verticalPattern = new double[]{-15.18,-15.18,-14.11,-13.32,-12.89,-12.79,-12.98,-13.5,-14.25,-15.12,-16.03,-16.59,-16.42,-15.52,-14.22,-12.9,-11.68,-10.59,-9.64,-8.97,-8.51,-8.22,-8.07,-8.03,-8.06,-8.1,-8.07,-7.82,-7.36,-6.64,-5.83,-4.94,-4.16,-3.48,-2.88,-2.37,-1.92,-1.57,-1.27,-1.07,-0.95,-0.84,-0.68,-0.51,-0.35,-0.28,-0.32,-0.46,-0.6,-0.69,-0.67,-0.59,-0.47,-0.32,-0.17,0.03,0.23,0.36,0.46,0.63,0.98,1.54,2.15,2.69,3.19,3.61,3.89,3.95,3.87,3.7,3.51,3.26,2.93,2.65,2.68,3.1,3.77,4.57,5.6,6.64,7.68,8.63,9.45,10.17,10.65,11.02,11.27,11.36,11.3,11.08,10.71,9.48,8.59,7.54,6.36,4.79,2.98,1.04,-1.3,-3.53,-4.84,-4.89,-4.29,-3.64,-3.38,-3.51,-3.88,-4.45,-5.3,-6.3,-7.23,-8.03,-8.11,-7.49,-6.64,-5.86,-5.3,-5.2,-5.47,-6.08,-6.95,-8.09,-9.81,-12.69,-17.49,-24.06,-19.83,-14.55,-11.82,-10.62,-9.98,-9.32,-8.57,-7.97,-7.99,-8.56,-9.62,-11.13,-13.11,-15.67,-18.13,-19.48,-18.89,-17.37,-16.24,-15.48,-14.78,-13.99,-13.22,-12.79,-12.85,-13.56,-14.5,-15.3,-16.11,-17.56,-20.3,-25.14,-29.44,-25.62,-22.22,-20.39,-19.44,-19.07,-18.9,-18.55,-17.82,-16.89,-15.83,-14.85,-14.03,-13.53,-13.26,-13.14,-13.14,-13.13,-13.09,-13.03,-13.02,-13.1,-13.41,-13.95,-14.74,-15.71,-17.03,-18.53,-20.1,-21.39,-22.05,-21.98,-21.44,-20.8,-20.33,-20.11,-20.26,-20.69,-21.44,-22.52,-23.81,-24.99,-25.68,-25.93,-25.92,-25.94,-26.02,-25.77,-24.88,-24,-23.73,-24.39,-26.1,-28.81,-31.5,-31.87,-29.02,-25.56,-23.04,-21.76,-21.69,-22.97,-26.34,-31.33,-29.64,-24.51,-22.06,-22.35,-24.9,-28.85,-30.15,-28.24,-24.75,-22.62,-22.17,-22.71,-22.99,-23.82,-26.67,-30.58,-32.36,-32.22,-32.47,-36.55,-37.34,-31.02,-27.39,-24.61,-22.79,-22.06,-21.4,-20.17,-18.94,-18.65,-19.26,-20.02,-20.73,-22.11,-25.27,-29.11,-29.73,-26.56,-24.02,-22.78,-21.67,-20.07,-18.77,-18.81,-20.05,-21.27,-21.48,-21.57,-22.3,-24.71,-39.6,-32.26,-29.57,-29.58,-25.76,-21.82,-20.44,-20.25,-20.01,-20.36,-20.81,-19.96,-18.53,-18.22,-18.78,-19.53,-20.6,-21.34,-20.72,-19.77,-19.71,-20.06,-20.37,-21.13,-21.6,-21.08,-20.23,-20.03,-20.18,-20.18,-20.24,-20.25,-19.87,-19.11,-18.22,-17.24,-16.11,-15.18,-15.27,-16.33,-17.29,-16.71,-15.08,-14.03,-13.93,-14.91,-17.22,-20.35,-21.75,-20.19,-18,-16.82,-16.29,-16.07,-16.03,-16.07,-15.95,-15.37,-14.65,-14.1,-13.9,-14.05,-14.48,-14.65,-14.25,-13.37,-12.28,-11.41,-11.01,-11.31,-12.26,-13.83,-15.42,-16.49,-16.2,-15.02,-13.51,-12.36,-11.62,-11.42,-11.79,-12.58,-13.89,-15.41,-17.03,-17.97,-17.65,-16.49};


        // Reconstruct using cross-weighted (product)
        double[][] patternProduct = reconstruct3DPattern(horizontalPattern, verticalPattern, true);

        // Reconstruct using summing (additive)
        double[][] patternSum = reconstruct3DPattern(horizontalPattern, verticalPattern, false);

        // Print a sample value
        System.out.println("Cross-Weighted (Theta=45, Phi=90): " + patternProduct[45][90] + " dB");
        System.out.println("Summed (Theta=45, Phi=90): " + patternSum[45][90] + " dB");
    }
}