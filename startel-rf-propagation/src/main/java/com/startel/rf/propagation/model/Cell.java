package com.startel.rf.propagation.model;

/**
 * @Author：ranchl
 * @Date：2025/4/19 17:49
 */
public class Cell {
    /**
     * 小区名称
     */
    String name;
    /**
     * 所属发射机
     */
    String transmitter;
    /**
     * 载波类型
     */
    String carrier;
    String physicalCellId;
    Integer pssId;
    Integer sssId;
    Double cellIndividualOffset = 0.0;
    Double cellSelectionThreshold = 20.0;
    Double diversitySupportDL = 7.0;
    Double additionalDLNoiseRise = 0.0;
    Double trafficLoadDL = 100.0;
    Double maxTrafficLoadUL = 100.0;
    Integer numMimoUsers = 1;
    Integer numUsers = 1;
    String radioEquipment = "5G NR Radio Equipment";
    Double handoverMargin = 4.0;
    String layer = "Small Cell Layer";
    Double maxPower;
    Double minSsRsrp;
    Double sssEpre;
    Double pssEpreOffset = 0.0;
    Double pbchRpreOffset = 0.0;
    Double pdcchRpreOffset = 0.0;
    Double pdcchOverheadOfdmSyms = 1.0;
    Double pdschCsiRsEpreOffset  = 0.0;
    Integer numOfRequiredPrachRsi = 1;
    String scheduler = "Proportional Fair";
    Double diversitySupportUl = 7.0;
    Double additionalUlNoiseRise = 0.0;
    Double fractionalPowerControlFactor = 1.0;
    Double trafficLoadUl = 100.0;
    Double maxTrafficLoadUl = 100.0;
    Integer mumOfMuMimoUsers = 1;
    Double ulNoiseRise = 0.0;
    String ssPbchNumerology = "0 (15 kHz)";
    String ssPbchPeriodicity = "5 ms";
    String ssPbchOfdmSymbols = "{2,8}+14n [Lmax=4]";
    String trafficNumerology = "0 (15 kHz)";
    Double TddDlOfdmSymbols = 50.0;
}
