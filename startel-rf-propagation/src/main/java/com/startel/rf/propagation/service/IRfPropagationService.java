package com.startel.rf.propagation.service;

import com.startel.common.core.domain.AjaxResult;
import com.startel.rf.propagation.model.PropagationResult;

import java.util.List;

/**
 * @Author：ranchl
 * @Date：2024/11/26 09:37
 */
public interface IRfPropagationService {

    public AjaxResult interplot(int W, int SC, int NUM_scs, String FR, String str_f, double p_slot, String str_slot,String str_m, double v, String strL, double W0,
                                String propagationModel, double transmitterX, double transmitterY, double transmitterH,
                                double frequency, double minRxPower, double noiseFactor, double dlInterMargin,
                                double txGain, double txAzimuth, double txTilt, double spacingX, double spacingY, double spacingZ, double maxHeight);

    public double calcRadius(String propagationModel, double transmitterH, double receiverH, double streetWidth, double buildingHeight,
                             double frequency, double txPower, double minRxPower);

    public double calcMaxRadius(double transmitterH, double frequency, double txPower, double txGain);

    public double calcRsrp(double transmitterX, double transmitterY, double transmitterH,
                           double receiverX, double receiverY, double receiverH,
                           double frequency, double lossBody, double lossOther,
                           double txPower, double txGain, double txAzimuth, double txTilt, double dmax);

    public double calcRsrpInLAA(PropagationResult result, double transmitterX, double transmitterY, double transmitterH,
                                double receiverX, double receiverY, double receiverH,
                                double frequency, double lossBody, double lossOther,
                                String txAntennaBeamModel,
                                double txPower, double txAzimuth, double txTilt,
                                double rxAzimuth, double rxTilt, double rxGain, double rxSystemLoss, double rxLossAntennuation,
                                double electricalAzimuth, double dmax,double txMaxGain);

    public double los389UMA(double transmitterX, double transmitterY, double transmitterH,
                            double receiverX, double receiverY, double receiverH,
                            double frequency, double dMax);

    public double los389UMA(PropagationResult result, double transmitterX, double transmitterY, double transmitterH,
                            double receiverX, double receiverY, double receiverH,
                            double frequency, double dMax);

    public double los389UMI(double transmitterX, double transmitterY, double transmitterH,
                            double receiverX, double receiverY, double receiverH,
                            double frequency, double dMax);

    public double losLdlSPM(double transmitterX, double transmitterY, double transmitterH,
                                  double receiverX, double receiverY, double receiverH,
                                  double diffractionLoss);
    public AjaxResult readRSRPResultFile(String outputFilePath);

    public double getRSRP(double x, double y, double z);
}
