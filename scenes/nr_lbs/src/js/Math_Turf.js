/**
 * 计算两点对于正北方向的朝向角度 [0,360]
 * @param {*} start format:[120, 30]
 * @param {*} end
 * 
 */
var bearing = function(start,end){
    var Fobj = {
        brea: () => {
            const a = Math.sin(lon2 - lon1) * Math.cos(lat2);
            const b = Math.cos(lat1) * Math.sin(lat2) -
                      Math.sin(lat1) * Math.cos(lat2) * Math.cos(lon2 - lon1);
            return Fobj.RtD(Math.atan2(a, b));
        },
        RtD: (r) => {
            const degrees = r % (2 * Math.PI);
            return degrees * 180 / Math.PI < 0 ? 360 + degrees * 180 / Math.PI:degrees * 180 / Math.PI;
        }
    }
    
    var rad = Math.PI / 180
    var lat1 = start[1] * rad
    var lat2 = end[1] * rad
    var lon1 = start[0] * rad
    var lon2 = end[0] * rad   
    return parseInt(Fobj.brea())
}


export default bearing
