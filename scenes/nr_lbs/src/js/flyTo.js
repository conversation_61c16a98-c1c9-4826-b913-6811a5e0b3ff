
//**       @params 
    //      let params = {
    //          lon: 121.4684,    //  经度
    //          lat: 31.2230,     //  维度
    //          height: 2000,     //  高度
    //          viewer:viewer,    //  载体
    //          heading:0,        //  偏移
    //          pitch:-88,
    //          roll:0
    //        }
//*/

var flyTos = function (location) {
  var centeropt = {
    x: location.lon,
    y: location.lat,
    z: location.height,
    viewer: location.viewer,
    heading: location.heading,
    pitch: location.pitch,
    roll: location.roll,
  }
  // var centeropt = { "y": coordinate.x, "x": coordinate.y, "z": 14000, "heading":2.4,"pitch":-89.9,"roll":0 };  一个合适的角度
  centeropt.viewer.camera.flyTo({
    destination: Cesium.Cartesian3.fromDegrees(
      centeropt.x,
      centeropt.y,
      centeropt.z || 0
    ), //经度、纬度、高度
    orientation: {
      heading: Cesium.Math.toRadians(centeropt.heading || 0), //绕垂直于地心的轴旋转
      pitch: Cesium.Math.toRadians(
        centeropt.pitch || -Cesium.Math.PI_OVER_FOUR
      ), //绕纬度线旋转
      roll: Cesium.Math.toRadians(centeropt.roll || 0), //绕经度线旋转
    },
    duration: 3,
  })
}

export default flyTos
