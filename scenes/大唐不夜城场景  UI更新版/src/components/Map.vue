<template>
  <div
    v-loading="loading"
    element-loading-text="正在加载模型数据..."
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.8)"
    style="width: 100%; height: 100%"
  >
    <div id="box"></div>

    <div class="left" v-show="home">
      <div class="container">
        <div class="container-title">
          <span>重点场馆信号覆盖情况</span>
        </div>
        <div class="container-content el-list">
          <el-row>
            <el-col :span="5">场馆名称</el-col>
            <el-col :span="5">基站数量</el-col>
            <el-col :span="5">忙时负载</el-col>
            <el-col :span="5">覆盖情况</el-col>
            <el-col :span="4">查看</el-col>
          </el-row>
          <div>
            <el-row v-for="item,index in fakeData" :key="index">
              <el-col :span="5"> {{ item.name }} </el-col>
              <el-col :span="5"> {{item.num}} </el-col>
              <el-col :span="5"> {{item.prect}} </el-col>
              <el-col :span="5"> 
                <!-- {{item.type}} -->
                <div class="solt-label Cpor" 
                :style="{ background: item.type==0?'#00F6C530':item.type==1?'#00DAFF30':item.type==2?'#FFB20030':'#E7565630',
                          color: item.type==0?'#00F6C5':item.type==1?'#00DAFF':item.type==2?'#FFB200':'#E75656',
                          borderColor: item.type==0?'#00F6C550':item.type==1?'#00DAFF50':item.type==2?'#FFB20050':'#E7565650'}
                ">
                  {{ item.type==0?'优':item.type==1?'良好':item.type==2?'一般':'较差'}}
                </div>
              </el-col>
              <el-col :span="4"><img src="~@/assets/UIpic/eye.png" alt="" @click="flyToStation(item)"></el-col >
            </el-row>
          </div>
        </div>
      </div>
      <div class="container">
        <div class="container-title">
          <span>基站网元孪生</span>
        </div>
        <div class="container-content">
          <div id="barChart" style="width:100%;height:100%"></div>
        </div>
      </div>
      <div class="container">
        <div class="container-title">
          <span>基站告警信息</span>
        </div>
        <div class="container-content el-list">
          <el-row>
            <el-col :span="6">日期</el-col>
            <el-col :span="6">时间</el-col>
            <el-col :span="5">报警级别</el-col>
            <el-col :span="7">处理情况</el-col>
          </el-row>
          <div>
            <el-row v-for="item,index in fakeData2" :key="index">
              <el-col :span="6"> {{ item.name }} </el-col>
              <el-col :span="6"> {{item.num}} </el-col>
              <el-col :span="5" :style="{color: item.prect==1?'#00DAFF':item.prect==2?'#FFB200':'#E75656',}"> 
                {{ item.prect==1?'低':item.prect==2?'中':'高'}} 
              </el-col>
              <el-col :span="7"> 
                <!-- {{item.type}} -->
                <div class="solt-label Cpor" 
                :style="{ background: item.type==1?'#00DAFF30':item.type==2?'#FFB20030':'#E7565630',
                          color: item.type==1?'#00DAFF':item.type==2?'#FFB200':'#E75656',
                          borderColor: item.type==1?'#00DAFF50':item.type==2?'#FFB20050':'#E7565650'}
                ">
                  {{ item.type==1?'已处理':item.type==2?'处理中':'未处理'}} 
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </div>
    </div>

    <div class="right" v-show="home">
      <div class="container" style="height:60%">
        <div class="container-title">
          <span>室外宏站室内信号孪生</span>
        </div>

        <div class="container-content boxOfrsrp">
          <div>

            <div class="pie_title"> 
              <div>
                <div class="pie_title_icon"></div>
                <span> 室内仿真RSRP面积统计</span>
              </div>
              <div>
                <select name="rsrp" id="sclectOfRSRP" @change="lineChartChange">
                  <option v-for="t,i in hOfrsrp" :key="i" :value="t">{{ t==0?'无高度':t+'m' }}</option>
                </select>
              </div>
            </div>

            <div class="pie_content">
                <div div id="lineOfRSRP" style="width:100%;height:100%"></div>
            </div>
          </div>
          
          <div>
            <div class="pie_title"> 
              <div>
                <div class="pie_title_icon"></div>
                <span> 室内仿真SINR面积统计</span>
              </div>
              <div>
                <select name="sinr" id="sclectOfSINR" @change="lineChartChange">
                  <option v-for="t,i in hOfrsrp" :key="i" :value="t">{{ t==0?'无高度':t+'m' }}</option>
                </select>
              </div>
            </div>
            <div class="pie_content">
                <div id="lineOfSINR" style="width:100%;height:100%"></div>
            </div>
          </div>
        </div>

      </div>
      <div class="container event" style="height:40%">
        <div class="container-title">
          <span>网络事件孪生</span>
        </div>
        <div class="container-content el-list">
          <el-row>
            <el-col :span="5">区域名称</el-col>
            <el-col :span="5">楼层</el-col>
            <el-col :span="5">质差面积</el-col>
            <el-col :span="5">质差因素</el-col>
            <el-col :span="4">查看</el-col>
          </el-row>
          <div>
            <el-row v-for="item,index in fakeData3" :key="index">
              <el-col :span="6"> {{ item.name }} </el-col>
              <el-col :span="6"> {{item.num}} </el-col>
              <el-col :span="6"> {{item.prect}} </el-col>
              <el-col :span="6"> {{item.type}} </el-col>
              <el-col :span="4"><img src="~@/assets/UIpic/eye.png" alt=""></el-col>
            </el-row>
          </div>
        </div>
      </div>
    </div>

    <!-- //  这个是底部按钮 -->
    <div class="bottom Cpoa">
      <div v-for="t,i in btnB" :key="i" 
      :style="'background: url('+t.path+') no-repeat center center;'" 
      class="btnB" :class="t.status?'hsv':''" @click="btnB_click(t)"> 
        <span> {{ t.label }} </span>
      </div>
    </div>

    <!-- //  这个是点小区的卡片 -->
    <div class="card tianxian" v-if="cards.tianxian">
      <div class="card_title">
        <div class="card_title_content">
          <span>天线参数孪生</span>
        </div>
        <div class="card_title_icon">
        </div>
      </div>
      <div class="card_content">
        <ul>
          <li v-for="t,i in tableData" :key="i">
            <span>{{t.key}}:</span> <span>{{t.value}}</span>
          </li>
        </ul>

        <div class="block">
          <img src="../assets/UIpic/slider-angle.png" alt="">
          <span class="demonstration">机械倾角({{slider.angle}}m)</span>
          <el-slider v-model="slider.angle" :min="-3" :max="3" @input="azimuthAdjustment"></el-slider>
        </div>

        <div class="block">
          <img src="../assets/UIpic/slider-rolate.png" alt="">
          <span class="demonstration">方位角({{slider.rolate}}m)</span>
          <el-slider v-model="slider.rolate" :min="-3" :max="3" @input="mechanicalAdjustment"></el-slider>
        </div>

      </div>
    </div>

    <!-- //  这个是点基站图表卡片 -->
    <div class="card station" v-if="cards.station">
      <div class="card_title">
        <div class="card_title_content">
          <span>基站孪生</span>
        </div>
        <div class="card_title_icon">
        </div>
      </div>
      <div class="card_content">
        <ul>
          <li v-if="SiteTableData.length > 0 && SiteTableData[0].value == '767107'">
            <span>全景图</span> <button @click="loadIframe()">查看</button>
          </li>
          <li v-for="t,i in SiteTableData" :key="i">
            <span>{{t.key}}:</span> <span>{{t.value}}</span>
          </li>
        </ul>

      </div>
    </div>

    <!-- //  这个是方案孪生卡片 -->
    <div class="card fangan" v-if="btnB.filter(t=>{return t.name=='fangan'})[0].status">
      <div class="card_title">
        <div class="card_title_content">
          <span>方案孪生</span>
        </div>
        <!-- <div class="card_title_icon">
          <img src="../assets/UIpic/close.png" alt="" @click="card_up($event,1)">
        </div> -->
      </div>
      <div class="card_content">
        <div style="display: flex;flex-direction: column; justify-content: space-around;">
          <div class="block">
            <img src="../assets/UIpic/slider-x.png" alt="">
            <span class="demonstration">横向切割({{ crosswiseMI }}m)</span>
            <el-slider v-model="value1" :min="slider1_min" :max="slider1_max" @input="crosswise"></el-slider>
          </div>
          <div class="block">
            <img src="../assets/UIpic/slider-y.png" alt="">
            <span class="demonstration">纵向切割({{ lengthwaysMI }}m)</span>
            <el-slider v-model="value2" :min="slider2_min" :max="slider2_max" @input="lengthways"></el-slider>
          </div>
          <div class="block">
            <img src="../assets/UIpic/slider-z.png" alt="">
            <span class="demonstration">水平切割({{ showMI }}m)</span>
            <el-slider v-model="value3" :min="-40" :max="40" @input="vertical"></el-slider>
          </div>
          <div class="block">
            <img src="../assets/UIpic/slider-size.png" alt="">
            <span class="demonstration">体元大小({{ pointsSize }})</span>
            <el-slider v-model="value4" :min="1" :max="20" @input="pointSize"></el-slider>
          </div>
        </div>
      </div>
    </div>

    <!-- //  这个是机房孪生卡片 -->
    <div class="card jifang" v-if="btnB.filter(t=>{return t.name=='jifang'})[0].status">
      <div class="card_title">
        <div class="card_title_content">
          <span>机房孪生</span>
        </div>
      </div>
      <div class="card_content">
        <div class="switch">
            <div>
              <img src="~@/assets/UIpic/slot_jifang.png" alt=""><span> 展开机房</span>
            </div>
            <div>
              <span :style="!switchs.mimo ? 'color:#F04646' : 'color: #c7c7c7;'">关</span>
                <el-switch
                v-model="switchs.mimo"
                @change="openServeRoom"
                active-color="#13ce66"
                inactive-color="#ff4949">
              </el-switch>
              <span :style="switchs.mimo ? 'color:#0ACF97' : 'color: #c7c7c7;'">开</span>
            </div>
        </div>
        <el-row>
            <div><img src="~@/assets/UIpic/slot_camare.png" alt=""><span>视角定位</span> </div>
            <div>
              <button @click="location('办公区')">办公室</button>
              <button @click="location('监控室')">监控区</button>
              <button @click="location('机房')">机房</button>
            </div>
        </el-row>
      </div>
    </div>

    <!-- //  这个是沉浸式仿真卡片  -->
    <div class="card chenjin" v-if="btnB.filter(t=>{return t.name=='chenjin'})[0].status">
      <div class="card_title">
        <div class="card_title_content">
          <span>沉浸式孪生</span>
        </div>
        <div class="card_title_icon">
          <img src="../assets/UIpic/boxup.png" alt="" @click="card_up($event,'')">
        </div>
      </div>
      <div class="card_content">
        <el-row>
          <div> <img src="../assets/UIpic/slot_key.png" alt=""><span>键盘操控车</span> </div>
          <div class="slot_key">
            <button @click="controlCar" v-if="carOfUser">加载车(人)</button>
            <button @click="controlCar" v-else>取消</button>
            <button @click="cancelFollow">取消跟随</button>
          </div>
        </el-row>
        <div class="el-list">
          <el-row>
            <el-col :span="6">孪生体名称</el-col>
            <el-col :span="6">时速(km/h)</el-col>
            <el-col :span="6">实时位置</el-col>
            <el-col :span="6">跟随</el-col>
          </el-row>
          <div>
            <el-row v-for="item,index in carModelList" :key="index">
              <el-col :span="6"> {{item.name}} </el-col>
              <el-col :span="6"> {{item.speed}} </el-col>
              <el-col :span="6"> {{item.location.slice(0,item.location.indexOf(','))+'\n'+item.location.slice(item.location.indexOf(',')+1)}} </el-col>
              <el-col :span="6"><img src="~@/assets/UIpic/table_tracked.png" alt="" @click="followCar(item)"></el-col >
            </el-row>
          </div>
        </div>
      </div>
    </div>

    <!-- //  这个是沉浸式仿真配置卡片 -->
    <div class="card carConfig" v-if="modelTiaozheng">
      <div class="card_title">
        <div class="card_title_content">
          <span>沉浸式仿真配置</span>
        </div>
      </div>
      <div class="card_content">
        <div class="switch">
            <div>
              <img src="~@/assets/UIpic/slot_jifang.png" alt=""><span> 轨迹显示方式</span>
            </div>
            <div>
              <span :style="!trajectoryType ? 'color:#25E7B0' : 'color: #c7c7c7;'">点</span>
                <el-switch
                v-model="trajectoryType"
                @change="displayUsage"
                active-color="#13ce66"
                inactive-color="#ff4949">
              </el-switch>
              <span :style="trajectoryType ? 'color:#0ACF97' : 'color: #c7c7c7;'">线</span>
            </div>
        </div>
        <div class="switch">
            <div>
              <img src="~@/assets/UIpic/slot_jifang.png" alt=""><span> 直达径</span>
            </div>
            <div>
              <span :style="!refractionLine ? 'color:#F04646' : 'color: #c7c7c7;'">关</span>
                <el-switch
                v-model="refractionLine"
                @change="refractionLineShow"
                active-color="#13ce66"
                inactive-color="#ff4949">
              </el-switch>
              <span :style="refractionLine ? 'color:#0ACF97' : 'color: #c7c7c7;'">开</span>
            </div>
        </div>
      </div>
    </div>

    <!-- //  这个是采样点详细卡片 -->
    <div class="card SamplingList" v-if="electricalList.length > 0">
      <div class="card_title">
        <div class="card_title_content">
          <span>采样点详细</span>
        </div>
        <div class="card_title_icon">
          <img src="../assets/UIpic/boxup.png" alt="" @click="card_up($event,'')">
        </div>
      </div>

      <div class="card_content">
        <div> 
          <button @click="exportExcel(1)">导出仿真结果</button>
          <button @click="analysis">生成分析结果</button>
        </div>
        <div class="el-list">
          <el-row>
            <el-col :span="1">序号</el-col>
            <el-col :span="3">时间</el-col>
            <el-col :span="3">经度</el-col>
            <el-col :span="3">纬度</el-col>
            <el-col :span="3">RSRP(dBm)</el-col>
            <el-col :span="3">SINR(dB)</el-col>
            <el-col :span="4">上行速率(kbps)</el-col>
            <el-col :span="4">下行速率(kbps)</el-col>
          </el-row>
          <div>
            <el-row v-for="item,index in electricalList" :key="index">
              <el-col :span="1"> {{electricalList.length - index}} </el-col>
              <el-col :span="3"> {{item.date}} </el-col>
              <el-col :span="3"> {{item.lon}} </el-col>
              <el-col :span="3"> {{item.lat}} </el-col>
              <el-col :span="3"> {{item.rsrp}} </el-col>
              <el-col :span="3"> {{item.sinr}} </el-col>
              <el-col :span="4"> {{item.ul}} </el-col>
              <el-col :span="4"> {{item.dl}} </el-col>
            </el-row>
          </div>
        </div>
      </div>
    </div>

    <!-- //  这个是分析结果卡片 -->
    <div class="card fangzhenresult Cpoa" v-show="fxjgShow">
        <div class="card_title">
          <div class="card_title_content">
            <span>仿真分析结果</span>
          </div>
          <div class="card_title_icon">
            <img src="../assets/UIpic/close.png" alt="" @click="resultCard($event,1)">
          </div>
        </div>
      
        <div class="card_content">
          <el-row>
            <el-col :span="12">
              <div>
                <div class="scale">
                  <div id="resultChart" style="width:100%;height: 100%;"></div>
                </div>
                <ul>
                  <li v-for="t,i in legendOfRSRP" :key="i">
                    <div>
                      <span :style="'background:'+t.itemStyle.color"></span> <span>{{t.name}}</span>
                    </div> 
                    <div>{{t.percnt}}</div>
                  </li>
                </ul>
              </div>
            </el-col>
            <el-col :span="12">
              <div>
                <div class="scale">
                  <div id="resultChart2" style="width:100%;height: 100%;"></div>
                </div>
                <ul>
                  <li v-for="t,i in legendOfSINR" :key="i">
                    <div>
                      <span :style="'background:'+t.itemStyle.color"></span> <span>{{t.name}}</span>
                    </div> 
                    <div>{{t.percnt}}</div>
                  </li>
                </ul>
              </div>
            </el-col>
          </el-row>
          <el-row>
            <div> <span>⋮ 路线数据汇总分析</span> <button @click="exportExcel(2)">导出汇总结果</button></div>
            <div class="el-list">
                <!-- <el-row>
                  <el-col :span="2">总数</el-col>
                  <el-col :span="4">RSRP(-70dBm较好)</el-col>
                  <el-col :span="4">RSRP(-80dBm好)</el-col>
                  <el-col :span="4">RSRP(-90dBm普通)</el-col>
                  <el-col :span="4">SINR(20dB较好)</el-col>
                  <el-col :span="3">SINR(10dB好)</el-col>
                  <el-col :span="3">SINR(0dB普通)</el-col>
                </el-row>
                <div>
                  <el-row v-for="item,index in jgfx_DataList" :key="index">
                    <el-col :span="2">{{item.zhibiao_sum}}</el-col>
                    <el-col :span="4">{{item.rsrp_worst_cnt}}</el-col>
                    <el-col :span="4">{{item.rsrp_bad_cnt}}</el-col>
                    <el-col :span="4">{{item.rsrp_ordinary_cnt}}</el-col>
                    <el-col :span="4">{{item.rsrp_good_cnt}}</el-col>
                    <el-col :span="3">{{item.rsrp_better_cnt}}</el-col>
                    <el-col :span="3">{{item.rsrp_best_cnt}}</el-col>
                  </el-row>
                </div> -->
                
                <el-table :data="jgfx_DataList" style="width: 100%;" id="jgfx_table">
                    <el-table-column
                      prop="zhibiao_sum"
                      label="总数">
                    </el-table-column>
                    <el-table-column
                      prop="rsrp_worst_cnt"
                      v-if = "jgfx_DataList.length > 0 && jgfx_DataList[0].rsrp_worst_cnt1 != 0"
                      label="RSRP(-60dBm极好)">
                    </el-table-column>
                    <el-table-column
                      prop="rsrp_bad_cnt"
                      v-if = "jgfx_DataList.length > 0 && jgfx_DataList[0].rsrp_bad_cnt != 0"
                      label="RSRP(-70dBm较好)">
                    </el-table-column>
                    <el-table-column
                      prop="rsrp_ordinary_cnt"
                      v-if = "jgfx_DataList.length > 0 && jgfx_DataList[0].rsrp_ordinary_cnt1 != 0"
                      label="RSRP(-80dBm好)">
                    </el-table-column>
                     <el-table-column
                      prop="rsrp_good_cnt"
                      v-if = "jgfx_DataList.length > 0 && jgfx_DataList[0].rsrp_good_cnt1 != 0"
                      label="RSRP(-90dBm普通)">
                    </el-table-column>
                     <el-table-column
                      prop="rsrp_better_cnt"
                      v-if = "jgfx_DataList.length > 0 && jgfx_DataList[0].rsrp_better_cnt1 != 0"
                      label="RSRP(-100dBm较差)">
                    </el-table-column>
                     <el-table-column
                      prop="rsrp_best_cnt"
                      v-if = "jgfx_DataList.length > 0 && jgfx_DataList[0].rsrp_best_cnt1 != 0"
                      label="RSRP(-110dBm极差)">
                    </el-table-column>
                     <el-table-column
                      prop="SINR_worst_cnt"
                      v-if = "jgfx_DataList.length > 0 && jgfx_DataList[0].SINR_worst_cnt1 != 0"
                      label="SINR(25dB(极好))">
                    </el-table-column>
                    <el-table-column
                      prop="SINR_bad_cnt"
                      v-if = "jgfx_DataList.length > 0 && jgfx_DataList[0].SINR_bad_cnt1 != 0"
                      label="SINR(20dB较好)">
                    </el-table-column>
                    <el-table-column
                      prop="SINR_ordinary_cnt"
                      v-if = "jgfx_DataList.length > 0 && jgfx_DataList[0].SINR_ordinary_cnt1 != 0"
                      label="SINR(10dB好)">
                    </el-table-column>
                     <el-table-column
                      prop="SINR_good_cnt"
                       v-if = "jgfx_DataList.length > 0 && jgfx_DataList[0].SINR_good_cnt1 != 0"
                      label="SINR(0dB普通)">
                    </el-table-column>
                     <el-table-column
                      prop="SINR_better_cnt"
                      v-if = "jgfx_DataList.length > 0 && jgfx_DataList[0].SINR_better_cnt1 != 0"
                      label="SINR(-5dB较差)">
                    </el-table-column>
                     <el-table-column
                      prop="SINR_best_cnt"
                      v-if = "jgfx_DataList.length > 0 && jgfx_DataList[0].SINR_best_cnt1 != 0"
                      label="SINR(-10dB极差)">
                    </el-table-column>
                </el-table>
            </div>
          </el-row>
        </div>

    </div>

    <!-- //  这个是沉浸式分析关联基站卡片 -->
    <div class="card stationOfFenxi" v-show="ImmersiveDataList.length > 0">
      <div class="card_title">
          <div class="card_title_content">
            <span>沉浸式孪生关联基站</span>
          </div>
          <!-- <div class="card_title_icon">
            <img src="../assets/UIpic/close.png" alt="" @click="resultCard($event,1)">
          </div> -->
        </div>

        <div class="card_content">
          <ul>
            <li v-for="t,i in ImmersiveDataList" :key="i">
              <span>{{t.key}}:</span> <span>{{t.value}}</span>
            </li>
          </ul>
        </div>
    </div>

    <!-- 沉浸式仿真 -->
    <el-dialog title="沉浸式孪生" :visible.sync="dialogVisible" width="25%">
      <el-form :model="form" style="margin-left:20px">
        <el-form-item label="选择分析模型:">
          <el-radio-group v-model="form.model">
              <el-radio label="人"></el-radio>
              <el-radio label="车"></el-radio>
          </el-radio-group>
        </el-form-item>
         <el-form-item label="轨迹显示方式:">
          <el-radio-group v-model="form.type">
            <el-radio label="点"></el-radio>
            <el-radio label="线"></el-radio>
         </el-radio-group>
        </el-form-item>
        <el-form-item label="配置时速:">
          <el-input v-model="form.speed" type="number" style="position:absolute;top:0px;left:98px"></el-input>
        </el-form-item>
        <el-form-item label="仿真业务:">
          <el-select v-model="form.region" placeholder="请选择仿真业务" style="position:absolute;top:0px;left:98px">
            <el-option label="4K高清视频" value="4kvideo"></el-option>
            <el-option label="8K超高清视频" value="8kvideo"></el-option>
            <el-option label="VR/AR" value="VRAR"></el-option>
            <el-option label="云游戏" value="yunGame"></el-option>
            <el-option label="高速下载" value="download"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer" style="margin:20px 0 20px 0">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="placeModel"
          >确 定</el-button
        >
      </div>
    </el-dialog>
    <!--  -->
    <div class="hud2" :style="{ color: rsrpColor }" v-show="shijiaoShow2">
      <span style="font-family: 'pmzd';font-size: 22px;">业务质量:</span><span style="font-family: 'pmzd';font-size: 22px;">{{RSRPText}}</span><br/>
      <span class="span1">RSRP(dBm):</span><span class="span1">{{ rsrpNum }}</span>
    </div>

    <!-- 全景图 -->
    <div class="PanoramaClass Cpoa"  v-if="PanoramaShow">
      <div @click="PanoramaShow = false"></div>
      <iframe src='http://*************:59083/vtour/' frameborder="0"></iframe>
    </div>

    <themeheader @sendDoms="sendDoms"/>








    <!-- 全屏按钮 -->
    <!-- <div class="screen" v-show="false" @click="largeScreenShow = !largeScreenShow" :style="{ left: largeScreenShow ? '400px' : '20px' }">
      <img src="../assets/sx.png" alt="">
    </div> -->
    <!-- <div class="left" v-show="largeScreenShow">
      <div class="box-item">
        <div class="content-title">
          <div class="title-up">
            <img
              src="../assets/cotnent-title-5.png"
              alt=""
              style="width: 29px; height: 29px"
            />
            <span>重点场馆信号覆盖情况</span>
          </div>
          <div class="title-down">WIRELESS SIGNAL COVERAGE</div>
        </div>
        <div class="content-box">
          <table>
            <thead>
              <tr>
                <td>场馆名称</td>
                <td>基站数量</td>
                <td>覆盖情况</td>
                <td>忙时负载</td>
                <td>查看</td>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>西安美术馆</td>
                <td>2</td>
                <td>良好</td>
                <td>78%</td>
                <td><a href="#" style="color:#70e6ff">查看</a></td>
              </tr>
              <tr>
                <td>大雁塔</td>
                <td>2</td>
                <td>良好</td>
                <td>89%</td>
                <td><a href="#" style="color:#70e6ff">查看</a></td>
              </tr>
              <tr>
                <td>银泰百货</td>
                <td>1</td>
                <td style="color: #fe9b00">一般</td>
                <td>96%</td>
                <td><a href="#" style="color:#70e6ff">查看</a></td>
              </tr>
              <tr>
                <td>西安音乐厅</td>
                <td>2</td>
                <td>良好</td>
                <td>63%</td>
                <td><a href="#" style="color:#70e6ff">查看</a></td>
              </tr>
              <tr>
                <td>陕西大剧院</td>
                <td>2</td>
                <td>良好</td>
                <td>72%</td>
                <td><a href="#" style="color:#70e6ff">查看</a></td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div class="box-item">
        <div class="content-title">
          <div class="title-up">
            <img
              src="../assets/cotnent-title-2.png"
              alt=""
              style="width: 29px; height: 29px"
            />
            <span>基站网元孪生</span>
          </div>
          <div class="title-down">BASE STATION DISTRIBUTION</div>
        </div>
        <div class="">
          <div id="charts1" style="width: 320px; height: 160px"></div>
        </div>
      </div>
      <div class="box-item">
        <div class="content-title">
          <div class="title-up">
            <img
              src="../assets/cotnent-title-3.png"
              alt=""
              style="width: 29px; height: 29px"
            />
            <span>基站告警信息</span>
          </div>
          <div class="title-down">BASE STATION ALARM INFORMATION</div>
        </div>
        <div class="content-box">
          <table cellspacing="0">
            <thead>
              <tr>
                <td>日期</td>
                <td>时间</td>
                <td>报警级别</td>
                <td>处理情况</td>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>2.11</td>
                <td>10:30:00</td>
                <td>中</td>
                <td>已处理</td>
              </tr>
              <tr>
                <td>2.11</td>
                <td>10:30:00</td>
                <td>低</td>
                <td style="color: #0affb1">未处理</td>
              </tr>
              <tr>
                <td>2.11</td>
                <td>10:30:00</td>
                <td>高</td>
                <td style="color: #ff8f1c">处理中</td>
              </tr>
              <tr>
                <td>2.11</td>
                <td>10:30:00</td>
                <td>中</td>
                <td>已处理</td>
              </tr>
              <tr>
                <td>2.11</td>
                <td>10:30:00</td>
                <td>中</td>
                <td>已处理</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div> -->
     <!-- <div class="right" v-show="largeScreenShow">
      <div class="box-item2">
        <div class="content-title2">
          <div class="title-up">
            <img
              src="../assets/cotnent-title-4.png"
              alt=""
              style="width: 29px; height: 29px"
            />
            <span>室外宏站室内信号孪生</span>
          </div>
          <div class="title-down">SIGNAL INFOMATION</div>
        </div>
        <div class="content-box2">
          <div class="box4">
            <div class="box1-title">
              <span class="Ctitle">室内仿真RSRP面积统计</span>
              <el-select v-model="RSRPHeight" filterable placeholder="请选择" @change="changeAreaCharts('rsrp')">
                <el-option
                  v-for="item in allHeight_select"
                  :key="item.key"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </div>
            <div id="areaChart_RSRP" style="width:100%; height:180px"></div>
          </div>
          <div class="box5">
            <div class="box1-title">
              <span class="Ctitle">室内仿真SINR面积统计</span>
              <el-select v-model="SINRHeight" filterable placeholder="请选择" @change="changeAreaCharts('sinr')">
                <el-option
                  v-for="item in allHeight_select"
                  :key="item.key"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </div>
            <div id="areaChart_SINR" style="width:100%; height:180px"></div>
          </div>
        </div>
      </div>
      <div class="box-item">
        <div class="content-title">
          <div class="title-up">
            <img
              src="../assets/cotnent-title-1.png"
              alt=""
              style="width: 29px; height: 29px"
            />
            <span>室内质差区域孪生</span>
          </div>
          <div>
          </div>
        </div>
        <div class="content-box ">
          <table>
            <thead>
              <tr>
                <td>区域名称</td>
                <td>楼层</td>
                <td>质差面积</td>
                <td>质差因素</td>
                <td>查看详情</td>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>西安美术馆</td>
                <td>2</td>
                <td>865㎡</td>
                <td>信号干扰</td>
                <td><a href="#" style="color:#70e6ff">查看</a></td>
              </tr>
              <tr>
                <td>大雁塔</td>
                <td>2</td>
                <td>322㎡</td>
                <td>信号干扰</td>
                <td><a href="#" style="color:#70e6ff">查看</a></td>
              </tr>
              <tr>
                <td>银泰百货</td>
                <td>1</td>
                <td>236㎡</td>
                <td>信号干扰</td>
                <td><a href="#" style="color:#70e6ff">查看</a></td>
              </tr>
              <tr>
                <td>西安音乐厅</td>
                <td>2</td>
                <td>725㎡</td>
                <td>信号干扰</td>
                <td><a href="#" style="color:#70e6ff">查看</a></td>
              </tr>
              <tr>
                <td>陕西大剧院</td>
                <td>2</td>
                <td>358㎡</td>
                <td>信号干扰</td>
                <td><a href="#" style="color:#70e6ff">查看</a></td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div> -->
    <!-- 实时信号指标 -->
     <!-- <div class="right" v-show="electricalList.length > 0 && AnalysisCharsShow">
      <div class="box-item">
        <div class="content-title">
          <div class="title-up">
            <img
              src="../assets/cotnent-title-1.png"
              alt=""
              style="width: 29px; height: 29px"
            />
            <span>RSRP实时指标</span>
          </div>
          <div>
          </div>
        </div>
        <div class="content-box">
           <div id="RSRPCharts" style="width: 385px; height: 215px;"></div>
        </div>
      </div>
      <div class="box-item">
        <div class="content-title">
          <div class="title-up">
            <img
              src="../assets/cotnent-title-1.png"
              alt=""
              style="width: 29px; height: 29px"
            />
            <span>SINR实时信噪比</span>
          </div>
          <div>
            <div id="SINRCharts" style="width: 385px; height: 221px; top:-15px !important; left: 5px !important;"></div>
          </div>
        </div>
        <div class="content-box ">
        </div>
      </div>
      <div class="box-item">
        <div class="content-title">
          <div class="title-up">
            <img
              src="../assets/cotnent-title-1.png"
              alt=""
              style="width: 29px; height: 29px"
            />
            <span>实时传输速率</span>
          </div>
          <div>
            <div id="upDownKbps" style="width: 385px; height: 221px;  top:-15px !important; left: -18px !important"></div>
          </div>
        </div>
        <div class="content-box ">
        </div>
      </div>
    </div> -->
    <!-- 导航窗口 -->
    <div
      id="box1"
      :style = "{bottom: height == 969 ? '115px' : '100px'}"
      v-show="shijiaoShow"
      v-loading="loadings"
      element-loading-text="正在导航"
      element-loading-spinner="el-icon-loading"
    ></div>
    <div class="hud" :style="{ color: rsrpColor }" v-show="shijiaoShow">
      <span style="font-family: 'pmzd';font-size: 22px;">业务质量:</span><span style="font-family: 'pmzd';font-size: 22px;">{{RSRPText}}</span><br/>
      <span class="span2">RSRP(dBm):</span><span class="span2">{{ rsrpNum }}</span>
    </div>
    <video id="daolu" muted autoplay loop crossorigin controls>
      <source src="../assets/carVideo.mp4" type="video/mp4" class="video_sp" />
    </video>

    <!-- 大唐不夜城按钮 -->
    <div class="btns" v-show="false">
      <div
        class="follow"
        :class="{ active: icon.icon7 }"
        @click="loadbycwy"
      >
        <div></div>
        <span>加载网元</span>
      </div>
      <img src="../assets/dot.png" style="width: 45px; height: 5px" />
      <div
        @click="loadMachineRoom"
        class="follow"
        :class="{ active: icon.icon8}"
      >
        <div></div>
        <span>机房孪生</span>
      </div>
      <img src="../assets/dot.png" style="width: 45px; height: 5px" />
      <div
        @click="pntsShow"
        class="start"
        :class="{ active: icon.icon3 }"
      >
        <div></div>
        <span>方案孪生</span>
      </div>
      <img src="../assets/dot.png" style="width: 45px; height: 5px" />
      <div
        @click="load_car"
        class="follow"
        :class="{ active: icon.icon1}"
      >
        <div></div>
        <span>沉浸式孪生</span>
      </div>
      <div
        class="selectsj"
        style="display: none"
      >
        <div
          id="manipulate"
          @click="
            firstPerson,
              shijiaoShow ? (shijiao = '第一人称') : (shijiao = '第三人称')
          "
        ></div>
        <span>{{ shijiao }}</span>
      </div>
    </div>

    <!-- 沉浸式分析指标记录 -->
    <el-card class="box-card" id="wycz3" v-show="false">
      <el-table :data="electricalList" id="exportTab">
          <el-table-column
            type="index"
            :index="indexMethod">
          </el-table-column>
          <el-table-column
            prop="date"
            label="时间"
            width="100">
          </el-table-column>
          <el-table-column
            prop="lon"
            label="经度"
            width="100">
          </el-table-column>
          <el-table-column
            prop="lat"
            label="纬度"
            width="90">
          </el-table-column>
          <el-table-column
            prop="rsrp"
            label="RSRP(dBm)"
            width="90">
          </el-table-column>
          <el-table-column
            prop="sinr"
            label="SINR(dB)"
            width="90">
          </el-table-column>
          <el-table-column
            prop="ul"
            label="上行速率(kbps)"
            width="90">
          </el-table-column>
          <el-table-column
            prop="dl"
            label="下行速率(kbps)"
            width="90">
          </el-table-column>
      </el-table>
    </el-card>
    <!-- 加载数据loading -->
    <div
      v-loading="loadingModel"
      element-loading-text="正在加载数据..."
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(0, 0, 0, 0)"
      class="loadingModelPromise"
    ></div>
  
  </div>
</template>

<script>
import chartData from '../js/data.js'
//js
import flyTos from '../js/flyTo.js'
import cesiumMap from '../js/cesiumMap.js'
// import PolylineTrailMaterialProperty from "../js/PolylineTrailMaterialProperty.js"; //线纹理
import '../js/circleRippleMaterialProperty.js' //点纹理
// import circleDiffuseMaterilaProperty from "../js/circleDiffuseMaterilaProperty.js"; //扩散圆
import '../js/dynamicWallMaterialProperty.js' //墙纹理
// import "../js/ellipsoidTrailMaterialProperty" //球纹理
import '../js/SkyBoxOnGround' //天空盒
import carlujing from '../js/carLength' //车行驶路径
import scaleMixin from "../js/scaleMixin";
//插件
import * as turf from '@turf/turf'
import * as echarts from 'echarts'
import FileSaver from 'file-saver'
import * as XLSX from 'xlsx'
//组件
import Lonlat from './lonlat.vue'
import About from './About.vue'
import themeheader from './Header.vue'
//图片
import colorss from '../assets/colors.png'
import lineColor from '../assets/line.png'
import positiveX from '../assets/sky/bk.jpg' //天空盒
import negativeX from '../assets/sky/rt.jpg' //天空盒
import positiveY from '../assets/sky/ft.jpg' //天空盒
import negativeY from '../assets/sky/lf.jpg' //天空盒
import positiveZ from '../assets/sky/up.jpg' //天空盒
import negativeZ from '../assets/sky/dn.jpg' //天空盒
import waterNormals from '../assets/waterNormals.jpg' //水面
import base from '../assets/base2.png' //基站图标
import base1 from '../assets/base3.png' //基站图标
import base2 from '../assets/base4.png' //基站图标
import base3 from '../assets/base5.png' //基站图标
import tourImg from '../assets/tour.jpg' //基站图标

let fromDegreesArrayHeights = [] //定义数组
let colors = [] //颜色数组
let pnts = [] //轨迹点存入数组
let lines = [] //轨迹线存入数组
let planeEntities = [] //存入切割面实体
let targetY = 0.0 //切割数值
let targetY2 = 0.0 //切割数值
let targetY3 = 0.0 //切割数值
let tileset //体元
let walls = [] //墙集合
let xianModel = [] //西安模型集合
let pickObject = [] //储存点击的模型
let antennaArray = [] //储存天线支架
let mimos = [] //储存波束
let WebsiteArray = [] //储存基站
let azjArray = [] //存储安装架
let txzjArray = [] //存储天线支架
let antennaAry = [] //存储天线
let billboardArray = [] //存储标签
let bycTransmitterArray = [] //储存不夜城天线
let bycSite = [] //不夜城基站
let MIMOid = [] //波束id
let jizhanshuju; //存储基站数据json
let tianxianshuju; //存储天线数据
let canvas2;
let wall //墙
let car1 = null //车
let car2 = null //车2
let xinhaolines = []
let serverRoom = null; //机房
let serverRoommodel = null; //机房
let kuixian = []; //机房馈线集合
let intervals = null //计时器
let intervals2 = null //计时器
let cameraCanshu = {} //相机参数
let rrp = {
  rrp_1: [-120, -110, 0.5],
  rrp_2: [-110, -100, 0.5],
  rrp_3: [-100, -90, 0.5],
  rrp_4: [-90, -80, 0.5],
  rrp_5: [-80, -70, 0.5],
  rrp_6: [-70, -60, 0.5],
  rrp_7: [-60, -50, 0.5],
  rrp_8: [-50, -40, 0.5],
  rrp_9: [-40, -30, 0.5],
  rrp_10: [-30, -20, 0.5],
}
let cars = []; //车集合
let rsrpZb;
let promise6;//grojson 
let RSRPList = [];
let SINRList = [];
let UpkbpsList = []; //上行速率
let DownkbpsList = []; //下行速率
let dataList = [];
let RSRPnum = 1;
let myChart; //rsrp
let mysinr; //sinr
let upDownsl; //上行行速率  
let myfxjgRsrp; //分析结果RSRP占比
let myfxjgSinr; //分析结果sinr占比
let PanoramaArray = []; //全景图标集合

let doms = null

export default {
  mixins: [scaleMixin],
  components: {
    Lonlat,
    About,
    themeheader,
  },
  data () {
    return {
      home:true,
      carOfUser:true,
      trajectoryType:false,
      refractionLine:true,
      fakeData:[
        { name:'西安美术馆', num:'2', prect:'98%', type:0 },
        { name:'大雁塔', num:'2', prect:'92%', type:1 },
        { name:'银泰百货', num:'2', prect:'89%', type:1 },
        { name:'西安音乐厅', num:'2', prect:'91%', type:1 },
        { name:'陕西大剧院', num:'2', prect:'91%', type:1 }
      ],
      fakeData2:[
        { name:'2022/12.23', num:'10:30:56', prect:1, type:2 },
        { name:'2022/12.22', num:'10:30:56', prect:3, type:1 },
        { name:'2022/12.21', num:'10:30:56', prect:2, type:2 },
        { name:'2022/12.20', num:'10:30:56', prect:1, type:3 },
        { name:'2022/12.19', num:'10:30:56', prect:2, type:1 }
      ],
      fakeData3:[
        { name:'西安美术馆', num:'2', prect:'865㎡', type:'信号干扰' },
        { name:'大雁塔', num:'3', prect:'322㎡', type:'信号干扰' },
        { name:'银泰百货', num:'2', prect:'236㎡', type:'信号干扰' },
        { name:'西安音乐厅', num:'5', prect:'725㎡', type:'信号干扰' },
        { name:'陕西大剧院', num:'2', prect:'375㎡', type:'信号干扰' }
      ],
      btnB:[
        {label:'首页',name:'home',path:require('../assets/UIpic/btnB-2.png'),status:true},
        {label:'加载网元',name:'wangyuan',path:require('../assets/UIpic/btnB-wangyuan.png'),status:false},
        {label:'方案孪生',name:'fangan',path:require('../assets/UIpic/btnB-1.png'),status:false},
        {label:'沉浸式孪生',name:'chenjin',path:require('../assets/UIpic/btnB-4.png'),status:false},
        {label:'机房孪生',name:'jifang',path:require('../assets/UIpic/btnB-jifang.png'),status:false}
      ],
      cards:{
        tianxian:false,
        station:false,
      },
      switchs:{
        mimo:false
      },
      slider:{
        angle:0,
        rolate:0
      },
      token:
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJlYTQ2ZjdjNS1jM2E0LTQ1M2EtOWM0My1mODMzNzY3YjYzY2YiLCJpZCI6MjkzMjcsInNjb3BlcyI6WyJhc3IiLCJnYyJdLCJpYXQiOjE1OTE5NDIzNjB9.RzKlVTVDTQ9r7cqCo-PDydgUh8Frgw0Erul_BVxiS9c',
      loading: false,
      icon: {
        icon1: false,
        icon3: false,
        icon7: false,
        icon8: false
      },
      keyboardLoadCar: true,
      shijiao: '第一人称',
      shijiaoShow: false,
      shijiaoShow2: false,
      cartographic: null,
      hpRoll: null,
      showFs: '轨迹线',
      switchingStatus: true,
      loadings: true,
      rsrpColor: '#fff',
      rsrpNum: 0,

      value1: 0,
      value2: 0,
      value3: 0,
      value4: 5,
      slider1_min: -3000,
      slider1_max: 3000,
      slider2_min: -2700,
      slider2_max: 2700,
      slider3_min: -40,
      slider3_max: 40,
      slider4_min: 1,
      slider4_max: 20,
      showMI: 0, //水平切割位置
      crosswiseMI: 0, //横向切割位置
      lengthwaysMI: 0, //纵向切割位置 
      pointsSize: 5, //网元大小

      analyse: false,
      modelName: '大唐不夜城',
      largeScreenShow: true,
      chartsShow: false, // 切换西安和大唐不夜城图表显示
      azimuth: 0, //方位角
      mechanical: 0, //方位角调整
      tableData: [],
      loadingModel: true,
      btnsShow: true,
      loadText: false, //判断业务数据是否加载
      dialogVisible: false, //沉浸式分析弹框
      wymbOpen: false, //折叠按钮
      wymbOpen2: false, //折叠按钮
      form: {
        model: '车',
        type: '点',
        speed: 7,
        region: ""
      },
      updateSpeed: 2,
      modelTiaozheng: false,
      showMI: 0,
      qiegemishow: false, //水平切割深度显示
      heatMapShow: true,
      severRoomShow: false,
      guagao: [
        {
          value: '1',
          label: '25米以下'
        },
        {
          value: '2',
          label: '25米-40米'
        },
        {
          value: '3',
          label: '40米-80米'
        },
        {
          value: '4',
          label: '80米以上'
        },
      ],
      txguagao: '1',
      rsrp: 0,
      serverRoomBtnName: '展开',
      chenjinshiShow: false, //沉浸式分析弹框
      carModelList: [],
      allHeight_select: [],
      rsrpChartsShow: false,
      points: false, //点云加载状态
      PanoramaShow: false, //全景弹框
      closeBtns: {
        ion1: false,
        ion2: false,
        ion3: false,
        ion4: false
      },
      lonLatObj: {},
      RSRPText: '',
      electricalList: [],
      AnalysisCharsShow: false,//沉浸式分析图表show
      fxjgShow: false,
      jgfx_DataList: [],
      SiteTableData: [], //基站弹框数据
      ImmersiveDataList: [], //仿真动态指标
      height: document.body.clientHeight,
      timer: false,
      hOfrsrp: [],
      legendOfRSRP:[],
      legendOfSINR:[]
    }
  },
  created () {
    chartData.RSRPcategoryData.map(item => {
      this.hOfrsrp.push(item.height)
    })
    let unique = (arr) => [...new Set(arr)]
    this.hOfrsrp = unique(this.hOfrsrp)
    this.hOfrsrp.unshift(0)
  },
  mounted () {
    // 禁用右键
    document.oncontextmenu = new Function('event.returnValue=false')
    // 禁用选择
    document.onselectstart = new Function('event.returnValue=false')
    this.materialLine() //调用连线shader
    this.loadXinhao() //加载业务数据
    this.loadzhandain() //加载基站数据
    this.loadTxzjJSON() //天线数据
    this.common()
    this.bycModelShow()
    this.$nextTick(() => {
      // this.$refs.long.mouseLonlat(window.viewer)
    })
    //加载天空
    this.skyboxs({
      positiveX: positiveX,
      negativeX: negativeX,
      positiveY: positiveY,
      negativeY: negativeY,
      positiveZ: positiveZ,
      negativeZ: negativeZ,
    })
    // this.loadzhandain() //加载站点
    // this.loadTxzjJSON() //加载天线支架
    // this.loadTxJSON() //加载天线
    // this.loadPnts() //加载西安电平数据
    // this.loadXinhao() //加载业务数据
    // this.load_tourCar() //加载观光车
    // this.loadbycBeas() //不夜城基站
    this.loadWall({
      positions: [
        108.95621305408461, 34.205408908219944, 108.95605328211997,
        34.2244018474736, 108.96156647368824, 34.22442617360019,
        108.96162531489004, 34.22087160085414, 108.96165486846544,
        34.22077109044262, 108.96175304413879, 34.22067625456899,
        108.96556494145032, 34.21919152567224, 108.96587804079748,
        34.217862870648474, 108.96201136420753, 34.21783347907373,
        108.96201624518575, 34.20538935419729, 108.95621305408461,
        34.205408908219944,
      ],
      maxheight: 30,
      minheight: 0,
      color: '#FFCA42',
      duration: 3000,
    }) //大唐不夜城边界

    // this.addCharts() //rsrp指标
    // this.SINRCharts() //sinr指标
    // this.klulkbps() //上下行速率
    this.carLengthjc() //解析车路径
    // this.fcjg_rsrp()
    // this.fcjg_sinr()
    //闪电效果
    // var collection = viewer.scene.postProcessStages;
    // var lightning = Cesium.PostProcessStageLibrary.createLightningStage({
    //   mix_factor: 0.4,//混合系数
    // });
    // collection.add(lightning);
    // setTimeout(() => {
    //   collection.remove(lightning);
    // }, 30000)
    const that = this
    window.onresize = () => {
      return (() => {
        window.screenHeight = document.body.clientHeight
        that.height = window.screenHeight;
      })()
    }
  },
  watch: {
    height (val) {
      console.log(val);
      // 为了避免频繁触发resize函数导致页面卡顿，使用定时器
      if (!this.timer) {
        // 一旦监听到的screenWidth值改变，就将其重新赋给data里的screenWidth
        this.screenHeight = val
        this.timer = true
        let that = this
        setTimeout(function () {
          that.timer = false
        }, 400)
      }
    }
  },
  methods: {
    resultCard(e,i){
      if (this.ImmersiveDataList.length == 0) {
        this.shijiaoShow2 = true
      }
        this.fxjgShow = false
    },
    card_up(e,i){
      switch(i){
        case 1:
            e.target.offsetParent.style = 'display:none'
          break
        case '':
          if (e.target.offsetParent.style.maxHeight) {
            e.target.style = ''
            e.target.offsetParent.style = ''
          } else {
            e.target.style = 'transform:rotate(0deg)'
            e.target.offsetParent.style = 'max-height:calc(2.6rem + 24px)'
          }
          break
      }
    },
    btnB_click(v){
      {
        this.pntsShow(false)
        this.loadMachineRoom(false)
      }
      if (v.name != 'wangyuan') {
        this.btnB.map(t => {t.status = false})
      }
      for(let i in this.cards){
        this.cards[i] = false
      }
      this.home = false
      switch(v.name){
        case 'wangyuan':
            v.status = true
            this.loadbycwy()
          break
        case 'fangan':
            v.status = true
            this.cards.fangan = true
            this.pntsShow(true)
          break
        case 'jifang':
            v.status = true
            this.cards.jifang = true
            this.loadMachineRoom(true)
          break
        case 'chenjin':
            v.status = true
            this.load_car()
          break
        case 'home':
          this.$nextTick(() => {
            for(let i in doms){
              doms[i].resize()
            }
          })
            v.status = true
            this.home = true
          break
      }
    },
  //  折线图下拉选择事件
    lineChartChange(v){
      let list
      let type
      if (v.target.id.indexOf('SINR') != -1) {
        list = v.target.value == 0 ? chartData.SINRcategoryAll : chartData.SINRcategoryData.filter(item => {return item.height == v.target.value})
        type = 'sinr'
      } else {
        list = v.target.value == 0 ? chartData.RSRPcategoryAll : chartData.RSRPcategoryData.filter(item => {return item.height == v.target.value})
        type = 'rsrp'
      }
      {
        const dataOfX = []
        const data = []
        list.map(t => {
          dataOfX.push(t[Object.keys(t).filter(item => { return item.indexOf(type) != -1})[0]])
          data.push(t.area_pfm)
        })
        const dom = Object.keys(doms).filter(item => { return item.indexOf(type.toUpperCase()) != -1})
        console.log(dataOfX);
        console.log(dom);
        doms[dom[0]].setOption({
          xAxis: [{ data: dataOfX }],
          series: [{ data: data }]
        })
      }
    },
    sendDoms(v){ doms = v},
    common () {
      Cesium.Ion.defaultAccessToken = this.token
      Cesium.Camera.DEFAULT_VIEW_RECTANGLE = Cesium.Rectangle.fromDegrees(
        80,
        22,
        130,
        50
      )
      window.viewer = new Cesium.Viewer('box', {
        // sceneMode: Cesium.SceneMode.COLUMBUS_VIEW, //哥伦布视图
        scene3DOnly: true,
        infoBox: false, // 信息框
        shouldAnimate: true,
        vrButton: false, // VR按钮
        geocoder: false,
        homeButton: false, // 初始位置
        sceneModePicker: false,
        baseLayerPicker: false,
        navigationHelpButton: false,
        selectionIndicator: false, //选择框
        animation: true, //时间控件
        timeline: true, //时间线
        fullscreenButton: false,
        shadows: false, // 去掉阴影
        // terrainProvider: Cesium.createWorldTerrain(), // 世界地形
      })
      // 显示帧率
      viewer.scene.debugShowFramesPerSecond = false
      // 开启深度检测，默认是关闭的
      // viewer.scene.globe.depthTestAgainstTerrain = true;
      window.viewer.scene.globe.enableLighting = false // 开启全球光照
      //分辨率调整函数
      window.viewer.scene.fxaa = true
      window.viewer.scene.postProcessStages.fxaa.enabled = true
      window.viewer.animation.container.style.visibility = 'hidden' // 不显示动画控件
      window.viewer.timeline.container.style.visibility = 'hidden' // 不显示动画控件
      var supportsImageRenderingPixelated =
        viewer.cesiumWidget._supportsImageRenderingPixelated
      if (supportsImageRenderingPixelated) {
        var vtxfDpr = window.devicePixelRatio
        while (vtxfDpr >= 2.0) {
          vtxfDpr /= 2.0
        }
        window.viewer.resolutionScale = vtxfDpr
      }
      //深度检测
      window.viewer.scene.globe.depthTestAgainstTerrain = false
      //卫星影像
      window.viewer.imageryLayers.removeAll()
      let guge = new Cesium.ImageryLayer(
        new Cesium.ArcGisMapServerImageryProvider({
          url: 'https://services.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer',
          enablePickFeatures: !1,
        })
      )
      window.viewer.imageryLayers.add(guge)
      // 去除时间原因影响模型颜色
      window.viewer.scene.light = new Cesium.DirectionalLight({
        //   //去除时间原因影响模型颜色
        direction: new Cesium.Cartesian3(
          0.35492591601301104,
          -0.8909182691839401,
          -0.2833588392420772
        ),
      })
      viewer.clock.currentTime = new Cesium.JulianDate(
        2459905,
        54037
      );
      window.viewer.cesiumWidget.screenSpaceEventHandler.removeInputAction(
        Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK
      )
      this.eye()
      this.clickFeatures()
    },
    eye () {
      let that = this
      window.viewer1 = new Cesium.Viewer('box1', {
        // sceneMode: Cesium.SceneMode.COLUMBUS_VIEW, //哥伦布视图
        scene3DOnly: true,
        infoBox: false, // 信息框
        shouldAnimate: true,
        vrButton: false, // VR按钮
        geocoder: false,
        homeButton: false, // 初始位置
        sceneModePicker: false,
        baseLayerPicker: false,
        navigationHelpButton: false,
        selectionIndicator: false, //选择框
        animation: true, //时间控件
        timeline: true, //时间线
        fullscreenButton: false,
        shadows: false, // 去掉阴影
      })
      window.viewer1.animation.container.style.visibility = 'hidden' // 不显示动画控件
      window.viewer1.timeline.container.style.visibility = 'hidden' // 不显示动画控件
      //卫星影像
      window.viewer1.imageryLayers.removeAll()
      let active = viewer1.imageryLayers.addImageryProvider(
        new Cesium.AmapImageryProvider({
          url: window.g.imageryLayerUrl,
          layer: 'tdtAnnoLayer',
          style: 'default',
          format: 'image/jpeg',
          maximumLevel: 18,
          // subdomains: this.subdomains,
          tileMatrixSetID: 'GoogleMapsCompatible',
          crs: 'WGS84', // 坐标系: WGS84 、BD09 、GCJ02，仅百度、高德有效
          tilingScheme: null,
        })
      )
      var helper = new Cesium.EventHelper()
      helper.add(viewer.scene.globe.tileLoadProgressEvent, function (e) {
        if (e == 0) {
          that.loadings = false //关闭加载动画
        }
      })
      let control = viewer1.scene.screenSpaceCameraController
      // 旋转
      control.enableRotate = false
      // 旋转
      control.enableTranslate = false
      // 放大
      control.enableZoom = false
      // 倾斜
      control.enableTilt = false
      // 看向
      control.enableLook = false
      //视角同步方法
      let syncViewer = function () {
        const cartesian3 = new Cesium.Cartesian3(
          viewer.camera.position.x,
          viewer.camera.position.y,
          viewer.camera.position.z
        )
        const cartographic =
          viewer.scene.globe.ellipsoid.cartesianToCartographic(cartesian3)
        const lat = Cesium.Math.toDegrees(cartographic.latitude)
        const lng = Cesium.Math.toDegrees(cartographic.longitude)
        const alt = cartographic.height
        var point = turf.point([lng, lat])
        var distance = -0.3
        var bearing = (viewer.camera.heading * 180) / Math.PI
        var options = { units: 'kilometers' }
        var destination = turf.destination(point, distance, bearing, options)
        viewer1.camera.flyTo({
          destination: Cesium.Cartesian3.fromDegrees(
            destination.geometry.coordinates[0],
            destination.geometry.coordinates[1],
            alt + 100
          ),
          orientation: {
            heading: viewer.camera.heading,
            pitch: Cesium.Math.toRadians(-15.0),
            roll: viewer.camera.roll,
          },
          duration: 0.0,
        })
      }
      //使用回调方法联动视角
      viewer.entities.add({
        position: Cesium.Cartesian3.fromDegrees(0, 0),
        label: {
          text: new Cesium.CallbackProperty(function () {
            syncViewer()
            return ''
          }, true),
        },
      })
      const titles = new Cesium.Cesium3DTileset({
        url: window.tileset.XANavTilse,
        maximumScreenSpaceError: 32, //用于驱动细节细化级别的最大屏幕空间误差。 默认16
        maximumMemoryUsage: 2048 * 2, //瓦片集可以使用的最大内存量（以 MB 为单位）。 默认512
        cullWithChildrenBounds: false, //优化选项。是否使用子边界体积的并集来剔除瓦片。默认true
        cullRequestsWhileMoving: false, //优化选项。不要请求由于相机移动而在返回时可能未使用的图块。这种优化只适用于静止的瓦片集。默认true
        cullRequestsWhileMovingMultiplier: 120.0, //优化选项。移动时用于剔除请求的乘数。较大的是更积极的剔除，较小的较不积极的剔除。 默认值60
        preloadWhenHidden: true, //tileset.show时 预加载瓷砖false。加载图块，就好像图块集可见但不渲染它们。 默认false
        preloadFlightDestinations: true, //优化选项。在相机飞行时在相机的飞行目的地预加载图块。。 默认true
        preferLeaves: true, //优化选项 最好先装载叶子。 默认false
        dynamicScreenSpaceError: true, //优化选项。减少距离相机较远的图块的屏幕空间错误。 默认false
        dynamicScreenSpaceErrorDensity: 0.00278, //用于调整动态屏幕空间误差的密度，类似于雾密度。
        skipLevelOfDetail: true,
        baseScreenSpaceError: 2048,
        skipScreenSpaceErrorFactor: 1,
        skipLevels: 0,
        immediatelyLoadDesiredLevelOfDetail: true,
        loadSiblings: false,
        // shadows: Cesium.ShadowMode.ENABLED,
        show: true,
      })
      titles.style = new Cesium.Cesium3DTileStyle({
        color: {
          conditions: [
            // ['${HEIGHT} <= 20', `color("#EDFC2A")`],
            ['true', "color('#ffffff',0.7)"],
          ],
        },
        // show: newStrings,
      })
      viewer1.scene.primitives.add(titles)
    },
    //加载西安模型
    loadXatiles (url) {
      let customShader = new Cesium.CustomShader({
        lightingModel: Cesium.LightingModel.UNLIT,
        fragmentShaderText: `
          void fragmentMain(FragmentInput fsInput, inout czm_modelMaterial material)
          {
              vec3 normalEC = fsInput.attributes.normalEC;
              vec3 normalMC = czm_inverseNormal * normalEC;
              vec3 color = material.diffuse;
              vec3 white = vec3(1.0,1.0,1.0);
              float m = dot(normalMC, vec3(0.0,0.0,1.0));
              m = pow(m,5.0);
              material.diffuse = mix(color, white, clamp(m,0.0,1.0) * 0.5);
          }
          `,
      });
      const titles = new Cesium.Cesium3DTileset({
        url: url,
        maximumScreenSpaceError: 5, //用于驱动细节细化级别的最大屏幕空间误差。 默认16
        maximumMemoryUsage: 2048 * 4, //瓦片集可以使用的最大内存量（以 MB 为单位）。 默认512
        cullWithChildrenBounds: false, //优化选项。是否使用子边界体积的并集来剔除瓦片。默认true
        cullRequestsWhileMoving: false, //优化选项。不要请求由于相机移动而在返回时可能未使用的图块。这种优化只适用于静止的瓦片集。默认true
        cullRequestsWhileMovingMultiplier: 60.0, //优化选项。移动时用于剔除请求的乘数。较大的是更积极的剔除，较小的较不积极的剔除。 默认值60
        preloadWhenHidden: true, //tileset.show时 预加载瓷砖false。加载图块，就好像图块集可见但不渲染它们。 默认false
        preloadFlightDestinations: true, //优化选项。在相机飞行时在相机的飞行目的地预加载图块。。 默认true
        preferLeaves: true, //优化选项 最好先装载叶子。 默认false
        dynamicScreenSpaceError: true, //优化选项。减少距离相机较远的图块的屏幕空间错误。 默认false
        dynamicScreenSpaceErrorDensity: 0.00278, //用于调整动态屏幕空间误差的密度，类似于雾密度。
        skipLevelOfDetail: true,
        baseScreenSpaceError: 2048 * 4,
        skipScreenSpaceErrorFactor: 1,
        skipLevels: 0,
        immediatelyLoadDesiredLevelOfDetail: true,
        loadSiblings: false,
        // shadows: Cesium.ShadowMode.ENABLED,
        customShader: customShader,
        show: true,
      })
      // let shows;
      // this.titlesID.forEach((item) => {
      //   let show = "${id} !==" + `"${item}" && `;
      //   shows += show;
      // });
      // let newString = shows.replace("undefined", "");
      // let newStrings = newString.slice(0, newString.length - 3);
      titles.style = new Cesium.Cesium3DTileStyle({
        color: {
          conditions: [
            // ['${HEIGHT} <= 20', `color("#EDFC2A")`],
            ['true', "color('#ffffff',1)"],
          ],
        }
      })
      viewer.scene.primitives.add(titles)
      flyTos({
        lon: 108.959,
        lat: 34.2054,
        height: 10,
        viewer: viewer,
        heading: 0,
        pitch: -8,
        roll: 0,
      })
      titles.readyPromise.then(function (e) {
        var heightOffset = 0;  //高度
        var boundingSphere = e.boundingSphere;
        var cartographic = Cesium.Cartographic.fromCartesian(boundingSphere.center);
        var surface = Cesium.Cartesian3.fromRadians(cartographic.longitude, cartographic.latitude, 0.0);
        var lon = Cesium.Math.toDegrees(cartographic.longitude)
        var lat = Cesium.Math.toDegrees(cartographic.latitude)
        var offset = Cesium.Cartesian3.fromDegrees(lon, lat, heightOffset);
        var translation = Cesium.Cartesian3.subtract(offset, surface, new Cesium.Cartesian3());
        e.modelMatrix = Cesium.Matrix4.fromTranslation(translation);
      })
      xianModel.push(titles)
    },
    //加载机房
    loadMachineRoom (v) {
      if (v) {
        this.wymbOpen = true
        this.severRoomShow = true
        let positions = Cesium.Cartesian3.fromDegrees(108.96131319168451, 34.206369966784436, 0.11)
        let hpRolls = new Cesium.HeadingPitchRoll(
          Cesium.Math.toRadians(180),
          Cesium.Math.toRadians(0),
          Cesium.Math.toRadians(0))
        let fixedFrameTransforms = Cesium.Transforms.localFrameToFixedFrameGenerator(
          'south',
          'east'
        )
        serverRoommodel = viewer.scene.primitives.add(
          Cesium.Model.fromGltf({
            id: '机楼',
            position: positions,
            url: window.models.serverRoomModelUrl,
            // url: './models/GLTF/打电话/man-dadianhua.gltf',
            modelMatrix: Cesium.Transforms.headingPitchRollToFixedFrame(
              positions,
              hpRolls,
              Cesium.Ellipsoid.WGS84,
              fixedFrameTransforms
            ),
            scale: 1,
            lightColor: new Cesium.Cartesian3(20, 20, 20),
          })
        )
        flyTos({
          lon: 108.961853,
          lat: 34.205680,
          height: 15.89,
          viewer: viewer,
          heading: 327.53,
          pitch: -1.93,
          roll: 0.0,
        })
        this.icon.icon8 = !this.icon.icon8
        this.fullScreen()  //判断是否全屏
        this.closeBtns.ion1 = !this.closeBtns.ion1
        this.closeBtns.ion2 = false
        this.closeBtns.ion3 = false
      } else {
        this.severRoomShow = false
        this.icon.icon8 = !this.icon.icon8
        viewer.scene.primitives.remove(serverRoommodel)
        this.serverRoomBtnName = '展开'
        this.fullScreen()  //判断是否全屏
      }
    },
    //展开机房
    openServeRoom () {
      if (this.serverRoomBtnName == '展开') {
        serverRoommodel.activeAnimations.addAll({
          loop: Cesium.ModelAnimationLoop.NONE, // 结束从头开始
          reverse: false,
          speedup: 1,
          multiplier: 3,
        })
        this.serverRoomBtnName = '关闭'
        flyTos({
          lon: 108.961668,
          lat: 34.206085,
          height: 41.48,
          viewer: viewer,
          heading: 313.65,
          pitch: -31.52,
          roll: 0.0,
        })
      } else {
        serverRoommodel.activeAnimations.addAll({
          loop: Cesium.ModelAnimationLoop.NONE, // 结束从头开始
          reverse: true,
          speedup: 1,
          multiplier: 3,
        })
        this.serverRoomBtnName = '展开'
      }
    },
    //视角定位
    location (val) {
      switch (val) {
        case '办公区':
          flyTos({
            lon: 108.961318,
            lat: 34.206373,
            height: 14.95,
            viewer: viewer,
            heading: 312.59,
            pitch: -12.81,
            roll: 0,
          })
          break;
        case '监控室':
          flyTos({
            lon: 108.961253,
            lat: 34.206403,
            height: 15.77,
            viewer: viewer,
            heading: 297.77,
            pitch: -19.65,
            roll: 0,
          })
          break;
        case '机房':
          flyTos({
            lon: 108.961284,
            lat: 34.206347,
            height: 15.41,
            viewer: viewer,
            heading: 100.54,
            pitch: -2.22,
            roll: 0,
          })
          break;
        default:
          break;
      }
    },
    //弹框布局
    tankuangbuju () {
      this.closeBtns.ion1 = !this.closeBtns.ion1
      this.closeBtns.ion2 = false
      this.closeBtns.ion3 = false
    },
    tankuangbuju1 () {
      this.closeBtns.ion1 = false
      this.closeBtns.ion2 = !this.closeBtns.ion2
      this.closeBtns.ion3 = false
    },
    tankuangbuju2 () {
      this.closeBtns.ion1 = false
      this.closeBtns.ion2 = false
      this.closeBtns.ion3 = !this.closeBtns.ion3
    },
    tankuangbuju3 () {
      this.closeBtns.ion4 = !this.closeBtns.ion4
    },
    //加载geojson
    xianPolygon (pmaras) {
      let getCustomMaterialLine = (image, color) => {
        return new Cesium.CustomMaterialLine({
          image: image,
          color: color,
          duration: 1000,
        })
      }
      promise6 = Cesium.GeoJsonDataSource.load(pmaras.url, { clampToGround: false, fill: Cesium.Color.DARGOLDENNROD });
      promise6.then(function (dataSource) {
        viewer.dataSources.add(dataSource);
        var entities = dataSource.entities.values;
        if (pmaras.name == '西安边界') {
          entities.forEach(item => {
            item.polygon.material = Cesium.Color.fromCssColorString('#102F73').withAlpha(0.8);
            item.polygon.extrudedHeight = 0.3;
            item.polygon.outlineColor = Cesium.Color.BLACK
            item.polygon.outlineWidth = 5
          })
        } else if (pmaras.name == "绿地") {
          entities.forEach(item => {
            item.polygon.material = Cesium.Color.fromCssColorString('#3BD132').withAlpha(0.8);
            item.polygon.extrudedHeight = 2;
            item.polygon.outlineColor = Cesium.Color.BLACK
            item.polygon.outlineWidth = 8
          })
        } else if (pmaras.name == "水系") {
          entities.forEach(item => {
            item.polygon.material = Cesium.Color.fromCssColorString('#3284D1').withAlpha(0.8);
            item.polygon.extrudedHeight = 2;
            item.polygon.outlineColor = Cesium.Color.BLACK
            item.polygon.outlineWidth = 8
          })
        } else {
          entities.forEach(item => {
            item.polyline.material = getCustomMaterialLine(
              colorss,
              Cesium.Color.fromCssColorString('#F47A1A').withAlpha(0.7)
            )
            item.polyline.width = 2;
            item.polyline.glowPower = 1;
            item.polyline.height = 2.2;
          })
        }
      });
    },
    removezhengzhouZGD () {
      let removePromise7
      promise6.then((res) => {
        removePromise7 = res;
      })
      viewer.dataSources.remove(removePromise7);
    },
    //大唐不夜城边界
    loadWall (wallCanshu) {
      let drawWall = viewer.entities.add({
        name: '立体墙',
        wall: {
          positions: Cesium.Cartesian3.fromDegreesArray(wallCanshu.positions),
          // 设置高度
          maximumHeights: new Array(wallCanshu.positions.length / 2).fill(
            wallCanshu.maxheight
          ),
          minimumHeights: new Array(wallCanshu.positions.length / 2).fill(
            wallCanshu.minheight
          ),
          material: new Cesium.DynamicWallMaterialProperty({
            color: Cesium.Color.fromCssColorString(wallCanshu.color).withAlpha(
              0.8
            ),
            duration: wallCanshu.duration,
          }),
        },
      })
    },
    //加载大唐不夜城水面
    loadWater (pmaras) {
      let polygonMaterial = new Cesium.EllipsoidSurfaceAppearance({
        material: new Cesium.Material({
          fabric: {
            type: 'Water',
            uniforms: {
              // baseWaterColor: new Cesium.Color(173 / 255, 165 / 255, 125 / 255, 0.5),  灰色
              baseWaterColor: new Cesium.Color(
                81 / 255,
                195 / 255,
                225 / 255,
                0.6
              ),
              // specularMap: water,
              normalMap: waterNormals,
              frequency: 100.0, //振幅
              animationSpeed: 0.02, //频率
              amplitude: 10.0,
              specularIntensity: 5, //反光
              time: 20,
            },
          },
        }),
        // fragmentShaderSource: fs,
        fragmentShaderSource:
          'varying vec3 v_positionMC;\n' +
          'varying vec3 v_positionEC;\n' +
          'varying vec2 v_st;\n' +
          'void main()\n' +
          '{\n' +
          'czm_materialInput materialInput;\n' +
          'vec3 normalEC = normalize(czm_normal3D * czm_geodeticSurfaceNormal(v_positionMC, vec3(0.0), vec3(1.0)));\n' +
          '#ifdef FACE_FORWARD\n' +
          'normalEC = faceforward(normalEC, vec3(0.0, 0.0, 1.0), -normalEC);\n' +
          '#endif\n' +
          'materialInput.s = v_st.s;\n' +
          'materialInput.st = v_st;\n' +
          'materialInput.str = vec3(v_st, 0.0);\n' +
          'materialInput.normalEC = normalEC;\n' +
          'materialInput.tangentToEyeMatrix = czm_eastNorthUpToEyeCoordinates(v_positionMC, materialInput.normalEC);\n' +
          'vec3 positionToEyeEC = -v_positionEC;\n' +
          'materialInput.positionToEyeEC = positionToEyeEC;\n' +
          'czm_material material = czm_getMaterial(materialInput);\n' +
          '#ifdef FLAT\n' +
          'gl_FragColor = vec4(material.diffuse + material.emission, material.alpha);\n' +
          '#else\n' +
          'gl_FragColor = czm_phong(normalize(positionToEyeEC), material, czm_lightDirectionEC);\n' +
          'gl_FragColor.a=0.6;\n' +
          '#endif\n' +
          '}\n',
        vertexShader: [
          '#include <common>',
          '#include <fog_pars_vertex>',
          '#include <logdepthbuf_pars_vertex>',

          'uniform mat4 textureMatrix;',

          'varying vec4 vCoord;',
          'varying vec2 vUv;',
          'varying vec3 vToEye;',

          'void main() {',

          'vUv = uv;',
          'vCoord = textureMatrix * vec4( position, 1.0 );',

          'vec4 worldPosition = modelMatrix * vec4( position, 1.0 );',
          'vToEye = cameraPosition - worldPosition.xyz;',

          'vec4 mvPosition =  viewMatrix * worldPosition;', // used in fog_vertex
          'gl_Position = projectionMatrix * mvPosition;',

          '#include <logdepthbuf_vertex>',
          '#include <fog_vertex>',

          '}',
        ].join('\n'),
      })
      viewer.scene.primitives.add(
        new Cesium.Primitive({
          geometryInstances: new Cesium.GeometryInstance({
            geometry: new Cesium.PolygonGeometry({
              polygonHierarchy: new Cesium.PolygonHierarchy(
                Cesium.Cartesian3.fromDegreesArrayHeights(
                  pmaras.polygonHierarchy
                )
              ),
              height: 0.36,
            }),
          }),
          appearance: polygonMaterial, //自定义appearance
          show: true,
        })
      )
    },
    //大屏
    addVideo (pmaras) {
      let videoElement = document.getElementById('daolu')
      videoElement.play()
      viewer.showRenderLoopErrors = false
      viewer.shouldAnimate = true
      let wall = viewer.entities.add({
        name: '立体墙效果',
        wall: {
          positions: Cesium.Cartesian3.fromDegreesArray(pmaras.positions),
          // 设置高度
          maximumHeights: new Array(pmaras.positions.length / 2).fill(
            pmaras.maxheight
          ),
          minimumHeights: new Array(pmaras.positions.length / 2).fill(
            pmaras.minheight
          ),
          material: videoElement,
        },
      })
      walls.push(wall)
    },
    //电平信号覆盖分析
    pntsShow (v) {
      console.log(this.analyse);
      if (v) {
        this.loadPnts(
          window.tileset.PointCloudUrl
        )
        this.icon.icon3 = !this.icon.icon3
        this.analyse = true
        flyTos({
          lon: 108.957920,
          lat: 34.135828,
          height: 6130.36,
          viewer: viewer,
          heading: 1.08,
          pitch: -34.64,
          roll: 360.00,
        })
        this.fullScreen()  //判断是否全屏
        this.closeBtns.ion3 = !this.closeBtns.ion3
        this.closeBtns.ion1 = false
        this.closeBtns.ion2 = false
      } else {
        viewer.scene.primitives.remove(tileset)
        this.analyse = !this.analyse
        this.icon.icon3 = !this.icon.icon3
        this.qiegemishow = false
        this.points = false
        this.fullScreen()  //判断是否全屏
      }
    },
    bycModelShow () {
      if (this.modelName == '大唐不夜城') {
        if (xianModel.length > 0) {
          xianModel.forEach((item) => {
            viewer.scene.primitives.remove(item)
          })
        }
        this.addVideo({
          positions: [
            108.95895930893968, 34.20779114709436, 108.95895930779104,
            34.20793121528859,
          ],
          maxheight: 17.82,
          minheight: 9.37,
        }) //大屏广告
        this.addVideo({
          positions: [
            108.95913745720488, 34.215752008530345, 108.95913746554546,
            34.216061754395795,
          ],
          maxheight: 14.04,
          minheight: 0,
        })
        this.addVideo({
          positions: [
            108.95835733808336, 34.21815346493692, 108.95835735954324,
            34.218252044515744,
          ],
          maxheight: 17.95,
          minheight: 5.79,
        })
        this.addVideo({
          positions: [
            108.95835753335838, 34.217638899149506, 108.9583573778265,
            34.21773746688779,
          ],
          maxheight: 17.95,
          minheight: 5.79,
        })
        this.addVideo({
          positions: [
            108.95835737634978, 34.218524674465144, 108.95835733069289,
            34.218623277673025,
          ],
          maxheight: 17.95,
          minheight: 5.79,
        })
        this.addVideo({
          positions: [
            108.95835734421509, 34.21882496018736, 108.95835731090727,
            34.21892355233954,
          ],
          maxheight: 17.95,
          minheight: 5.79,
        })
        this.addVideo({
          positions: [
            108.95833542301898, 34.21779433123705, 108.95833544114247,
            34.218034330150225,
          ],
          maxheight: 21.11,
          minheight: 5.59,
        })
        this.loadXatiles(
          window.tileset.BYCModelUrl
        )
        this.loadXatiles(
          window.tileset.BYCzbModelUrl
        )
        this.loadXatiles(
          window.tileset.BYCTreeModel
        )
        //加载水面
        // WaterPnts.forEach(item => {
        //   this.loadWater({
        //     polygonHierarchy: item,
        //   })
        // })
        this.modelName = '西安白膜'
      } else {
        if (xianModel.length > 0) {
          xianModel.forEach((item) => {
            viewer.scene.primitives.remove(item)
          })
        }
        if (walls.length > 0) {
          walls.forEach((item) => {
            viewer.entities.remove(item)
          })
        }
        this.loadXatiles(
          'http://*************:59083/models/3dmodels/XIAN2_Model/tileset.json'
        )
        this.modelName = '大唐不夜城'
      }
    },
    //加载电平数据
    loadPnts (url) {
      var viewModel = {
        debugBoundingVolumesEnabled: false,
        edgeStylingEnabled: false,
        // exampleTypes: clipObjects,
        // currentExampleType: clipObjects[0],
      }
      this.loading = true
      let that = this
      let selectedPlane
      let clippingPlanes
      clippingPlanes = new Cesium.ClippingPlaneCollection({
        planes: [
          new Cesium.ClippingPlane(new Cesium.Cartesian3(0.0, -1.0, 0.0), 0.0),
          new Cesium.ClippingPlane(new Cesium.Cartesian3(1.0, 0.0, 0.0), 0.0),
          new Cesium.ClippingPlane(new Cesium.Cartesian3(0.0, 0.0, -1.0), 0.0),
          // new Cesium.ClippingPlane(new Cesium.Cartesian3(0.0, 0.0, 1.0), 0.0),
        ],
        edgeWidth: viewModel.edgeStylingEnabled ? 1.0 : 0.0,
      })
      tileset = new Cesium.Cesium3DTileset({
        url: url,
        clippingPlanes: clippingPlanes,
        pointCloudShading: {
          attenuation: true,
          maximumAttenuation: 2,
        },
        show: true,
      })
      tileset.style = new Cesium.Cesium3DTileStyle({
        color: {
          conditions: [
            [
              '${RSRP} >=' +
              `${rrp.rrp_10[0]}` +
              '&& ${RSRP} <=' +
              `${rrp.rrp_10[1]}`,
              "color('#0000FF',0.3)",
            ],
            [
              '${RSRP} >=' +
              `${rrp.rrp_9[0]}` +
              '&& ${RSRP} <=' +
              `${rrp.rrp_9[1]}`,
              "color('#008FFF',0.3)",
            ],
            [
              '${RSRP} >=' +
              `${rrp.rrp_8[0]}` +
              '&& ${RSRP} <=' +
              `${rrp.rrp_8[1]}`,
              "color('#00FFA5',0.3)",
            ],
            [
              '${RSRP} >=' +
              `${rrp.rrp_7[0]}` +
              '&& ${RSRP} <=' +
              `${rrp.rrp_7[1]}`,
              "color('#00FF4B',0.3)",
            ],
            [
              '${RSRP} >=' +
              `${rrp.rrp_6[0]}` +
              '&& ${RSRP} <=' +
              `${rrp.rrp_6[1]}`,
              "color('#75FF00',0.3)",
            ],
            [
              '${RSRP} >=' +
              `${rrp.rrp_5[0]}` +
              '&& ${RSRP} <=' +
              `${rrp.rrp_5[1]}`,
              "color('#D0FF00',0.3)",
            ],
            [
              '${RSRP} >=' +
              `${rrp.rrp_4[0]}` +
              '&& ${RSRP} <=' +
              `${rrp.rrp_4[1]}`,
              "color('#FFF200',0.3)",
            ],
            [
              '${RSRP} >=' +
              `${rrp.rrp_3[0]}` +
              '&& ${RSRP} <=' +
              `${rrp.rrp_3[1]}`,
              "color('#FFAB00',0.3)",
            ],
            [
              '${RSRP} >=' +
              `${rrp.rrp_2[0]}` +
              '&& ${RSRP} <=' +
              `${rrp.rrp_2[1]}`,
              "color('#FF6F00',0.3)",
            ],
            [
              '${RSRP} >=' +
              `${rrp.rrp_1[0]}` +
              '&& ${RSRP} <=' +
              `${rrp.rrp_1[1]}`,
              "color('#FF0000',0.3)",
            ],
            ['true', "color('#FF0000',0.3)"],
          ],
        },
        pointSize: 5,
      })
      viewer.scene.primitives.add(tileset)

      tileset.debugShowBoundingVolume = viewModel.debugBoundingVolumesEnabled
      tileset.readyPromise.then((tilesets) => {
        this.points = true
        this.loading = false
        var boundingSphere = tileset.boundingSphere
        var radius = boundingSphere.radius
        const cartesian3 = new Cesium.Cartesian3(
          boundingSphere.center.x,
          boundingSphere.center.y,
          boundingSphere.center.z
        )
        const cartographic =
          viewer.scene.globe.ellipsoid.cartesianToCartographic(cartesian3)
        const lat = Cesium.Math.toDegrees(cartographic.latitude)
        const lng = Cesium.Math.toDegrees(cartographic.longitude)
        const alt = cartographic.height
        if (
          !Cesium.Matrix4.equals(
            tileset.root.transform,
            Cesium.Matrix4.IDENTITY
          )
        ) {
          // 裁剪平面最初定位在瓷砖的根变换处。
          //应用一个额外的矩阵使裁剪平面在边界球中心居中。
          var transformCenter = Cesium.Matrix4.getTranslation(
            tileset.root.transform,
            new Cesium.Cartesian3()
          )
          console.log(tileset.root.transform)
          var transformCartographic =
            Cesium.Cartographic.fromCartesian(transformCenter)
          var boundingSphereCartographic = Cesium.Cartographic.fromCartesian(
            tileset.boundingSphere.center
          )
          var height =
            boundingSphereCartographic.height - transformCartographic.height
          clippingPlanes.modelMatrix = Cesium.Matrix4.fromTranslation(
            new Cesium.Cartesian3(0.0, 0.0, height)
          )
        }
        var plane = clippingPlanes.get(0)
        var planeEntity = viewer.entities.add({
          position: Cesium.Cartesian3.fromDegrees(lng, lat, alt),
          plane: {
            dimensions: new Cesium.Cartesian2(radius * 1.5, radius * 1.5),
            material: Cesium.Color.WHITE.withAlpha(0),
            plane: new Cesium.CallbackProperty(
              createPlaneUpdateFunction(plane),
              false
            ),
            outline: false,
            outlineColor: Cesium.Color.WHITE.withAlpha,
          },
        })
        planeEntities.push(planeEntity)
        //纵面
        var plane1 = clippingPlanes.get(1)
        var planeEntity1 = viewer.entities.add({
          position: Cesium.Cartesian3.fromDegrees(lng, lat, alt),
          plane: {
            dimensions: new Cesium.Cartesian2(radius * 1.5, radius * 1.5),
            material: Cesium.Color.WHITE.withAlpha(0),
            plane: new Cesium.CallbackProperty(
              createPlaneUpdateFunction2(plane1),
              false
            ),
            outline: false,
            outlineColor: Cesium.Color.WHITE.withAlpha,
          },
        })
        planeEntities.push(planeEntity1)
        //水平面
        var plane2 = clippingPlanes.get(2)
        var planeEntity2 = viewer.entities.add({
          position: Cesium.Cartesian3.fromDegrees(lng, lat, alt),
          plane: {
            dimensions: new Cesium.Cartesian2(radius * 5, radius * 5),
            material: Cesium.Color.WHITE.withAlpha(0),
            plane: new Cesium.CallbackProperty(
              createPlaneUpdateFunction3(plane2),
              false
            ),
            outline: false,
            outlineColor: Cesium.Color.WHITE.withAlpha,
          },
        })
        planeEntities.push(planeEntity2)

        return tileset
      })
        .otherwise(function (error) {
          console.log(error)
        })

      var downHandler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas)
      downHandler.setInputAction(function (movement) {
        var pickedObject = viewer.scene.pick(movement.position)
        if (
          Cesium.defined(pickedObject) &&
          Cesium.defined(pickedObject.id) &&
          Cesium.defined(pickedObject.id.plane)
        ) {
          selectedPlane = pickedObject.id.plane
          selectedPlane.material = Cesium.Color.WHITE.withAlpha(0)
          selectedPlane.outlineColor = Cesium.Color.WHITE
          viewer.scene.screenSpaceCameraController.enableInputs = false
        }
      }, Cesium.ScreenSpaceEventType.LEFT_DOWN)

      // Release plane on mouse up
      var upHandler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas)
      upHandler.setInputAction(function () {
        if (Cesium.defined(selectedPlane)) {
          selectedPlane.material = Cesium.Color.WHITE.withAlpha(0)
          selectedPlane.outlineColor = Cesium.Color.WHITE
          selectedPlane = undefined
        }
        viewer.scene.screenSpaceCameraController.enableInputs = true
      }, Cesium.ScreenSpaceEventType.LEFT_UP)
      // var moveHandler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas)
      // moveHandler.setInputAction(function (movement) {
      //   if (Cesium.defined(selectedPlane)) {
      //     var deltaY = movement.startPosition.x - movement.endPosition.x
      //     targetY += deltaY
      //     console.log(targetY)
      //   }
      // }, Cesium.ScreenSpaceEventType.MOUSE_MOVE)
      function createPlaneUpdateFunction (plane) {
        return function () {
          plane.distance = targetY
          return plane
        }
      }
      function createPlaneUpdateFunction2 (plane) {
        return function () {
          plane.distance = targetY2
          return plane
        }
      }
      function createPlaneUpdateFunction3 (plane) {
        return function () {
          plane.distance = targetY3
          return plane
        }
      }
    },
    crosswise (val) {
      console.log(val);
      if (this.points) {
        var pickedObject = planeEntities[0].plane
        pickedObject.material = Cesium.Color.WHITE.withAlpha(0)
        pickedObject.outlineColor = Cesium.Color.WHITE
        targetY = val
        targetY2 = this.slider2_min
        targetY3 = this.slider3_min
        this.qiegemishow = false
        this.crosswiseMI = val + 3000
      }
    },
    lengthways (val) {
      if (this.points) {
        var pickedObject = planeEntities[1].plane
        pickedObject.material = Cesium.Color.WHITE.withAlpha(0)
        pickedObject.outlineColor = Cesium.Color.WHITE
        targetY2 = val
        targetY = this.slider1_min
        targetY3 = this.slider3_min
        this.qiegemishow = false
        this.lengthwaysMI = val + 2700
      }
    },
    vertical (val) {
      if (this.points) {
        var pickedObject = planeEntities[2].plane
        pickedObject.material = Cesium.Color.WHITE.withAlpha(0)
        pickedObject.outlineColor = Cesium.Color.WHITE
        targetY3 = val
        targetY = this.slider1_min
        targetY2 = this.slider2_min
        this.showMI = val + 40
        this.qiegemishow = true
      }
    },
    pointSize (val) {
      if (this.points) {
        this.pointsSize = val
        this.qiegemishow = false
        // console.log(tile);
        tileset.style = new Cesium.Cesium3DTileStyle({
          color: {
            conditions: [
              [
                '${RSRP} >=' +
                `${rrp.rrp_10[0]}` +
                '&& ${RSRP} <=' +
                `${rrp.rrp_10[1]}`,
                "color('#0000FF',0.3)",
              ],
              [
                '${RSRP} >=' +
                `${rrp.rrp_9[0]}` +
                '&& ${RSRP} <=' +
                `${rrp.rrp_9[1]}`,
                "color('#008FFF',0.3)",
              ],
              [
                '${RSRP} >=' +
                `${rrp.rrp_8[0]}` +
                '&& ${RSRP} <=' +
                `${rrp.rrp_8[1]}`,
                "color('#00FFA5',0.3)",
              ],
              [
                '${RSRP} >=' +
                `${rrp.rrp_7[0]}` +
                '&& ${RSRP} <=' +
                `${rrp.rrp_7[1]}`,
                "color('#00FF4B',0.3)",
              ],
              [
                '${RSRP} >=' +
                `${rrp.rrp_6[0]}` +
                '&& ${RSRP} <=' +
                `${rrp.rrp_6[1]}`,
                "color('#75FF00',0.3)",
              ],
              [
                '${RSRP} >=' +
                `${rrp.rrp_5[0]}` +
                '&& ${RSRP} <=' +
                `${rrp.rrp_5[1]}`,
                "color('#D0FF00',0.3)",
              ],
              [
                '${RSRP} >=' +
                `${rrp.rrp_4[0]}` +
                '&& ${RSRP} <=' +
                `${rrp.rrp_4[1]}`,
                "color('#FFF200',0.3)",
              ],
              [
                '${RSRP} >=' +
                `${rrp.rrp_3[0]}` +
                '&& ${RSRP} <=' +
                `${rrp.rrp_3[1]}`,
                "color('#FFAB00',0.3)",
              ],
              [
                '${RSRP} >=' +
                `${rrp.rrp_2[0]}` +
                '&& ${RSRP} <=' +
                `${rrp.rrp_2[1]}`,
                "color('#FF6F00',0.3)",
              ],
              [
                '${RSRP} >=' +
                `${rrp.rrp_1[0]}` +
                '&& ${RSRP} <=' +
                `${rrp.rrp_1[1]}`,
                "color('#FF0000',0.3)",
              ],
              ['true', "color('#FF0000',0.3)"],
            ],
          },
          pointSize: val,
        })
      }
    },
    //加载仿真数据
    async loadXinhao () {
      if (!this.loadText) {
        this.clickFeatures({
          coverage: await this.getXqCoverage_area(),
          facade_pnts: await this.getFacade_pnts(),
          // rsrp: await this.getRsrp(),
          antenna: await this.getTxJSON(),
        }) //点击
        // rsrpZb = await this.getRsrp()
        // this.calRsrp(await this.getRsrp())
      }
    },
    //加载车
    load_car () {
      if (!this.icon.icon1) {
        this.wymbOpen2 = true
        this.chenjinshiShow = true
        this.icon.icon1 = !this.icon.icon1
        flyTos({
          lon: 108.958750,
          lat: 34.199638,
          height: 569.5329501921358,
          viewer: viewer,
          heading: 0,
          pitch: -25.29,
          roll: 0,
        })
        this.fullScreen()  //判断是否全屏
        this.closeBtns.ion2 = !this.closeBtns.ion2
        this.closeBtns.ion1 = false
        this.closeBtns.ion3 = false
        this.closeBtns.ion4 = true
        this.AnalysisCharsShow = true
      } else {
        this.ImmersiveDataList = []
        this.chenjinshiShow = false
        this.icon.icon1 = !this.icon.icon1
        this.rsrpChartsShow = false  //rsrp折线图
        this.shijiaoShow2 = false   //实时rsrp信号隐藏
        this.AnalysisCharsShow = false
        viewer.scene.screenSpaceCameraController.enableRotate = true
        viewer.scene.screenSpaceCameraController.enableZoom = true
        viewer.scene.screenSpaceCameraController.enableTilt = true
        this.shijiaoShow = false
        this.modelTiaozheng = false
        this.rsrpChartsShow = false //隐藏rsrp图表
        function foo () {
          console.log('keyup')
        }
        viewer.scene.primitives.remove(car1)
        viewer1.scene.primitives.remove(car2)
        car1 = null; car2 = null;
        document.removeEventListener('keyup', foo)
        document.removeEventListener('keydown', foo)
        lines.forEach((item) => {
          viewer.scene.primitives.remove(item)
        })
        pnts.forEach((item) => {
          viewer.entities.remove(item)
        })
        pnts = []
        xinhaolines.forEach(item => {
          viewer.entities.remove(item)
        })
        mimos.forEach(item => {
          viewer.scene.primitives.remove(item)
        })
        xinhaolines = []
        if (intervals) {
          clearInterval(intervals)
          clearInterval(intervals2)
          intervals = null
          intervals2 = null
        }
        flyTos({
          lon: 108.958750,
          lat: 34.199638,
          height: 569.5329501921358,
          viewer: viewer,
          heading: 0,
          pitch: -25.29,
          roll: 0,
        })
        this.fullScreen()  //判断是否全屏
      }
    },
    //操控车
    controlCar () {
      if (this.loadingModel == false && !this.modelTiaozheng) {
        if (this.icon.icon7) {
          this.dialogVisible = true //放置车辆弹框
        } else {
          this.$message.error('请先加载网元设备！')
        }
      } else {
        // this.$message.error('数据加载完成后操作！')
      }
      if (this.modelTiaozheng) {
        
        viewer.scene.primitives.remove(car1)
        viewer1.scene.primitives.remove(car2)
        car1 = null; car2 = null;

        lines.forEach((item) => {
          viewer.scene.primitives.remove(item)
        })
        pnts.forEach((item) => {
          viewer.entities.remove(item)
        })
        pnts = []
        xinhaolines.forEach(item => {
          viewer.entities.remove(item)
        })
        mimos.forEach(item => {
          viewer.scene.primitives.remove(item)
        })
        xinhaolines = []
        this.ImmersiveDataList = []
        this.electricalList = []
        this.carOfUser = true
        this.modelTiaozheng = !this.modelTiaozheng
        this.AnalysisCharsShow = false
        this.SamplingList = false
        this.$message.closeAll()
      }
    },
    //解析车路径
    carLengthjc () {
      let speeds = []
      carlujing.forEach((item) => {
        item.forEach((itemSon, index) => {
          if (index + 1 < item.length) {
            var from = turf.point([item[index].longitude, item[index].dimension]);
            var to = turf.point([item[index + 1].longitude, item[index + 1].dimension]);
            var options = { units: 'kilometers' };
            var distance = parseFloat((turf.distance(from, to, options) * 1000).toFixed(2));
            item[index + 1].lengths = distance + item[index].lengths  //算出总距离
          }
        })

      })
      carlujing.forEach((item) => {
        // item[item.length - 1].lengths / 300 //先通过总长度算出每秒跑多少米
        item.forEach((itemSon, index) => {
          if (index + 1 < item.length) {
            item[index + 1].time = item[index + 1].lengths / (item[item.length - 1].lengths / 70); //用总长度除以每秒跑的距离算出总时间
          }
        })
      })
      this.animation({
        stop: 70,
        url: window.models.carModelUrls.car1,
        url1: window.models.carModelUrls.car2,
        url2: window.models.carModelUrls.car3,
        url3: window.models.carModelUrls.car4,
        url4: window.models.carModelUrls.car5,
        url5: window.models.carModelUrls.car6,
        url6: window.models.carModelUrls.car7,
        url7: window.models.carModelUrls.car8,
        url8: window.models.carModelUrls.car9,
        lane: carlujing
      })
    },
    //加载车行驶方法
    animation (animation) {
      //加载车
      // 起始时间
      let start = Cesium.JulianDate.fromDate(new Date(0, animation.stop))
      // 结束时间
      let stop = Cesium.JulianDate.addSeconds(
        start,
        animation.stop,
        new Cesium.JulianDate()
      )
      // 设置始时钟始时间
      viewer.clock.startTime = start.clone()
      // 设置时钟当前时间
      viewer.clock.currentTime = start.clone()
      // 设置始终停止时间
      viewer.clock.stopTime = stop.clone()
      // 时间速率，数字越大时间过的越快
      viewer.clock.multiplier = 1
      // 时间轴
      viewer.timeline.zoomTo(start, stop)
      // 循环执行,即为2，到达终止时间，重新从起点时间开始
      viewer.clock.clockRange = Cesium.ClockRange.LOOP_STOP
      var s = animation.speed;
      for (var j = 0; j < animation.lane.length; j++) {
        var property = computeFlight(animation.lane[j])
        var planeModel1 = viewer.entities.add({
          id: j == 0 ? 'car1' : j == 1 ? 'car2' : j == 2 ? 'car3' : j == 3 ? 'car4' : j == 4 ? 'car5' : j == 5 ? 'car6' : j == 6 ? 'car7' : j == 7 ? 'car8' : j == 8 ? 'car9' : 'car10',
          // 和时间轴关联
          availability: new Cesium.TimeIntervalCollection([
            new Cesium.TimeInterval({
              start: start,
              stop: stop,
            }),
          ]),
          position: property,
          // 根据所提供的速度计算模型的朝向
          orientation: new Cesium.VelocityOrientationProperty(property),
          // 模型数据
          // orientation: orientation,
          model: {
            uri: j == 0 ? animation.url : j == 1 ? animation.url1 : j == 2 ? animation.url2 : j == 3 ? animation.url3 : j == 4 ? animation.url4 : j == 5 ? animation.url5 : j == 6 ? animation.url6 : j == 7 ? animation.url7 : j == 8 ? animation.url8 : animation.url8,
            scale: 1,
            minimumPixelSize: 25,
            maximumScale: 25,
          },
        })

        cars.push(planeModel1)

        this.carModelList.push({
          name: planeModel1.id,
          speed: ((animation.lane[j][animation.lane[j].length - 1].lengths / 70) * 360 / 1000).toFixed(2),
          location: 0
        })

        function computeFlight (source) {
          // 取样位置 相当于一个集合
          let property = new Cesium.SampledPositionProperty()
          for (let i = 0; i < source.length; i++) {
            let time = Cesium.JulianDate.addSeconds(
              start,
              source[i].time,
              new Cesium.JulianDate()
            )
            let position = Cesium.Cartesian3.fromDegrees(
              source[i].longitude,
              source[i].dimension,
              source[i].height
            )
            // 添加位置，和时间对应
            property.addSample(time, position)
          }
          return property
        }
      }
      setTimeout(() => {
        this.calRsrp()
      }, 2000)
    },
    //计算汽车实时rsrp
    calRsrp (rsrp) {
      let that = this
      setInterval(function () {
        cars.forEach((car, index) => {
          var positionProperty = car._position._property.getValue(viewer.clock.currentTime) // 获取车的当前位置
          var cartographic = Cesium.Cartographic.fromCartesian(positionProperty)
          var lat_String = Cesium.Math.toDegrees(cartographic.latitude).toFixed(4)
          var log_String = Cesium.Math.toDegrees(cartographic.longitude).toFixed(4)
          that.carModelList[index].location = log_String + ',' + lat_String
        })
      }, 100)
    },
    //跟随车
    followCar (val) {
      // if (!this.loadingModel) {
      let that = this
      this.electricalList = []
      this.shijiaoShow2 = true
      this.rsrpChartsShow = true
      RSRPnum = 0
      dataList = []
      RSRPList = []
      if (intervals) {
        clearInterval(intervals)
        clearInterval(intervals2)
        intervals = null
        intervals2 = null
      }
      cars.forEach((car, index) => {
        if (car._id == val.name) {
          intervals = setInterval(() => {
            var positionProperty = car._position._property.getValue(viewer.clock.currentTime)
            let orientation = car.orientation.getValue(viewer.clock.currentTime)
            var mtx3 = Cesium.Matrix3.fromQuaternion(orientation, new Cesium.Matrix3())
            var mtx4 = Cesium.Matrix4.fromRotationTranslation(mtx3, positionProperty, new Cesium.Matrix4());
            var hpr = Cesium.Transforms.fixedFrameToHeadingPitchRoll(mtx4, viewer.scene.globe.ellipsoid, Cesium.Transforms.eastNorthUpToFixedFrame, new Cesium.HeadingPitchRoll());
            //计算经纬度
            var cartographic = Cesium.Cartographic.fromCartesian(positionProperty)
            var lat_String = parseFloat(Cesium.Math.toDegrees(cartographic.latitude))
            var log_String = parseFloat(Cesium.Math.toDegrees(cartographic.longitude))
            var alt = parseFloat(cartographic.height)
            var point = turf.point([log_String, lat_String])
            var distance = -40 / 1000
            var bearing = ((hpr.heading + Cesium.Math.toRadians(90)) * 180) / Math.PI
            var options = { units: 'kilometers' }
            var destination = turf.destination(point, distance, bearing, options)
            cameraCanshu = {
              lon: destination.geometry.coordinates[0],
              lat: destination.geometry.coordinates[1],
              alt: alt,
              heading: hpr.heading
            }
            viewer.camera.setView({
              destination: Cesium.Cartesian3.fromDegrees(
                cameraCanshu.lon,
                cameraCanshu.lat,
                cameraCanshu.alt + 10
              ),
              orientation: {
                // 指向  镜头随小车变化角度
                heading: cameraCanshu.heading + Cesium.Math.toRadians(90),
                // 视角固定
                pitch: Cesium.Math.toRadians(-9.0),
                roll: 0.0,
              },
            })
            this.lonLatObj = {
              longitude: parseFloat(log_String),
              latitude: parseFloat(lat_String),
              pointCount: 4,
              height: 1.5
            }
          })
        }
      })
      intervals2 = setInterval(() => {
        this.queryElectrical(this.lonLatObj, true)
      }, 1000)
      // } else {
      //   this.$message.error('请等待业务数据加载完毕后操作！')
      // }
    },
    //请求指标数据
    async queryElectrical (lonLatObj, flag) {
      const { data: res } = await this.$http({
        method: "post",
        url: window.g.businessUrl,
        headers: {
          "Content-Type": "application/json"
        },
        data: JSON.stringify(lonLatObj),
      });
      if (res.result == -1) {
        this.$message.error(res.errorMsg);
      } else {
        var date = new Date();
        var sign2 = ":";
        var hour = date.getHours(); // 时
        var minutes = date.getMinutes(); // 分
        var seconds = date.getSeconds() //秒
        if (flag) {
          this.electricalList.unshift({
            date: hour + sign2 + minutes + sign2 + seconds,
            lon: lonLatObj.longitude.toFixed(6),
            lat: lonLatObj.latitude.toFixed(6),
            rsrp: res.data[0].rsrp.toFixed(2),
            sinr: res.data[0].pdsch_c.toFixed(2),
            ul: res.data[0].application_channel_throughput_ul.toFixed(2),
            dl: res.data[0].application_channel_throughput_dl.toFixed(2)
          })
          this.rsrpNum = parseFloat(res.data[0].rsrp).toFixed(0) //rsrp
          this.rsrpColor = this.rountColor(res.data[0].rsrp)//rsrp设置颜色
          dataList.push(RSRPnum++)
          RSRPList.push(parseFloat(res.data[0].rsrp).toFixed(2))
          myChart.setOption({
            xAxis: [{ data: dataList }],
            series: [{ data: RSRPList }]
          })
          SINRList.push(parseFloat(res.data[0].pdsch_c).toFixed(2))
          mysinr.setOption({
            xAxis: [{ data: dataList }],
            series: [{ data: SINRList }]
          })
          UpkbpsList.push(parseFloat(res.data[0].application_channel_throughput_ul).toFixed(2))
          DownkbpsList.push(parseFloat(res.data[0].application_channel_throughput_dl).toFixed(2))
          upDownsl.setOption({
            xAxis: [{ data: dataList }],
            series: [{ data: DownkbpsList }, { data: UpkbpsList }]
          })
        } else {
          this.electricalList.push({
            date: hour + sign2 + minutes + sign2 + seconds,
            lon: lonLatObj.longitude.toFixed(6),
            lat: lonLatObj.latitude.toFixed(6),
            rsrp: res.data[0].rsrp.toFixed(2),
            sinr: res.data[0].pdsch_c.toFixed(2),
            ul: res.data[0].application_channel_throughput_ul.toFixed(2),
            dl: res.data[0].application_channel_throughput_dl.toFixed(2)
          })
          //rsrp
          this.rsrpColor = this.rountColor(res.data[0].rsrp) //rsrp设置颜色
          this.rsrpNum = parseFloat(res.data[0].rsrp).toFixed(2) //设置值
          //更新指标
          dataList.push(RSRPnum++)
          RSRPList.push(this.rsrpNum)
          myChart.setOption({
            xAxis: [{ data: dataList }],
            series: [{ data: RSRPList }]
          })
          //sinr
          SINRList.push(parseFloat(res.data[0].pdsch_c).toFixed(2))
          mysinr.setOption({
            xAxis: [{ data: dataList }],
            series: [{ data: SINRList }]
          })
          //上下行速率
          UpkbpsList.push(parseFloat(res.data[0].application_channel_throughput_ul).toFixed(2))
          DownkbpsList.push(parseFloat(res.data[0].application_channel_throughput_dl).toFixed(2))
          upDownsl.setOption({
            xAxis: [{ data: dataList }],
            series: [{ data: DownkbpsList }, { data: UpkbpsList }]
          })
        }
      }
    },
    //取消跟随
    cancelFollow () {
      this.shijiaoShow2 = false
      this.rsrpChartsShow = false
      this.AnalysisCharsShow = false
      clearInterval(intervals)
      clearInterval(intervals2)
      this.electricalList = []
      flyTos({
        lon: 108.958750,
        lat: 34.199638,
        height: 569.5329501921358,
        viewer: viewer,
        heading: 0,
        pitch: -25.29,
        roll: 0,
      })
    },
    //加载观光轨道车
    load_tourCar () {
      var position = Cesium.Cartesian3.fromDegrees(
        108.95969199681146,
        34.20751772983063,
        8.49
      )
      let heading = Cesium.Math.toRadians(0)
      let pitch = Cesium.Math.toRadians(0)
      let roll = Cesium.Math.toRadians(0)
      let hpr = new Cesium.HeadingPitchRoll(heading, pitch, roll)
      let orientation = Cesium.Transforms.headingPitchRollQuaternion(
        position,
        hpr
      )
      let entity = viewer.entities.add({
        name: '观光车',
        position: position,
        orientation: orientation,
        model: {
          url: 'http://*************:59083/models/monomer_model/model/guanguangyungui.gltf',
          minimumPixelSize: 128,
          maximumScale: 128,
          scale: 1, //模型缩放比例
          lightColor: new Cesium.Cartesian3(30, 30, 30), //模型亮度
        },
      })
    },
    //请求小区覆盖范围
    getXqCoverage_area () {
      return new Promise((resolve, reject) => {
        this.$http
          .get(window.Geojson.antennaCoverJson)
          .then((res) => {
            if (res) {
              resolve(res.data)
            }
          })
          .catch((error) => {
            resolve('请求小区覆盖范围请求失败')
          })
      })
    },
    //请求天线安装位置
    getTxJSON () {
      return new Promise((resolve, reject) => {
        this.$http
          .get(window.Geojson.antennaPositionJson)
          .then((res) => {
            if (res) {
              resolve(res.data.features)
            }
          })
          .catch((error) => {
            resolve('请求天线安装位置失败')
          })
      })
    },
    //请求立面点
    getFacade_pnts () {
      return new Promise((resolve, reject) => {
        this.$http
          .get(window.Geojson.facadePositionJson)
          .then((res) => {
            if (res) {
              resolve(res.data)
            }
          })
          .catch((error) => {
            resolve('立面点请求失败')
          })
      })
    },
    //请求rsrp
    getRsrp () {
      return new Promise((resolve, reject) => {
        this.$http
          .get('http://*************:59083/models/JSON/rsrp.json')
          .then((res) => {
            if (res) {
              resolve(res.data)
            }
          })
          .catch((error) => {
            resolve('rsrp请求失败')
          })
      })
    },
    //绘制线
    drawLine (pmaras) {
      // perVertexPolyline = new Cesium.GeometryInstance({
      //   geometry: new Cesium.SimplePolylineGeometry({
      //     positions: Cesium.Cartesian3.fromDegreesArrayHeights(fromDegreesArrayHeights),
      //     colors: colors,
      //     colorsPerVertex: true,
      //   }),
      // })
      // viewer.scene.primitives.add(
      //   new Cesium.Primitive({
      //     geometryInstances: [perVertexPolyline],
      //     appearance: new Cesium.PerInstanceColorAppearance({
      //       flat: true,
      //       renderState: {
      //         lineWidth: Math.min(10.0, viewer.scene.maximumAliasedLineWidth),
      //       },
      //     }),
      //   })
      // )
      perVertexPolyline = viewer.scene.primitives.add(
        new Cesium.Primitive({
          geometryInstances: new Cesium.GeometryInstance({
            geometry: new Cesium.PolylineGeometry({
              positions: Cesium.Cartesian3.fromDegreesArrayHeights(
                pmaras.fromDegreesArrayHeights
              ),
              width: 10.0,
              vertexFormat: Cesium.PolylineColorAppearance.VERTEX_FORMAT,
              colors: pmaras.colors,
              colorsPerVertex: true,
            }),
          }),
          appearance: new Cesium.PolylineColorAppearance(),
        })
      )
    },
    //放置车
    placeModel () {
      this.$message('请在地图上指定模型位置')
      this.carOfUser = false
      this.dialogVisible = false
      this.modelTiaozheng = true
      if (this.form.type == "线") {
        this.trajectoryType = true
        this.switchingStatus = false
      } else {
        this.trajectoryType = false
        this.switchingStatus = true
      }
      if (this.form.model == "人") {
        this.form.speed = 0.3
      }
    },
    //键盘控制车
    keyboard_car (params) {
      RSRPnum = 0
      dataList = []
      RSRPList = []
      this.rsrpChartsShow = true
      let that = this
      //控制车的初始连线
      this.loadLine([
        {
          data: [
            params.lng,
            params.lat,
            params.height,
            params.lng,
            params.lat,
            params.height,
          ],
          color: '#FC6B09',
        },
        {
          data: [
            params.lng,
            params.lat,
            params.height,
            params.lng,
            params.lat,
            params.height,
          ],
          color: '#15BBDC',
        },
        {
          data: [
            params.lng,
            params.lat,
            params.height,
            params.lng,
            params.lat,
            params.height,
          ],
          color: '#EF2323',
        },
        {
          data: [
            params.lng,
            params.lat,
            params.height,
            params.lng,
            params.lat,
            params.height,
          ],
          color: '#E8EF23',
        },
        {
          data: [
            params.lng,
            params.lat,
            params.height,
            params.lng,
            params.lat,
            params.height,
          ],
          color: '#6FEF23',
        },
      ])
      let newStr = [params.lng, params.lat, params.height]
      // 小车旋转角度
      let radian = Cesium.Math.toRadians(2)
      // 小车的速度
      let speed = parseFloat(this.form.speed);
      // 速度矢量
      let speedVector = new Cesium.Cartesian3()
      let scene = viewer.scene
      // 起始位置
      let position = Cesium.Cartesian3.fromDegrees(
        newStr[0],
        newStr[1],
        newStr[2]
      )
      // 用于设置小车方向
      let heading = Cesium.Math.toRadians(0)
      let pitch = 0
      let roll = 0
      let hpRoll = new Cesium.HeadingPitchRoll(heading, pitch, roll)
      let fixedFrameTransforms =
        Cesium.Transforms.localFrameToFixedFrameGenerator('north', 'west')
      // Cesium.Transforms.localFrameToFixedFrameGenerator("south", "east");
      var carPrimitive = scene.primitives.add(
        Cesium.Model.fromGltf({
          id: 'mycar',
          url: this.form.model == "车" ? window.models.tesla_2018_modelUrl : window.models.manModelUrl,
          modelMatrix: Cesium.Transforms.headingPitchRollToFixedFrame(
            position,
            hpRoll,
            Cesium.Ellipsoid.WGS84,
            fixedFrameTransforms
          ),
          scale: this.form.model == "车" ? 0.8 : 0.0002
        })
      )
      // console.log(carPrimitive)
      var count = 0
      carPrimitive.readyPromise.then((model) => {
        this.loadingModel = false
        this.$message.success('模型加载完成')
        model.activeAnimations.addAll({
          loop: Cesium.ModelAnimationLoop.REPEAT,
          speedup: 1, //速度
          reverse: false, //false顺时针  true逆时针
        });
        car1 = model
        if (car1) {
          document.addEventListener('keyup', (e) => {
            setFlagStatus(e, false)
          })

          document.addEventListener('keydown', (e) => {
            setFlagStatus(e, true)
          })

          document.addEventListener('keyup', (e) => {
            setFlagStatus2(e)
          })

          viewer.clock.onTick.addEventListener((clock) => {
            if (flag.moveUp) {
              if (flag.moveLeft) {
                hpRoll.heading -= radian
                count += 2
              }
              if (flag.moveRight) {
                hpRoll.heading += radian
                count -= 2
              }
              moveCar(1)
            } else if (flag.moveDown) {
              if (flag.moveLeft) {
                hpRoll.heading -= radian
                count += 2
              }
              if (flag.moveRight) {
                hpRoll.heading += radian
                count -= 2
              }
              moveCar(-1)
            } else {
              if (flag.moveLeft) {
                hpRoll.heading -= radian
                count += 2
                moveCar(0)
              }
              if (flag.moveRight) {
                hpRoll.heading += radian
                count -= 2
                moveCar(0)
              }
            }
          })
          that.hpRoll = hpRoll
        }
      })

      let carPrimitive1 = viewer1.scene.primitives.add(
        Cesium.Model.fromGltf({
          id: 'mycar',
          url: window.models.carModelUrls.car1,
          modelMatrix: Cesium.Transforms.headingPitchRollToFixedFrame(
            position,
            hpRoll,
            Cesium.Ellipsoid.WGS84,
            fixedFrameTransforms
          ),
          scale: 0.8,
          minimumPixelSize: 40,
          maximumPixelSize: 40,
        })
      )

      car2 = carPrimitive1

      // 小车状态标志
      let flag = {
        moveUp: false,
        moveDown: false,
        moveLeft: false,
        moveRight: false,
      }
      // 根据键盘按键返回标志
      function setFlagStatus (key, value) {
        switch (key.keyCode) {
          case 37:
            // 左
            flag.moveLeft = value
            break
          case 65:
            // 左
            flag.moveLeft = value
            break
          case 38:
            // 上
            flag.moveUp = value
            break
          case 87:
            flag.moveUp = value
            // 上
            break
          case 39:
            // 右
            flag.moveRight = value
            break
          case 68:
            // 右
            flag.moveRight = value
            break
          case 40:
            flag.moveDown = value
            // 下
            break
          case 83:
            flag.moveDown = value
            // 下
            break
        }
      }

      function setFlagStatus2 (key) {
        switch (key.keyCode) {
          case 32:
            document.getElementById('manipulate').click()
            //空格
            break
        }
      }



      // moveCar(true);
      function moveCar (isUP) {
        // 计算速度矩阵
        if (isUP === 1) {
          speedVector = Cesium.Cartesian3.multiplyByScalar(
            Cesium.Cartesian3.UNIT_X,
            speed,
            speedVector
          )
        } else if (isUP === -1) {
          speedVector = Cesium.Cartesian3.multiplyByScalar(
            Cesium.Cartesian3.UNIT_X,
            -speed,
            speedVector
          )
        } else {
          speedVector = Cesium.Cartesian3.multiplyByScalar(
            Cesium.Cartesian3.UNIT_X,
            0,
            speedVector
          )
        }

        Cesium.Matrix4.multiplyByPoint(
          carPrimitive1.modelMatrix,
          speedVector,
          position
        )

        // 根据速度计算出下一个位置的坐标
        position = Cesium.Matrix4.multiplyByPoint(
          carPrimitive.modelMatrix,
          speedVector,
          position
        )

        // carPrimitive._cachedGltf._gltf.nodes.forEach(item => {
        //   if (item.name == 'movsteer_1.0_movsteer_1.0.0_0' || item.name == 'movsteer_1.0_dvorright.0_0') {
        //     console.log(carPrimitive);
        //     let oldMatrix = item.matrix
        //     let oldCenter = new Cesium.Cartesian3(oldMatrix[12], oldMatrix[13], oldMatrix[14]);
        //     const m1 = Cesium.Transforms.eastNorthUpToFixedFrame(Cesium.Matrix4.getTranslation(oldMatrix, new Cesium.Cartesian3()), Cesium.Ellipsoid.WGS84, new Cesium.Matrix4());
        //     const m3 = Cesium.Matrix4.multiply(Cesium.Matrix4.inverse(m1, new Cesium.Matrix4()), oldMatrix, new Cesium.Matrix4());
        //     const mat3 = Cesium.Matrix4.getMatrix3(m3, new Cesium.Matrix3());
        //     const q = Cesium.Quaternion.fromRotationMatrix(mat3);
        //     const hpr = Cesium.HeadingPitchRoll.fromQuaternion(q);
        //     console.log(hpr);
        //     let headingPitchRoll = new Cesium.HeadingPitchRoll(Cesium.Math.toRadians(hpr.heading += radian), Cesium.Math.toRadians(hpr.patch), Cesium.Math.toRadians(hpr.roll));
        //     let m = Cesium.Transforms.headingPitchRollToFixedFrame(oldCenter, headingPitchRoll, Cesium.Ellipsoid.WGS84, Cesium.Transforms.eastNorthUpToFixedFrame, new Cesium.Matrix4());
        //     console.log(m);
        //     item.matrix = m;
        //   }
        // })

        //连线
        let pont_xq = {}
        let llh_old
        let llh1, llh2, llh3, llh4
        let lengArrey = [] //储存车到墙的距离
        var ellipsoid = viewer.scene.globe.ellipsoid
        var cartesian3 = new Cesium.Cartesian3(
          position.x,
          position.y,
          position.z
        )
        var cartographic = ellipsoid.cartesianToCartographic(cartesian3)
        let lon = Cesium.Math.toDegrees(cartographic.longitude).toFixed(5)
        let lats = Cesium.Math.toDegrees(cartographic.latitude).toFixed(5)
        let llh = [
          parseFloat(Cesium.Math.toDegrees(cartographic.longitude).toFixed(5)),
          parseFloat(Cesium.Math.toDegrees(cartographic.longitude).toFixed(5)),
          parseFloat(59),
          parseFloat(lon),
          parseFloat(lats),
          parseFloat(cartographic.height.toFixed(0)),
        ]
        llh_old = llh
        //计算车辆是否进入小区覆盖区域
        params.xiaoqufugai.features.forEach((item) => {
          var pt = turf.point([lon, lats])
          let poly = turf.polygon(item.geometry.coordinates)
          if (turf.booleanPointInPolygon(pt, poly)) {
            params.antenna.forEach((antennaItem) => {
              if (
                item.properties.Transmitte ==
                antennaItem.properties.Transmitte
              ) {
                that.ImmersiveDataList = [
                  {
                    key: '基站ID',
                    value: item.properties.Site,
                  },
                  {
                    key: '天线ID',
                    value: item.properties.Transmitte,
                  },
                  {
                    key: '天线参数',
                    value: item.properties.Antenna,
                  },
                  {
                    key: '天线方位角',
                    value: item.properties.Azimuth,
                  },
                  {
                    key: '天线机械倾角',
                    value: item.properties.Mechanical,
                  }
                ]
                switch (item.properties.安装方式) {
                  case '楼顶抱杆':
                    if (parseFloat(antennaItem.properties.minheight)) {
                      pont_xq.lon = antennaItem.geometry.coordinates[0]
                      pont_xq.lat = antennaItem.geometry.coordinates[1]
                      pont_xq.height =
                        parseFloat(antennaItem.properties.minheight) +
                        2 +
                        0.68
                    } else {
                      pont_xq.lon = antennaItem.geometry.coordinates[0]
                      pont_xq.lat = antennaItem.geometry.coordinates[1]
                      pont_xq.height = 0 + 2 + 0.68
                    }
                    break
                  case '楼顶三角塔':
                    pont_xq.lon = antennaItem.geometry.coordinates[0]
                    pont_xq.lat = antennaItem.geometry.coordinates[1]
                    pont_xq.height = antennaItem.properties.Height + 0.68
                    break
                  case '单管塔':
                    pont_xq.lon =
                      Math.cos(
                        Cesium.Math.toRadians(
                          (antennaItem.properties.Azimuth - 90) * -1
                        )
                      ) *
                      0.00000398 +
                      antennaItem.geometry.coordinates[0]
                    pont_xq.lat =
                      Math.sin(
                        Cesium.Math.toRadians(
                          (antennaItem.properties.Azimuth - 90) * -1
                        )
                      ) *
                      0.00000398 +
                      antennaItem.geometry.coordinates[1]
                    pont_xq.height = antennaItem.properties.Height + 0.68 * 2
                    break
                  case '地面铁塔':
                    pont_xq.lon = antennaItem.geometry.coordinates[0]
                    pont_xq.lat = antennaItem.geometry.coordinates[1]
                    pont_xq.height = antennaItem.properties.Height + 0.68
                    break
                  default:
                    break
                }
              }
            })
            bycTransmitterArray.forEach((antennaItem) => {
              if (
                item.properties.Transmitte ==
                antennaItem.properties.Transmitte
              ) {
                pont_xq.lon = antennaItem.geometry.coordinates[0]
                pont_xq.lat = antennaItem.geometry.coordinates[1]
                pont_xq.height = 20 + 0.68 * 2
                MIMOid.push(antennaItem.properties.Transmitte)
                if (MIMOid[MIMOid.length - 2] != MIMOid[MIMOid.length - 1]) {
                  that.loadMIMO(antennaItem.properties.Transmitte, 20, 2)
                }
              }
            })
            // pont_xq.lon =
            //   Math.cos(
            //     Cesium.Math.toRadians((item.properties.Azimuth - 90) * -1)
            //   ) *
            //   0.000048 +
            //   item.properties.Longitude +
            //   0.000021
            // pont_xq.lat =
            //   Math.sin(
            //     Cesium.Math.toRadians((item.properties.Azimuth - 90) * -1)
            //   ) *
            //   0.000048 + item.properties.Latitude
          }
        })

        //汽车外扩
        let minlon_wk = parseFloat(lon) - 0.003
        let maxlon_wk = parseFloat(lon) + 0.003
        let minlat_wk = parseFloat(lats) - 0.003
        let maxlat_wk = parseFloat(lats) + 0.003

        params.limiandian.forEach((item) => {
          if (
            item.x > minlon_wk &&
            item.x < maxlon_wk &&
            item.y > minlat_wk &&
            item.y < maxlat_wk
          ) {
            //汽车到墙的距离
            let from = turf.point([parseFloat(lon), parseFloat(lats)]) //车
            let to = turf.point([item.x, item.y]) //墙
            let options = { units: 'kilometers' }
            let distance = turf.distance(from, to, options)
            if (distance * 1000 <= 300) {
              //基站到墙的距离
              let from1 = turf.point([
                parseFloat(pont_xq.lon),
                parseFloat(pont_xq.lat),
              ]) //基站
              let to1 = turf.point([parseFloat(item.x), parseFloat(item.y)]) //墙
              let options1 = { units: 'kilometers' }
              let distance1 = turf.distance(from1, to1, options1)
              //基站到车的距离
              let from2 = turf.point([
                parseFloat(pont_xq.lon),
                parseFloat(pont_xq.lat),
              ]) //基站
              let to2 = turf.point([parseFloat(lon), parseFloat(lats)]) //墙
              let options2 = { units: 'kilometers' }
              let distance2 = turf.distance(from2, to2, options2)
              //判断基站到墙大于基站到车
              if (distance1 > distance2) {
                //汽车到墙的距离
                let from = turf.point([parseFloat(lon), parseFloat(lats)]) //车
                let to = turf.point([item.x, item.y]) //墙
                let options = { units: 'kilometers' }
                let distance = turf.distance(from, to, options)
                lengArrey.push({
                  x: item.x,
                  y: item.y,
                  z: item.z,
                  uuid: item.uuid,
                  distance: distance,
                })
              }
            }
          }
        })

        let distance_duan = Math.min.apply(
          Math,
          lengArrey.map((item) => {
            return item.distance
          })
        ) //最短距离

        let distance_duan1 = [] //存入除了最短的其他长度
        let distance_duan1_uuid //最短距离uuid

        //墙距离车最短距离 与 基站和车连线
        lengArrey.forEach((item) => {
          if (item.distance == distance_duan) {
            distance_duan1_uuid = item.uuid
            llh1 = [
              item.x,
              item.y,
              item.z,
              parseFloat(lon),
              parseFloat(lats),
              parseFloat(cartographic.height.toFixed(0)),
            ]
            viewer.entities.getById('purpleArrows2').polyline.positions =
              new Cesium.CallbackProperty(function () {
                return Cesium.Cartesian3.fromDegreesArrayHeights(llh1)
              }, false)
            llh2 = [
              parseFloat(pont_xq.lon),
              parseFloat(pont_xq.lat),
              parseFloat(pont_xq.height),
              item.x,
              item.y,
              item.z,
            ]
            viewer.entities.getById('purpleArrows3').polyline.positions =
              new Cesium.CallbackProperty(function () {
                return Cesium.Cartesian3.fromDegreesArrayHeights(llh2)
              }, false)
          } else {
            distance_duan1.push(item)
          }
        })

        let distance_duan2 = Math.min.apply(
          Math,
          distance_duan1.map((item) => {
            return item.distance
          })
        ) //第二最短距离
        // console.log(distance_duan2)

        //墙距离车第二最短距离 与 基站和车连线
        lengArrey.forEach((item) => {
          if (
            item.distance == distance_duan2 &&
            item.uuid !== distance_duan1_uuid
          ) {
            llh3 = [
              item.x,
              item.y,
              item.z,
              parseFloat(lon),
              parseFloat(lats),
              parseFloat(cartographic.height.toFixed(0)),
            ]
            viewer.entities.getById('purpleArrows4').polyline.positions =
              new Cesium.CallbackProperty(function () {
                return Cesium.Cartesian3.fromDegreesArrayHeights(llh3)
              }, false)
            llh4 = [
              parseFloat(pont_xq.lon),
              parseFloat(pont_xq.lat),
              parseFloat(pont_xq.height),
              item.x,
              item.y,
              item.z,
            ]
            viewer.entities.getById('purpleArrows5').polyline.positions =
              new Cesium.CallbackProperty(function () {
                return Cesium.Cartesian3.fromDegreesArrayHeights(llh4)
              }, false)
          }
        })

        llh = [
          parseFloat(pont_xq.lon),
          parseFloat(pont_xq.lat),
          parseFloat(pont_xq.height),
          parseFloat(lon),
          parseFloat(lats),
          parseFloat(cartographic.height.toFixed(0)),
        ]

        if (llh.toString() != llh_old.toString()) {
          viewer.entities.getById('purpleArrows1').polyline.positions =
            new Cesium.CallbackProperty(function () {
              return Cesium.Cartesian3.fromDegreesArrayHeights(llh)
            }, false)
          llh_old = llh
        }

        that.lonLatObj = {
          longitude: parseFloat(lon),
          latitude: parseFloat(lats),
          pointCount: 4,
          height: 1.5
        }

        that.queryElectrical(that.lonLatObj, false)

        // params.rsrp.forEach((item) => {
        //   if (
        //     parseFloat(lon) > item.xmin &&
        //     parseFloat(lon) < item.xmax &&
        //     parseFloat(lats) > item.ymin &&
        //     parseFloat(lats) < item.ymax
        //   ) {
        //     that.rsrpColor = that.rountColor(item.rsrp) //rsrp设置颜色
        //     that.rsrpNum = parseFloat(item.rsrp).toFixed(2) //设置值
        //     //更新指标
        //     dataList.push(RSRPnum++)
        //     RSRPList.push(item.rsrp)
        //     myChart.setOption({
        //       xAxis: [{ data: dataList }],
        //       series: [{ data: RSRPList }]
        //     })
        //     //绘制信号点
        //     that.loadingPoint({
        //       lon: lon,
        //       lats: lats,
        //       rsrp: item.rsrp,
        //       cartographic: cartographic,
        //       show: that.switchingStatus,
        //     })

        //   }
        // })
        //绘制信号点
        that.loadingPoint({
          lon: lon,
          lats: lats,
          rsrp: that.rsrpNum,
          cartographic: cartographic,
          show: that.switchingStatus,
        })
        //绘制线
        fromDegreesArrayHeights.push(
          parseFloat(lon),
          parseFloat(lats),
          parseFloat(cartographic.height.toFixed(0)) + 1
        )
        colors.push(
          Cesium.Color.fromCssColorString(
            that.rountColor(that.rsrpNum)
          ).withAlpha(0.8)
        )
        if (colors.length > 2) {
          that.loadingLine({
            fromDegreesArrayHeights: fromDegreesArrayHeights,
            colors: colors,
            show: !that.switchingStatus,
          })
        }

        // 小车移动
        Cesium.Transforms.headingPitchRollToFixedFrame(
          position,
          hpRoll,
          Cesium.Ellipsoid.WGS84,
          fixedFrameTransforms,
          carPrimitive.modelMatrix
        )
        // 小车移动
        Cesium.Transforms.headingPitchRollToFixedFrame(
          position,
          hpRoll,
          Cesium.Ellipsoid.WGS84,
          fixedFrameTransforms,
          carPrimitive1.modelMatrix
        )
        //计算相机位置
        var cartesian3 = new Cesium.Cartesian3(
          position.x,
          position.y,
          position.z
        )
        var cartographic =
          scene.globe.ellipsoid.cartesianToCartographic(cartesian3)
        that.cartographic = cartographic
        //控制视角切换
        that.firstPerson({
          cartographic: cartographic,
          hpRoll: hpRoll,
          count: count,
        })
        // var lng =
        //   Cesium.Math.toDegrees(cartographic.longitude) +
        //   0.00001852398509 *
        //   200 *
        //   Math.cos(((270 + count) * 2 * Math.PI) / 360);
        // var lat =
        //   Cesium.Math.toDegrees(cartographic.latitude) +
        //   0.00001852398509 *
        //   200 *
        //   Math.sin(((270 + count) * 2 * Math.PI) / 360);
        // var alt = cartographic.height + 400;
        // viewer.camera.setView({
        //   destination: Cesium.Cartesian3.fromDegrees(lng, lat, alt),
        //   orientation: {
        //     // 指向  镜头随小车变化角度
        //     heading: hpRoll.heading,
        //     // 视角固定
        //     pitch: Cesium.Math.toRadians(-45.0),
        //     roll: 0.0,
        //   },
        // });
      }
    },
    //反射径显示隐藏
    refractionLineShow (v) {
      if (!v) {
        viewer.entities.getById('purpleArrows2').polyline.show = false
        viewer.entities.getById('purpleArrows3').polyline.show = false
        viewer.entities.getById('purpleArrows4').polyline.show = false
        viewer.entities.getById('purpleArrows5').polyline.show = false
      } else {
        viewer.entities.getById('purpleArrows2').polyline.show = true
        viewer.entities.getById('purpleArrows3').polyline.show = true
        viewer.entities.getById('purpleArrows4').polyline.show = true
        viewer.entities.getById('purpleArrows5').polyline.show = true
      }
    },
    //加载点方法
    loadingPoint (pmaras) {
      let gjpnt = viewer.entities.add({
        position: Cesium.Cartesian3.fromDegrees(
          parseFloat(pmaras.lon),
          parseFloat(pmaras.lats),
          parseFloat(pmaras.cartographic.height.toFixed(0)) + 1
        ),
        point: {
          pixelSize: 10, //点的大小
          color: Cesium.Color.fromCssColorString(this.rountColor(pmaras.rsrp)), //颜色
          // distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
          //   0,
          //   6000
          // ),
          show: pmaras.show,
        },
      })
      pnts.push(gjpnt)
    },
    //加载线方法
    loadingLine (pmaras) {
      let perVertexPolyline = viewer.scene.primitives.add(
        new Cesium.Primitive({
          geometryInstances: new Cesium.GeometryInstance({
            geometry: new Cesium.PolylineGeometry({
              positions: Cesium.Cartesian3.fromDegreesArrayHeights(
                pmaras.fromDegreesArrayHeights
              ),
              width: 8.0,
              vertexFormat: Cesium.PolylineColorAppearance.VERTEX_FORMAT,
              colors: pmaras.colors,
              colorsPerVertex: true,
            }),
          }),
          appearance: new Cesium.PolylineColorAppearance(),
          show: pmaras.show,
        })
      )
      lines.push(perVertexPolyline)
    },
    //切换显示方式
    displayUsage (v) {
      if (v) {
        this.switchingStatus = false
        lines.forEach((item, index) => {
          viewer.entities.remove(lines[index - 1])
          item.show = true
        })
        pnts.forEach((item) => {
          item.point.show._value = false
        })
      } else {
        this.switchingStatus = true
        lines.forEach((item) => {
          item.show = false
        })
        pnts.forEach((item) => {
          item.point.show._value = true
        })
      }
    },
    //判断视角高度
    monitorCamera () {
      if (this.form.model == "车") {
        let cartesian3 = new Cesium.Cartesian3(
          viewer.camera.position.x,
          viewer.camera.position.y,
          viewer.camera.position.z
        )
        let cartographic =
          viewer.scene.globe.ellipsoid.cartesianToCartographic(cartesian3)
        let alt = cartographic.height
        if (alt < 30) {
          this.shijiaoShow = true
        } else {
          this.shijiaoShow = false
        }
      }
    },
    //第一人称
    firstPerson (params) {
      this.monitorCamera()
      if (this.shijiao === '第三人称') {
        if (this.form.model == "车") {
          var lng =
            Cesium.Math.toDegrees(params.cartographic.longitude) -
            0 * 5 * Math.cos(((0 + params.count) * 2 * Math.PI) / 360)
          var lat =
            Cesium.Math.toDegrees(params.cartographic.latitude) -
            0 * 5 * Math.sin(((0 + params.count) * 2 * Math.PI) / 360)
          var point = turf.point([lng, lat])
          var distance = -1 / 1000
          var distance1 = -0.3
          var bearing = (params.hpRoll.heading * 180) / Math.PI
          var options = { units: 'kilometers' }
          var destination = turf.destination(point, distance, bearing, options)
          var destination1 = turf.destination(point, distance1, bearing, options)
          var alt = params.cartographic.height + 1.9
          var alt1 = params.cartographic.height + 100
          viewer.camera.setView({
            destination: Cesium.Cartesian3.fromDegrees(
              destination.geometry.coordinates[0],
              destination.geometry.coordinates[1],
              alt
            ),
            orientation: {
              // 指向  镜头随小车变化角度
              heading: params.hpRoll.heading,
              // 视角固定
              pitch: Cesium.Math.toRadians(-4.0),
              roll: 0.0,
            },
          })
          viewer1.camera.setView({
            destination: Cesium.Cartesian3.fromDegrees(
              destination1.geometry.coordinates[0],
              destination1.geometry.coordinates[1],
              alt1
            ),
            orientation: {
              // 指向  镜头随小车变化角度
              heading: params.hpRoll.heading,
              // 视角固定
              pitch: Cesium.Math.toRadians(-15.0),
              roll: 0.0,
            },
          })
          viewer.scene.screenSpaceCameraController.enableRotate = false
          viewer.scene.screenSpaceCameraController.enableZoom = false
          viewer.scene.screenSpaceCameraController.enableTilt = false
        } else {
          this.shijiaoShow2 = true
          this.shijiaoShow = false
          var lng =
            Cesium.Math.toDegrees(params.cartographic.longitude) -
            0 * 5 * Math.cos(((0 + params.count) * 2 * Math.PI) / 360)
          var lat =
            Cesium.Math.toDegrees(params.cartographic.latitude) -
            0 * 5 * Math.sin(((0 + params.count) * 2 * Math.PI) / 360)
          var point = turf.point([lng, lat])
          var distance = -10 / 1000
          var bearing = (params.hpRoll.heading * 180) / Math.PI
          var options = { units: 'kilometers' }
          var destination = turf.destination(point, distance, bearing, options)
          var alt = params.cartographic.height + 2.3
          viewer.camera.setView({
            destination: Cesium.Cartesian3.fromDegrees(
              destination.geometry.coordinates[0],
              destination.geometry.coordinates[1],
              alt
            ),
            orientation: {
              // 指向  镜头随小车变化角度
              heading: params.hpRoll.heading,
              // 视角固定
              pitch: Cesium.Math.toRadians(-4.0),
              roll: 0.0,
            },
          })
        }
      } else {
        if (this.form.model == "车") {
          var lng =
            Cesium.Math.toDegrees(params.cartographic.longitude) +
            0.00001852398509 *
            200 *
            Math.cos(((270 + params.count) * 2 * Math.PI) / 360)
          var lat =
            Cesium.Math.toDegrees(params.cartographic.latitude) +
            0.00001852398509 *
            200 *
            Math.sin(((270 + params.count) * 2 * Math.PI) / 360)
          var alt = params.cartographic.height + 400
          viewer.camera.setView({
            destination: Cesium.Cartesian3.fromDegrees(lng, lat, alt),
            orientation: {
              // 指向  镜头随小车变化角度
              heading: params.hpRoll.heading,
              // 视角固定
              pitch: Cesium.Math.toRadians(-45.0),
              roll: 0.0,
            },
          })
          viewer.scene.screenSpaceCameraController.enableRotate = true
          viewer.scene.screenSpaceCameraController.enableZoom = true
          viewer.scene.screenSpaceCameraController.enableTilt = true
        } else {
          this.shijiaoShow2 = true
          var lng =
            Cesium.Math.toDegrees(params.cartographic.longitude) +
            0.00001852398509 *
            100 *
            Math.cos(((270 + params.count) * 2 * Math.PI) / 360)
          var lat =
            Cesium.Math.toDegrees(params.cartographic.latitude) +
            0.00001852398509 *
            100 *
            Math.sin(((270 + params.count) * 2 * Math.PI) / 360)
          var alt = params.cartographic.height + 200
          viewer.camera.setView({
            destination: Cesium.Cartesian3.fromDegrees(lng, lat, alt),
            orientation: {
              // 指向  镜头随小车变化角度
              heading: params.hpRoll.heading,
              // 视角固定
              pitch: Cesium.Math.toRadians(-45.0),
              roll: 0.0,
            },
          })
          viewer.scene.screenSpaceCameraController.enableRotate = true
          viewer.scene.screenSpaceCameraController.enableZoom = true
          viewer.scene.screenSpaceCameraController.enableTilt = true
        }
      }
    },
    //控制车的初始连线
    loadLine (data) {
      let getCustomMaterialLine = (image, color) => {
        return new Cesium.CustomMaterialLine({
          image: image,
          color: color,
          duration: 1000,
        })
      }
      data.forEach((item, index) => {
        let lianxian = viewer.entities.add({
          id: 'purpleArrows' + (index + 1),
          polyline: {
            positions: Cesium.Cartesian3.fromDegreesArrayHeights(item.data),
            width: 2,
            arcType: 1,
            material: getCustomMaterialLine(
              colorss,
              Cesium.Color.fromCssColorString(item.color).withAlpha(0.7)
            ),
          },
        })
        xinhaolines.push(lianxian)
      })
    },
    //加载网元
    loadwangyuan () {
      if (!this.icon.icon5) {
        this.loadBase(jizhanshuju)
        this.loadTxzj(tianxianshuju)
        this.loadTxJSON()
        // flyTos({
        //   lon: 108.959,
        //   lat: 34.2054,
        //   height: 1400,
        //   viewer: viewer,
        //   heading: 0,
        //   pitch: -35,
        //   roll: 0,
        // })
      } else {
        if (WebsiteArray.length > 0) {
          WebsiteArray.forEach((item) => {
            viewer.scene.primitives.remove(item)
          })
          azjArray.forEach((item) => {
            viewer.scene.primitives.remove(item)
          })
          txzjArray.forEach((item) => {
            viewer.scene.primitives.remove(item)
          })
          antennaAry.forEach((item) => {
            viewer.scene.primitives.remove(item)
          })
          billboardArray.forEach((item) => {
            viewer.entities.remove(item)
          })
          PanoramaArray.forEach((item) => {
            viewer.entities.remove(item)
          })
        }
      }
    },
    //获取站点数据
    async loadzhandain () {
      await this.$http
        .get(window.Geojson.siteJson)
        .then((res) => {
          if (res) {
            jizhanshuju = res.data.features
          }
        })
    },
    //按照基站类型加载基站
    loadBase (pmaras) {
      pmaras.forEach((item) => {
        switch (item.properties.安装方) {
          case '楼顶抱杆':
            this.loadWebsite({
              lon: item.geometry.coordinates[0],
              lat: item.geometry.coordinates[1],
              height: parseFloat(item.properties.minheight) || 0,
              url: window.models.bgModelUrl,
              scale: 1,
              color: '#42FF73',
            })
            if (parseFloat(item.properties.minheight)) {
              this.addMultipleBillboards({
                id: item.properties.Site,
                lon: item.geometry.coordinates[0],
                lat: item.geometry.coordinates[1],
                height: parseFloat(item.properties.minheight) + 10,
                image: base3,
                width: 32,
                height2: 32,
              })
            } else {
              this.addMultipleBillboards({
                id: item.properties.Site,
                lon: item.geometry.coordinates[0],
                lat: item.geometry.coordinates[1],
                height: 10,
                image: base3,
                width: 32,
                height2: 32,
              })
            }
            break
          case '楼顶三角塔':
            this.loadWebsite({
              lon: item.geometry.coordinates[0],
              lat: item.geometry.coordinates[1],
              height: parseFloat(item.properties.minheight) || 0,
              url: window.models.sjtModelUrl,
              scale: 1,
              color: '#FB9526',
            })
            //安装架
            this.loadAzj({
              lon: item.geometry.coordinates[0],
              lat: item.geometry.coordinates[1],
              height: item.properties.Height,
              url: window.models.ldsjtazjModelUrl,
              scale: 1,
            })
            this.addMultipleBillboards({
              id: item.properties.Site,
              lon: item.geometry.coordinates[0],
              lat: item.geometry.coordinates[1],
              height: item.properties.Height + 35,
              image: base2,
              width: 32,
              height2: 32,
            })
            break
          case '单管塔':
            this.loadWebsite({
              lon: item.geometry.coordinates[0],
              lat: item.geometry.coordinates[1],
              height: item.properties.Height - 80 + 3,
              url: window.models.dgtModelUrl,
              scale: 1,
              color: '#E726FB',
              size: 1,
            })
            this.addMultipleBillboards({
              id: item.properties.Site,
              lon: item.geometry.coordinates[0],
              lat: item.geometry.coordinates[1],
              height: item.properties.Height + 25,
              image: base,
              width: 32,
              height2: 32,
            })
            break
          case '地面铁塔':
            this.loadWebsite({
              lon: item.geometry.coordinates[0],
              lat: item.geometry.coordinates[1],
              height: item.properties.Height - 110 + 3,
              url: 'http://*************:59083/models/gltf/%E5%AE%A4%E5%A4%96%E9%93%81%E5%A1%942.gltf',
              scale: 1,
              color: '#FB264D',
            })
            this.loadAzj({
              lon: item.geometry.coordinates[0],
              lat: item.geometry.coordinates[1],
              height: item.properties.Height,
              url: 'http://*************:59083/models/gltf/%E6%A5%BC%E9%A1%B6%E4%B8%89%E8%A7%92%E5%A1%94%E5%AE%89%E8%A3%85%E6%9E%B6.gltf',
              scale: 1,
            })
            this.addMultipleBillboards({
              id: item.properties.Site,
              lon: item.geometry.coordinates[0],
              lat: item.geometry.coordinates[1],
              height: item.properties.Height + 35,
              image: base1,
              width: 32,
              height2: 32,
            })
            break
          default:
            break
        }
      })
    },
    //加载基站标注
    addMultipleBillboards (pmaras) {
      let scale = 0.7;
      let ha = true;
      let billboards = viewer.entities.add({
        id: pmaras.id,
        position: Cesium.Cartesian3.fromDegrees(pmaras.lon,
          pmaras.lat,
          pmaras.height),
        billboard: { //图标
          image: pmaras.image,
          width: pmaras.width,
          height: pmaras.height2,
          scale: new Cesium.CallbackProperty(function () {
            if (ha) {
              scale += (Math.floor(Math.random() * 10 + 1) * 0.01)
              if (scale >= 1) {
                ha = false
              }
            } else {
              scale -= (Math.floor(Math.random() * 10 + 1) * 0.01)
              if (scale <= 0.7) {
                ha = true
              }
            }
            return scale
          }, false),
          scaleByDistance: new Cesium.NearFarScalar(1.5e2, 1, 60000, 0.0),
          // pixelOffset: new this.Cesium.Cartesian2(100, -35),   //偏移量
        },
      });
      billboardArray.push(billboards)
    },
    //加载全景图
    loadTours (pmaras) {
      let width = 32;
      let ha = true;
      let tours = viewer.entities.add({
        name: pmaras.name,
        position: Cesium.Cartesian3.fromDegrees(
          pmaras.lon,
          pmaras.lat,
          pmaras.height),
        billboard: { //图标
          image: pmaras.image,
          width: new Cesium.CallbackProperty(function () {
            if (ha) {
              width -= Math.floor(Math.random() * 3 + 1)
              if (width <= 1) {
                ha = false
              }
            } else {
              width += Math.floor(Math.random() * 3 + 1)
              if (width >= 32) {
                ha = true
              }
            }
            return width
          }, false),
          height: pmaras.height2,
          scale: 1,
          scaleByDistance: new Cesium.NearFarScalar(1.5e2, 1, 60000, 0.0),
          // pixelOffset: new this.Cesium.Cartesian2(100, -35),   //偏移量
        },
      });
      PanoramaArray.push(tours)
    },
    //加载标注
    loadSlz (pmaras) {
      let position = Cesium.Cartesian3.fromDegrees(
        pmaras.lon,
        pmaras.lat,
        pmaras.height
      )
      let heading = 0
      function diaoyong () {
        heading = heading + Cesium.Math.toRadians(10)
        var hpr = new Cesium.HeadingPitchRoll(heading, 0, 0)
        var orientation = Cesium.Transforms.headingPitchRollQuaternion(
          position,
          hpr
        )
        return orientation
      }
      viewer.entities.add({
        position: position, //椎体位置
        //通过CallbackProperty延迟回调函数一直调用封装的偏航角方法
        //false，返回的值如果改变则一直调用自身，diaoyong()返回的值是orientation，而orientation会根据每次heading 的不同而发生改变
        orientation: new Cesium.CallbackProperty(diaoyong, false),
        model: {
          show: true,
          uri: 'http://*************:59083/models/gltf/sileizhui1.gltf',
          scale: 5,
          minimumPixelSize: 10,
          maximumScale: 10,
        },
      })
    },
    //请求天线支架服务
    async loadTxzjJSON () {
      await this.$http
        .get(
          window.Geojson.txzjJson
        )
        .then((res) => {
          if (res) {
            tianxianshuju = res.data.features
          }
        })
    },
    //加载天线支架
    loadTxzj (pmaras) {
      pmaras.forEach((item) => {
        switch (item.properties.安装方) {
          case '楼顶抱杆':
            this.xiaoqubaogan_loding({
              lon: item.geometry.coordinates[0],
              lat: item.geometry.coordinates[1],
              height: parseFloat(item.properties.minheight) + 2 || 0 + 2,
              Azimuth: (item.properties.Azimuth - 90) * -1,
              url: window.models.dgtModelUrl,
              id: item.properties,
              scale: 1,
            })
            break
          case '楼顶三角塔':
            this.xiaoqubaogan_loding({
              lon: item.geometry.coordinates[0],
              lat: item.geometry.coordinates[1],
              height: item.properties.Height,
              Azimuth: (item.properties.Azimuth - 90) * -1,
              url: window.models.dgtModelUrl,
              id: item.properties,
              scale: 1,
            })
            break
          case '单管塔':
            this.xiaoqubaogan_loding({
              lon: item.geometry.coordinates[0],
              lat: item.geometry.coordinates[1],
              height: item.properties.Height,
              Azimuth: (item.properties.Azimuth - 90) * -1,
              url: window.models.dgtModelUrl,
              id: item.properties,
              scale: 2,
            })
            break
          case '地面铁塔':
            this.xiaoqubaogan_loding({
              lon: item.geometry.coordinates[0],
              lat: item.geometry.coordinates[1],
              height: item.properties.Height,
              Azimuth: (item.properties.Azimuth - 90) * -1,
              url: window.models.dgtModelUrl,
              id: item.properties,
              scale: 1,
            })
            break
          default:
            break
        }
      })
    },
    //请求天线服务
    async loadTxJSON () {
      await this.$http
        .get(
          window.Geojson.antennaPositionJson
        )
        .then((res) => {
          if (res) {
            antennaArray = res.data.features
            this.loadTx(res.data.features)
          }
        })
    },
    //加载天线
    loadTx (pmaras) {
      pmaras.forEach((item) => {
        switch (item.properties.安装方) {
          case '楼顶抱杆':
            if (parseFloat(item.properties.minheight)) {
              this.loadXiaoqu({
                id: item.properties.Transmitte,
                lon: item.geometry.coordinates[0],
                lat: item.geometry.coordinates[1],
                height: parseFloat(item.properties.minheight) + 2 + 0.68,
                Azimuth: (item.properties.Azimuth - 90) * -1,
                url: window.models.transmitterModelUrl,
                scale: 1,
              })
            } else {
              this.loadXiaoqu({
                id: item.properties.Transmitte,
                lon: item.geometry.coordinates[0],
                lat: item.geometry.coordinates[1],
                height: 0 + 2 + 0.68,
                Azimuth: (item.properties.Azimuth - 90) * -1,
                url: window.models.transmitterModelUrl,
                scale: 1,
              })
            }
            break
          case '楼顶三角塔':
            this.loadXiaoqu({
              id: item.properties.Transmitte,
              lon: item.geometry.coordinates[0],
              lat: item.geometry.coordinates[1],
              height: item.properties.Height + 0.68,
              Azimuth: (item.properties.Azimuth - 90) * -1,
              url: window.models.transmitterModelUrl,
              scale: 1,
            })
            break
          case '单管塔':
            this.loadXiaoqu({
              id: item.properties.Transmitte,
              lon:
                Math.cos(
                  Cesium.Math.toRadians((item.properties.Azimuth - 90) * -1)
                ) *
                0.00000398 +
                item.geometry.coordinates[0],
              lat:
                Math.sin(
                  Cesium.Math.toRadians((item.properties.Azimuth - 90) * -1)
                ) *
                0.00000398 +
                item.geometry.coordinates[1],
              height: item.properties.Height + 0.68 * 2,
              Azimuth: (item.properties.Azimuth - 90) * -1,
              url: window.models.transmitterModelUrl,
              scale: 2,
            })
            break
          case '地面铁塔':
            this.loadXiaoqu({
              id: item.properties.Transmitte,
              lon: item.geometry.coordinates[0],
              lat: item.geometry.coordinates[1],
              height: item.properties.Height + 0.68,
              Azimuth: (item.properties.Azimuth - 90) * -1,
              url: window.models.transmitterModelUrl,
              scale: 1,
            })
            break
          default:
            break
        }
      })
    },
    //加载波束
    loadMIMO (Transmitte, height, num) {
      var scales = [10, 7, 7, 4, 4, 2, 2]
      var jiaodu = [0, -17, 17, -40, 40, -70, 70]
      var models = window.models.mimoModels
      if (num != 1) {
        if (mimos.length > 0) {
          mimos.forEach((item) => {
            viewer.scene.primitives.remove(item)
          })
        }
        bycTransmitterArray.forEach((item) => {
          if (item.properties.Transmitte == Transmitte) {
            scales.forEach((itemSon, index) => {
              let cx =
                Math.cos(
                  Cesium.Math.toRadians((item.properties.Azimuth - 90) * -1)
                ) *
                0.00000398 +
                item.geometry.coordinates[0]
              let cy =
                Math.sin(
                  Cesium.Math.toRadians((item.properties.Azimuth - 90) * -1)
                ) *
                0.00000398 +
                item.geometry.coordinates[1]
              let position = Cesium.Cartesian3.fromDegrees(
                cx,
                cy,
                height + 0.68 * 2
              )
              let heading = Cesium.Math.toRadians(
                item.properties.Azimuth + 190 * -1
              )
              let pitch = Cesium.Math.toRadians(jiaodu[index])
              let roll = Cesium.Math.toRadians(-3)
              let hpRolls = new Cesium.HeadingPitchRoll(heading, pitch, roll)
              let fixedFrameTransforms =
                Cesium.Transforms.localFrameToFixedFrameGenerator(
                  'south',
                  'east'
                )
              var models2 = viewer.scene.primitives.add(
                Cesium.Model.fromGltf({
                  position: position,
                  url: models[index],
                  modelMatrix: Cesium.Transforms.headingPitchRollToFixedFrame(
                    position,
                    hpRolls,
                    Cesium.Ellipsoid.WGS84,
                    fixedFrameTransforms
                  ),
                  scale: itemSon,
                  lightColor: new Cesium.Cartesian3(100, 100, 100),
                  color: new Cesium.Color(1, 1, 1, 0.4),
                })
              )
              mimos.push(models2)
            })
          }
        })
      } else {
        bycTransmitterArray.forEach((item) => {
          if (item.properties.Transmitte == Transmitte) {
            scales.forEach((itemSon, index) => {
              let cx =
                Math.cos(
                  Cesium.Math.toRadians((item.properties.Azimuth - 90) * -1)
                ) *
                0.00000398 +
                item.geometry.coordinates[0]
              let cy =
                Math.sin(
                  Cesium.Math.toRadians((item.properties.Azimuth - 90) * -1)
                ) *
                0.00000398 +
                item.geometry.coordinates[1]
              let position = Cesium.Cartesian3.fromDegrees(
                cx,
                cy,
                height + 0.68 * 2
              )
              let heading = Cesium.Math.toRadians(
                item.properties.Azimuth + 190 * -1
              )
              let pitch = Cesium.Math.toRadians(jiaodu[index])
              let roll = Cesium.Math.toRadians(-3)
              let hpRolls = new Cesium.HeadingPitchRoll(heading, pitch, roll)
              let fixedFrameTransforms =
                Cesium.Transforms.localFrameToFixedFrameGenerator(
                  'south',
                  'east'
                )
              var models2 = viewer.scene.primitives.add(
                Cesium.Model.fromGltf({
                  position: position,
                  url: models[index],
                  modelMatrix: Cesium.Transforms.headingPitchRollToFixedFrame(
                    position,
                    hpRolls,
                    Cesium.Ellipsoid.WGS84,
                    fixedFrameTransforms
                  ),
                  scale: itemSon,
                  lightColor: new Cesium.Cartesian3(100, 100, 100),
                  color: new Cesium.Color(1, 1, 1, 0.4),
                })
              )
              mimos.push(models2)
            })
          }
        })
      }
      // setInterval(() => {
      //   mimos.forEach((item) => {
      //     if (item.scale <= 20) {
      //       item.scale += 0.02
      //     } else {
      //       item.scale = 1
      //     }
      //   })
      // }, 100)
    },
    //基站
    loadWebsite (data) {
      let lon = data.lon
      let lat = data.lat
      let alt = data.height
      let position = Cesium.Cartesian3.fromDegrees(lon, lat, alt)
      let heading = Cesium.Math.toRadians(data.heading || 0)
      let pitch = Cesium.Math.toRadians(0)
      let roll = Cesium.Math.toRadians(0)
      let hpRolls = new Cesium.HeadingPitchRoll(heading, pitch, roll)
      let fixedFrameTransforms =
        Cesium.Transforms.localFrameToFixedFrameGenerator('south', 'east')
      let models3 = viewer.scene.primitives.add(
        Cesium.Model.fromGltf({
          position: position,
          url: data.url,
          modelMatrix: Cesium.Transforms.headingPitchRollToFixedFrame(
            position,
            hpRolls,
            Cesium.Ellipsoid.WGS84,
            fixedFrameTransforms
          ),
          scale: data.scale,
          lightColor: data.lightColor || new Cesium.Cartesian3(20, 20, 20),
          color: new Cesium.Color(1, 1, 1, 1),
          // distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
          //   0,
          //   5000
          // ),
        })
      )
      WebsiteArray.push(models3)
      // viewer.entities.add({
      //   position: position,
      //   point: {
      //     pixelSize: 10, //点的大小
      //     color: Cesium.Color.fromCssColorString(data.color), //颜色
      //     show: true,
      //   },
      // })
    },
    //加载安装架
    loadAzj (data) {
      let lon = data.lon
      let lat = data.lat
      let alt = data.height
      let position = Cesium.Cartesian3.fromDegrees(lon, lat, alt)
      let heading = Cesium.Math.toRadians(0)
      let pitch = Cesium.Math.toRadians(0)
      let roll = Cesium.Math.toRadians(0)
      let hpRolls = new Cesium.HeadingPitchRoll(heading, pitch, roll)
      let fixedFrameTransforms =
        Cesium.Transforms.localFrameToFixedFrameGenerator('south', 'east')
      let model = viewer.scene.primitives.add(
        Cesium.Model.fromGltf({
          position: position,
          url: data.url,
          modelMatrix: Cesium.Transforms.headingPitchRollToFixedFrame(
            position,
            hpRolls,
            Cesium.Ellipsoid.WGS84,
            fixedFrameTransforms
          ),
          scale: data.scale,
          lightColor: new Cesium.Cartesian3(20, 20, 20),
          color: new Cesium.Color(1, 1, 1, 1),
          // distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
          //   0,
          //   5000
          // ),
        })
      )
      azjArray.push(model)
    },
    //基站扇形
    drawBasePoint (data) {
      var cx = Math.cos(Cesium.Math.toRadians(data.aoa)) * data.juli + data.lng
      var cy = Math.sin(Cesium.Math.toRadians(data.aoa)) * data.juli + data.lat
      //Cesium.Math.toRadians(data.aoa) data.aoa是扇形的朝向
      let headingd = Cesium.Math.toRadians(360 - data.aoa)
      let pitchd = Cesium.Math.toRadians(data.mechanical * -1)
      let rolld = Cesium.Math.toRadians(0)
      let hpRolls2 = new Cesium.HeadingPitchRoll(headingd, pitchd, rolld)
      let shanxin = viewer.entities.add({
        id: data.id,
        name: '扇形' + data.aoa,
        position: Cesium.Cartesian3.fromDegrees(cx, cy, 59),
        orientation: Cesium.Transforms.headingPitchRollQuaternion(
          Cesium.Cartesian3.fromDegrees(cx, cy, 59),
          hpRolls2
        ),
        ellipsoid: {
          radii: new Cesium.Cartesian3(130.0, 130.0, 130.0), // 扇形半径
          innerRadii: new Cesium.Cartesian3(1.0, 1.0, 1.0), // 内半径
          minimumClock: Cesium.Math.toRadians(-25), // 左右偏角
          maximumClock: Cesium.Math.toRadians(25),
          minimumCone: Cesium.Math.toRadians(90), // 上下偏角  可以都设置为90
          maximumCone: Cesium.Math.toRadians(75),
          material: new Cesium.CircleRippleMaterialProperty({
            color: Cesium.Color.fromCssColorString(data.color).withAlpha(0.6),
            speed: 6.0,
            count: 4,
            gradient: 0.2,
          }),
          // material: new Cesium.CircleSpiralMaterialProperty({
          //   color: Cesium.Color.fromCssColorString(
          //     data.color
          //   ).withAlpha(0.6),
          //   speed: 10.0
          // }),
          outline: false,
          outlineColor: Cesium.Color.fromCssColorString(data.color),
        },
      })
    },
    //实例化抱杆
    xiaoqubaogan_loding (pmaras) {
      let position3 = new Cesium.Cartesian3.fromDegrees(
        pmaras.lon,
        pmaras.lat,
        pmaras.height
      )
      var headingd = Cesium.Math.toRadians(pmaras.Azimuth)
      var pitchd = Cesium.Math.toRadians(180)
      var rolld = Cesium.Math.toRadians(0)
      let hpRolls2 = new Cesium.HeadingPitchRoll(headingd, pitchd, rolld)
      let fixedFrameTransforms2 =
        Cesium.Transforms.localFrameToFixedFrameGenerator('east', 'south')
      var models3 = viewer.scene.primitives.add(
        Cesium.Model.fromGltf({
          position: position3,
          url: pmaras.url,
          modelMatrix: Cesium.Transforms.headingPitchRollToFixedFrame(
            position3,
            hpRolls2,
            Cesium.Ellipsoid.WGS84,
            fixedFrameTransforms2
          ),
          scale: pmaras.scale,
          lightColor: new Cesium.Cartesian3(20, 20, 20),
          color: new Cesium.Color(1, 1, 1, 1),
          distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
            0,
            5000
          ),
        })
      )
      txzjArray.push(models3)
    },
    //加载小区
    loadXiaoqu (pmaras) {
      var cx = pmaras.lon
      var cy = pmaras.lat
      let position3 = Cesium.Cartesian3.fromDegrees(cx, cy, pmaras.height)
      var headingd = Cesium.Math.toRadians(pmaras.Azimuth)
      var pitchd = Cesium.Math.toRadians(0)
      var rolld = Cesium.Math.toRadians(0)
      let hpRolls2 = new Cesium.HeadingPitchRoll(headingd, pitchd, rolld)
      let fixedFrameTransforms2 =
        Cesium.Transforms.localFrameToFixedFrameGenerator('east', 'south')
      var xiaoqu = viewer.scene.primitives.add(
        Cesium.Model.fromGltf({
          id: pmaras.id,
          position: position3,
          url: pmaras.url,
          modelMatrix: Cesium.Transforms.headingPitchRollToFixedFrame(
            position3,
            hpRolls2,
            Cesium.Ellipsoid.WGS84,
            fixedFrameTransforms2
          ),
          scale: pmaras.scale,
          lightColor: new Cesium.Cartesian3(20, 20, 20),
          color: new Cesium.Color(1, 1, 1, 1),
          distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
            0,
            5000
          ),
        })
      )
      antennaAry.push(xiaoqu)
    },
    //机械倾角调整
    azimuthAdjustment (val) {
      if (this.tableData.length > 0) {
        this.tableData[6].value += val
      }
      if (pickObject.length > 0) {
        let oldMatrix = pickObject[0].primitive.modelMatrix
        let oldCenter = new Cesium.Cartesian3(
          oldMatrix[12],
          oldMatrix[13],
          oldMatrix[14]
        )
        let m1 = Cesium.Transforms.eastNorthUpToFixedFrame(
          Cesium.Matrix4.getTranslation(oldMatrix, new Cesium.Cartesian3()),
          Cesium.Ellipsoid.WGS84,
          new Cesium.Matrix4()
        )
        let m3 = Cesium.Matrix4.multiply(
          Cesium.Matrix4.inverse(m1, new Cesium.Matrix4()),
          oldMatrix,
          new Cesium.Matrix4()
        )
        let mat3 = Cesium.Matrix4.getMatrix3(m3, new Cesium.Matrix3())
        let q = Cesium.Quaternion.fromRotationMatrix(mat3)
        let hpr = Cesium.HeadingPitchRoll.fromQuaternion(q)
        let headingPitchRoll = new Cesium.HeadingPitchRoll(
          hpr.heading,
          Cesium.Math.toRadians(val),
          hpr.roll
        )
        let m = Cesium.Transforms.headingPitchRollToFixedFrame(
          oldCenter,
          headingPitchRoll,
          Cesium.Ellipsoid.WGS84,
          Cesium.Transforms.eastNorthUpToFixedFrame,
          new Cesium.Matrix4()
        )
        pickObject[0].primitive.modelMatrix = m
      }
      if (mimos.length > 0) {
        mimos.forEach((item) => {
          let oldMatrix = item.modelMatrix
          let oldCenter = new Cesium.Cartesian3(
            oldMatrix[12],
            oldMatrix[13],
            oldMatrix[14]
          )
          let m1 = Cesium.Transforms.eastNorthUpToFixedFrame(
            Cesium.Matrix4.getTranslation(oldMatrix, new Cesium.Cartesian3()),
            Cesium.Ellipsoid.WGS84,
            new Cesium.Matrix4()
          )
          let m3 = Cesium.Matrix4.multiply(
            Cesium.Matrix4.inverse(m1, new Cesium.Matrix4()),
            oldMatrix,
            new Cesium.Matrix4()
          )
          let mat3 = Cesium.Matrix4.getMatrix3(m3, new Cesium.Matrix3())
          let q = Cesium.Quaternion.fromRotationMatrix(mat3)
          let hpr = Cesium.HeadingPitchRoll.fromQuaternion(q)
          let headingPitchRoll = new Cesium.HeadingPitchRoll(
            hpr.heading,
            hpr.pitch += Cesium.Math.toRadians(val),
            hpr.roll
          )
          let m = Cesium.Transforms.headingPitchRollToFixedFrame(
            oldCenter,
            headingPitchRoll,
            Cesium.Ellipsoid.WGS84,
            Cesium.Transforms.eastNorthUpToFixedFrame,
            new Cesium.Matrix4()
          )
          item.modelMatrix = m
        })
      }
    },
    //方位角调整
    mechanicalAdjustment (val) {
      if (this.tableData.length > 0) {
        this.tableData[3].value += val
      }
      if (pickObject.length > 0) {
        let oldMatrix = pickObject[0].primitive.modelMatrix
        let oldCenter = new Cesium.Cartesian3(
          oldMatrix[12],
          oldMatrix[13],
          oldMatrix[14]
        )
        let m1 = Cesium.Transforms.eastNorthUpToFixedFrame(
          Cesium.Matrix4.getTranslation(oldMatrix, new Cesium.Cartesian3()),
          Cesium.Ellipsoid.WGS84,
          new Cesium.Matrix4()
        )
        let m3 = Cesium.Matrix4.multiply(
          Cesium.Matrix4.inverse(m1, new Cesium.Matrix4()),
          oldMatrix,
          new Cesium.Matrix4()
        )
        let mat3 = Cesium.Matrix4.getMatrix3(m3, new Cesium.Matrix3())
        let q = Cesium.Quaternion.fromRotationMatrix(mat3)
        let hpr = Cesium.HeadingPitchRoll.fromQuaternion(q)
        let headingPitchRoll = new Cesium.HeadingPitchRoll(
          hpr.heading += Cesium.Math.toRadians(val),
          hpr.pitch,
          hpr.roll
        )
        let m = Cesium.Transforms.headingPitchRollToFixedFrame(
          oldCenter,
          headingPitchRoll,
          Cesium.Ellipsoid.WGS84,
          Cesium.Transforms.eastNorthUpToFixedFrame,
          new Cesium.Matrix4()
        )
        pickObject[0].primitive.modelMatrix = m
      }
      if (mimos.length > 0) {
        mimos.forEach((item) => {
          let oldMatrix = item.modelMatrix
          let oldCenter = new Cesium.Cartesian3(
            oldMatrix[12],
            oldMatrix[13],
            oldMatrix[14]
          )
          let m1 = Cesium.Transforms.eastNorthUpToFixedFrame(
            Cesium.Matrix4.getTranslation(oldMatrix, new Cesium.Cartesian3()),
            Cesium.Ellipsoid.WGS84,
            new Cesium.Matrix4()
          )
          let m3 = Cesium.Matrix4.multiply(
            Cesium.Matrix4.inverse(m1, new Cesium.Matrix4()),
            oldMatrix,
            new Cesium.Matrix4()
          )
          let mat3 = Cesium.Matrix4.getMatrix3(m3, new Cesium.Matrix3())
          let q = Cesium.Quaternion.fromRotationMatrix(mat3)
          let hpr = Cesium.HeadingPitchRoll.fromQuaternion(q)
          let headingPitchRoll = new Cesium.HeadingPitchRoll(
            hpr.heading += Cesium.Math.toRadians(val),
            hpr.pitch,
            hpr.roll
          )
          let m = Cesium.Transforms.headingPitchRollToFixedFrame(
            oldCenter,
            headingPitchRoll,
            Cesium.Ellipsoid.WGS84,
            Cesium.Transforms.eastNorthUpToFixedFrame,
            new Cesium.Matrix4()
          )
          item.modelMatrix = m
        })
      }
    },
    //随机颜色方法
    randomColor () {
      let col = '#'
      for (let i = 0; i < 6; i++)
        col += parseInt(Math.random() * 16).toString(16)
      return col
    },
    //天空
    skyboxs (skyImg) {
      let groundSkybox = new Cesium.GroundSkyBox({
        sources: {
          positiveX: skyImg.positiveX,
          negativeX: skyImg.negativeX,
          positiveY: skyImg.positiveY,
          negativeY: skyImg.negativeY,
          positiveZ: skyImg.positiveZ,
          negativeZ: skyImg.negativeZ,
        },
      })
      let defaultSkybox = viewer.scene.skyBox
      // // 渲染前监听并判断相机位置
      viewer.scene.preUpdate.addEventListener(() => {
        let position = viewer.scene.camera.position
        let cameraHeight = Cesium.Cartographic.fromCartesian(position).height
        if (cameraHeight < 240000) {
          viewer.scene.skyBox = groundSkybox
          viewer.scene.skyAtmosphere.show = false
        } else {
          viewer.scene.skyBox = defaultSkybox
          viewer.scene.skyAtmosphere.show = true
        }
      })
    },
    //加载大唐不夜城附近基站
    loadbycBeas () {
      let position = Cesium.Cartesian3.fromDegrees(108.958393, 34.216579, 12)
      let heading = Cesium.Math.toRadians(35)
      let pitch = 0
      let roll = 0
      let hpRoll = new Cesium.HeadingPitchRoll(heading, pitch, roll)
      let fixedFrameTransforms =
        Cesium.Transforms.localFrameToFixedFrameGenerator('north', 'west')
      // Cesium.Transforms.localFrameToFixedFrameGenerator("south", "east");
      jizhan = viewer.scene.primitives.add(
        Cesium.Model.fromGltf({
          url: window.models.computerRoomModel,
          modelMatrix: Cesium.Transforms.headingPitchRollToFixedFrame(
            position,
            hpRoll,
            Cesium.Ellipsoid.WGS84,
            fixedFrameTransforms
          ),
          scale: 1,
          minimumPixelSize: 40,
          maximumPixelSize: 40,
          lightColor: new Cesium.Cartesian3(100, 100, 100),
        })
      )
      jifang = viewer.scene.primitives.add(
        Cesium.Model.fromGltf({
          url: window.models.beautifySiteModel,
          modelMatrix: Cesium.Transforms.headingPitchRollToFixedFrame(
            position,
            hpRoll,
            Cesium.Ellipsoid.WGS84,
            fixedFrameTransforms
          ),
          scale: 1,
          minimumPixelSize: 40,
          maximumPixelSize: 40,
          lightColor: new Cesium.Cartesian3(100, 100, 100),
        })
      )
    },
    //点击事件
    clickFeatures (pmaras) {
      console.log(pmaras);
      let that = this
      if (pmaras) {
        this.loadingModel = false
        this.loadText = true
        this.$message.success('数据加载完成！')
      }
      new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas).setInputAction(
        (e) => {
          for(let i in that.cards){
            that.cards[i] = false
          }
          let pick = viewer.scene.pick(e.position)
          let cartesian = viewer.scene.pickPosition(e.position)
          let lng, lat, height
          if (cartesian) {
            let cartographic = Cesium.Cartographic.fromCartesian(cartesian)
            lng = Cesium.Math.toDegrees(cartographic.longitude)
            lat = Cesium.Math.toDegrees(cartographic.latitude)
            height = cartographic.height
          }
          // alert('经度' + lng + '纬度' + lat + '高度' + height)
          if (this.modelTiaozheng) {
            this.loadingModel = true
            this.keyboard_car({
              lng: lng,
              lat: lat,
              height: this.form.model == "车" ? height : 2,
              xiaoqufugai: pmaras.coverage,
              limiandian: pmaras.facade_pnts,
              rsrp: pmaras.rsrp,
              antenna: pmaras.antenna,
            })
            this.$message({
              showClose: true,
              duration: 0,
              message: '请用键盘方向键或WASD键控制汽车移动，空格键可切换视角'
            });
          }
          //判断点击模型
          if (pickObject.length > 0) {
            pickObject.forEach((item) => {
              item.primitive.color = Cesium.Color.fromCssColorString('#fff')
              item.primitive.lightColor = new Cesium.Cartesian3(20, 20, 20)
            })
            pickObject = []
          }
          if (mimos.length > 0) {
            mimos.forEach((item) => {
              viewer.scene.primitives.remove(item)
            })
            mimos = []
          }
       
          let heading = Cesium.Math.toDegrees(viewer.camera.heading).toFixed(2);   // 方位角
          let pitch = Cesium.Math.toDegrees(viewer.camera.pitch).toFixed(2);  // 俯仰角
          let roll = Cesium.Math.toDegrees(viewer.camera.roll).toFixed(2);  // 翻滚角
          var position = viewer.scene.camera.positionCartographic
          var longitude = Cesium.Math.toDegrees(position.longitude).toFixed(6)
          var latitude = Cesium.Math.toDegrees(position.latitude).toFixed(6)
          var height2 = position.height
          console.log(heading, pitch, roll, longitude, latitude, height2);
          if (pick && pick.primitive instanceof Cesium.Model && pick.id != '机楼') {
            if (pick.id) {
              that.tableData = []
              pickObject.push(pick)
              pick.primitive.color =
                Cesium.Color.fromCssColorString('#FFA300').withAlpha(0.8)
              pick.primitive.lightColor = new Cesium.Cartesian3(200, 200, 200)
              bycTransmitterArray.forEach((item) => {
                if (pick.id === item.properties.Transmitte) {
                  that.tableData.push(
                    {
                      key: '天线ID',
                      value: item.properties.Transmitte,
                    },
                    {
                      key: '基站ID',
                      value: item.properties.Site,
                    },
                    {
                      key: '安装方式',
                      value: item.properties.安装方,
                    },
                    {
                      key: '方位角',
                      value: item.properties.Azimuth,
                    },
                    {
                      key: '备注',
                      value: item.properties.Comments,
                    },
                    {
                      key: '挂高',
                      value: item.properties.Height,
                    },
                    {
                      key: '机械倾角',
                      value: item.properties.Mechanical,
                    },
                    {
                      key: '频段',
                      value: item.properties.Antenna,
                    }
                  )
                  that.loadMIMO(pick.id, 20)
                }
              })

              this.home = false
              this.cards.tianxian = true
            }
          } else {
            pickObject = []
            if (mimos.length > 0) {
              mimos.forEach((item) => {
                viewer.scene.primitives.remove(item)
              })
            }
          }
          //点击基站标签
          if (pick && pick.primitive instanceof Cesium.Billboard && !pick.id._name) {
            that.SiteTableData = []
            let SiteantennaNum = []
            bycTransmitterArray.forEach((item) => {
              if (item.properties.Site == pick.id._id) {
                that.loadMIMO(item.properties.Transmitte, 20, 1)
                SiteantennaNum.push(item.properties.Transmitte)
              }
            })
            bycSite.forEach(item => {
              if (item.properties.Site == pick.id._id) {
                that.SiteTableData.push(
                  {
                    key: '基站ID',
                    value: item.properties.Site
                  },
                  {
                    key: '天线挂高',
                    value: item.properties.Height
                  },
                  {
                    key: '天线数',
                    value: SiteantennaNum.length
                  },
                  {
                    key: '基站类型',
                    value: item.properties.安装方
                  }
                )
              }
            })
              this.home = false
              this.cards.station = true
          }
        },
        Cesium.ScreenSpaceEventType.LEFT_CLICK
      )
    },
    loadIframe () {
      this.PanoramaShow = false
      this.$nextTick(function () {
        this.PanoramaShow = true
      })
    },
    //判断是否全屏
    fullScreen () {
      let icons = Object.values(this.icon);
      for (let i = 0; i < icons.length; i++) {
        if (icons[i] == true) {
          this.largeScreenShow = false
          break
        } else {
          this.largeScreenShow = true
        }
      }
    },
    //加载不夜城网元
    loadbycwy () {
      if (!this.icon.icon7) {
        this.icon.icon7 = !this.icon.icon7
        this.loadbycsite()
        this.loadbycjizhanazj()
        this.loadbyctianxian()
        flyTos({
          lon: 108.958750,
          lat: 34.199638,
          height: 569.5329501921358,
          viewer: viewer,
          heading: 0,
          pitch: -25.29,
          roll: 0,
        })
        this.fullScreen()  //判断是否全屏
      } else {
        this.icon.icon7 = !this.icon.icon7
        this.removeBycstart()
        flyTos({
          lon: 108.959,
          lat: 34.2054,
          height: 10,
          viewer: viewer,
          heading: 0,
          pitch: -8,
          roll: 0,
        })
        this.fullScreen()  //判断是否全屏
      }
    },
    //加载大唐不夜城基站
    async loadbycsite () {
      await this.$http.get(window.Geojson.bycSiteJson)
        .then((res) => {
          if (res) {
            bycSite = res.data.features
            console.log(res.data.features);
            this.loadbycjizhan(res.data.features)
          }
        })
    },
    loadbycjizhan (pmaras) {
      pmaras.forEach((item) => {
        if (item.properties.安装方 == '单管塔') {
          this.loadWebsite({
            lon: item.geometry.coordinates[0],
            lat: item.geometry.coordinates[1],
            height: 0,
            url: window.models.beautifySiteModel,
            scale: 1,
            lightColor: new Cesium.Cartesian3(40, 40, 40),
          })
          this.addMultipleBillboards({
            id: item.properties.Site,
            lon: item.geometry.coordinates[0],
            lat: item.geometry.coordinates[1],
            height: 28,
            image: base,
            width: 32,
            height2: 32,
          })
          // if (item.properties.Site == 767107) {
          //   this.loadTours({
          //     name: item.properties.Site,
          //     lon: item.geometry.coordinates[0],
          //     lat: item.geometry.coordinates[1],
          //     height: 28 + 30,
          //     image: tourImg,
          //     width: 32,
          //     height2: 32,
          //   })
          // }
        } else {
          console.log(item)
        }
      })
    },
    //加载大唐不夜城那成天线安装架
    async loadbycjizhanazj () {
      await this.$http
        .get(
          window.Geojson.byctransmitterzjJson
        )
        .then((res) => {
          if (res) {
            this.loadbyctransmitter_zj(res.data.features)
          }
        })
    },
    loadbyctransmitter_zj (pmaras) {
      pmaras.forEach((item) => {
        if (item.properties.安装方 == '单管塔') {
          this.xiaoqubaogan_loding({
            lon: item.geometry.coordinates[0],
            lat: item.geometry.coordinates[1],
            height: 20,
            Azimuth: (item.properties.Azimuth - 90) * -1,
            url: window.models.txzjModelUrl,
            id: item.properties,
            scale: 2,
          })
        }
      })
    },
    //加载不夜城基站天线
    loadbyctianxian () {
      this.$http
        .get(
          window.Geojson.bycransmitterJson
        )
        .then((res) => {
          if (res) {
            bycTransmitterArray = res.data.features
            this.loadbyctransmitter(res.data.features)
          }
        })
    },
    loadbyctransmitter (pmaras) {
      pmaras.forEach((item) => {
        if (item.properties.安装方 == '单管塔') {
          this.loadXiaoqu({
            id: item.properties.Transmitte,
            lon: item.geometry.coordinates[0],
            lat: item.geometry.coordinates[1],
            height: 20 + 0.68 * 2,
            Azimuth: (item.properties.Azimuth - 90) * -1,
            url: window.models.transmitterModelUrl,
            scale: 2,
          })
        }
      })
    },
    //双击大唐不夜城边界
    doubleClickFeatures () {
      let that = this
      new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas).setInputAction(
        (e) => {
          let pick = viewer.scene.pick(e.position)
          if (pick && pick.id instanceof Cesium.Entity) {
            if (pick.id._name == '立体墙') {
              wall = pick
              that.btnsShow = !that.btnsShow
              that.chartsShow = !that.chartsShow
              that.icon.icon5 = true
              pick.id._name = 'SSS'
              that.loadwangyuan() //判断后移除网元
              that.bycModelShow() //加载不夜城模型
              //深度检测
              viewer.scene.globe.depthTestAgainstTerrain = false
              setTimeout(() => {
                flyTos({
                  lon: 108.959,
                  lat: 34.2054,
                  height: 10,
                  viewer: viewer,
                  heading: 0,
                  pitch: -8,
                  roll: 0,
                })
              }, 500)
              that.modelName = '西安白膜'
              that.removezhengzhouZGD()
            }
          }
        },
        Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK
      )
    },
    //返回
    returnFn () {
      wall.id._name = '立体墙'
      this.btnsShow = !this.btnsShow
      this.icon.icon5 = false
      this.chartsShow = !this.chartsShow
      this.bycModelShow() //加载不夜城模型
      //深度检测
      viewer.scene.globe.depthTestAgainstTerrain = true
      this.modelName = '大唐不夜城'
      this.removeBycstart()
    },
    //移除基站
    removeBycstart () {
      //移除基站
      if (WebsiteArray.length > 0) {
        WebsiteArray.forEach(item => {
          viewer.scene.primitives.remove(item)
        })
      }
      //移除billboard
      if (billboardArray.length > 0) {
        billboardArray.forEach(item => {
          viewer.entities.remove(item)
        })
        PanoramaArray.forEach((item) => {
          viewer.entities.remove(item)
        })
      }
      //移除天线支架
      if (txzjArray.length > 0) {
        txzjArray.forEach(item => {
          viewer.scene.primitives.remove(item)
        })
      }
      //移除天线
      if (antennaAry.length > 0) {
        antennaAry.forEach(item => {
          viewer.scene.primitives.remove(item)
        })
      }
    },
    //扩散圆
    loadEllipse (ellipsesCanshu) {
      let ellipses = viewer.entities.add({
        position: Cesium.Cartesian3.fromDegrees(
          ellipsesCanshu.position.x,
          ellipsesCanshu.position.y,
          ellipsesCanshu.position.z
        ),
        name: '波纹圆',
        ellipse: {
          semiMinorAxis: ellipsesCanshu.semiMinorAxis,
          semiMajorAxis: ellipsesCanshu.semiMajorAxis,
          material: new Cesium.CircleSpiralMaterialProperty({
            color: new Cesium.Color(1, 109 / 255, 71 / 255, 0.7),
            speed: 60.0,
          }),
        },
      })
      viewer.cesiumWidget.screenSpaceEventHandler.removeInputAction(
        Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK
      )
      viewer.cesiumWidget.screenSpaceEventHandler.removeInputAction(
        Cesium.ScreenSpaceEventType.LEFT_CLICK
      )
    },
    //挂高热力图
    guagaoHeatMap (val) {
      if (val == "1") {
        viewer.entities.remove(heatMapjieshou)
        let points = [];
        tianxianshuju.forEach(item => {
          if (item.properties.Height < 25) {
            points.push({
              x: Math.floor((item.geometry.coordinates[0] - lonMin) / (lonMax - lonMin) * 10000),
              y: Math.floor(Math.abs(item.geometry.coordinates[1] - latMax) / (latMax - latMin) * 10000),
              value: Math.floor(item.properties.Height),
            })
          }
        })
        let heatMapInstance = h337.create({
          container: document.querySelector('#heatMap')
        })
        console.log(points);
        let data = {
          max: 25,
          data: points
        }
        heatMapInstance.setData(data)
        this.stickHot()
      } else if (val == "2") {
        let points = [];
        viewer.entities.remove(heatMapjieshou)
        tianxianshuju.forEach(item => {
          if (item.properties.Height >= 25 && item.properties.Height < 40) {
            points.push({
              x: Math.floor((item.geometry.coordinates[0] - lonMin) / (lonMax - lonMin) * 10000),
              y: Math.floor(Math.abs(item.geometry.coordinates[1] - latMax) / (latMax - latMin) * 10000),
              value: Math.floor(item.properties.Height),
            })
          }
        })
        let heatMapInstance = h337.create({
          container: document.querySelector('#heatMap')
        })
        console.log(points);
        let data = {
          max: 25,
          data: points
        }
        heatMapInstance.setData(data)
        this.stickHot()
      } else if (val == "3") {
        let points = [];
        viewer.entities.remove(heatMapjieshou)
        tianxianshuju.forEach(item => {
          if (item.properties.Height >= 40 && item.properties.Height < 80) {
            points.push({
              x: Math.floor((item.geometry.coordinates[0] - lonMin) / (lonMax - lonMin) * 10000),
              y: Math.floor(Math.abs(item.geometry.coordinates[1] - latMax) / (latMax - latMin) * 10000),
              value: Math.floor(item.properties.Height),
            })
          }
        })
        console.log(points);
        let heatMapInstance = h337.create({
          container: document.querySelector('#heatMap')
        })
        let data = {
          max: 25,
          data: points
        }
        heatMapInstance.setData(data)
        this.stickHot()
      } else {
        let points = [];
        viewer.entities.remove(heatMapjieshou)
        tianxianshuju.forEach(item => {
          if (item.properties.Height >= 80) {
            points.push({
              x: Math.floor((item.geometry.coordinates[0] - lonMin) / (lonMax - lonMin) * 10000),
              y: Math.floor(Math.abs(item.geometry.coordinates[1] - latMax) / (latMax - latMin) * 10000),
              value: Math.floor(item.properties.Height),
            })
          }
        })
        console.log(points);
        let heatMapInstance = h337.create({
          container: document.querySelector('#heatMap')
        })
        let data = {
          max: 25,
          data: points
        }
        heatMapInstance.setData(data)
        this.stickHot()
      }
    },
    //实时图表
    addCharts () {
      let dom = document.getElementById('RSRPCharts')
      myChart = echarts.init(dom)
      const option = {
        color: ['#FFFFFF'],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
            label: {
              backgroundColor: '#6a7985'
            }
          },
          formatter: function (params) {
            return '<div><span style="color: #43E8FF;font-size:18px">RSRP:</span><span style="color: #00B83F;">' + params[0].value + 'dBm</span>' + '</div>';
          }
        },
        xAxis: {
          splitLine: {
            show: true,
            lineStyle: {
              color: 'rgba(0,0,0,0)', //更改坐标轴颜色
            },
          },
          lineStyle: {
            color: 'rgba(0,0,0,0)', //更改坐标轴颜色
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#FFFFFF', //更改坐标轴文字颜色
              fontSize: 14, //更改坐标轴文字大小
            },
          },
          data: []
        },
        yAxis: {
          name: 'dBm',
          nameTextStyle: {
            color: '#96d1ff',
            align: 'right',
            fontSize: '16px',
          },
          type: 'value',
          inverse: false,  //Y轴排序
          boundaryGap: [0, '100%'],  //Y轴轴线间隔
          axisLabel: {
            show: true,
            textStyle: {
              color: 'aliceblue'
            },
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "#fff",
              width: 0,
              type: "solid"
            }
          },
        },
        grid: [  //设置图表上下左右边距
          {
            left: '10%',
            right: '10%',
            top: 50,
            bottom: 35
          },
        ],
        series: [
          {
            type: 'line',
            symbol: 'none',
            sampling: 'lttb',
            smooth: false,
            itemStyle: {
              color: '#4adfff'
            },
            // areaStyle: {
            //   color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            //     {
            //       offset: 0,
            //       color: '#4adfff'
            //     },
            //     {
            //       offset: 1,
            //       color: 'rgba(74,223,255,0.3)'
            //     }
            //   ]),
            // },
            data: []
          }
        ]
      }
      option && myChart.setOption(option);
    },
    //实时图表
    SINRCharts () {
      let dom = document.getElementById('SINRCharts')
      mysinr = echarts.init(dom)
      const option = {
        color: ['#FFFFFF'],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
            label: {
              backgroundColor: '#6a7985'
            }
          },
          formatter: function (params) {
            return '<div><span style="color: #43E8FF;font-size:18px">SINR:</span><span style="color: #00B83F;">' + params[0].value + 'dB</span>' + '</div>';
          }
        },
        xAxis: {
          splitLine: {
            show: true,
            lineStyle: {
              color: 'rgba(0,0,0,0)', //更改坐标轴颜色
            },
          },
          lineStyle: {
            color: 'rgba(0,0,0,0)', //更改坐标轴颜色
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#FFFFFF', //更改坐标轴文字颜色
              fontSize: 14, //更改坐标轴文字大小
            },
          },
          data: []
        },
        yAxis: {
          name: 'dB',
          nameTextStyle: {
            color: '#96d1ff',
            align: 'right',
            fontSize: '16px',
          },
          type: 'value',
          inverse: false,
          boundaryGap: [0, '100%'],
          axisLabel: {
            show: true,
            textStyle: {
              color: 'aliceblue'
            },
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "#fff",
              width: 0,
              type: "solid"
            }
          },
        },
        grid: [
          {
            left: '10%',
            right: '10%',
            top: 50,
            bottom: 35
          },
        ],
        series: [
          {
            type: 'line',
            symbol: 'none',
            sampling: 'lttb',
            itemStyle: {
              color: '#4adfff'
            },
            smooth: true,
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: '#4adfff'
                },
                {
                  offset: 1,
                  color: 'rgba(74,223,255,0.3)'
                }
              ])
            },
            data: []
          }
        ]
      }
      option && mysinr.setOption(option);
    },
    //上下行速率
    klulkbps () {
      let dom = document.getElementById('upDownKbps')
      upDownsl = echarts.init(dom)
      let option = {
        color: ['#FF0087', '#FFBF00'],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
            label: {
              backgroundColor: '#6a7985'
            }
          },
          formatter: function (params) {
            return '<div><span style="color: #43E8FF;font-size:18px">上行速率：</span><span style="color: #00B83F;">' + params[1].value + 'kbps</span><br/><span style="color: #43E8FF;font-size:18px">下行速率：</span><span style="color: #FF0303;">' + params[0].value + 'kbps</span></div>';
          },
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            axisLine: {
              show: true,
              lineStyle: {
                color: "#fff",
                width: 0,
                type: "solid"
              }
            },
            data: []
          }
        ],
        yAxis: [
          {
            name: 'kbps',
            nameTextStyle: {
              color: '#96d1ff',
              align: 'right',
              fontSize: '16px',
            },
            axisLabel: {//y轴文字的配置
              textStyle: {
                color: "#fff",
                margin: 15
              },
              // formatter: '{value}MB'
            },
            axisLine: {//y轴线的颜色以及宽度
              show: false,
              lineStyle: {
                color: "#fff",
                width: 1,
                type: "solid"
              },
            },
            type: 'value'
          }
        ],
        series: [
          {
            type: 'line',
            stack: 'Total',
            smooth: true,
            lineStyle: {
              width: 0
            },
            showSymbol: false,
            areaStyle: {
              opacity: 0.8,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: 'rgb(128, 255, 165)'
                },
                {
                  offset: 1,
                  color: 'rgb(1, 191, 236)'
                }
              ])
            },
            emphasis: {
              focus: 'series'
            },
            data: []
          },
          {
            type: 'line',
            stack: 'Total',
            smooth: true,
            lineStyle: {
              width: 0
            },
            showSymbol: false,
            areaStyle: {
              opacity: 0.8,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: 'rgb(0, 221, 255)'
                },
                {
                  offset: 1,
                  color: 'rgb(77, 119, 255)'
                }
              ])
            },
            emphasis: {
              focus: 'series'
            },
            data: []
          },
        ]
      };
      option && upDownsl.setOption(option);
    },
    //分析结果
    fcjg_rsrp () {
      let dom = document.getElementById('fxjg_rsrpCharts')
      myfxjgRsrp = echarts.init(dom)
      let option = {
        legend: {
          selectedMode: true, // 取消图例上的点击事件
          type: 'plain',
          orient: 'vertical',
          left: '63%',
          top: 'center',
          align: 'left',
          itemGap: 10,
          itemWidth: 15, // 设置宽度
          itemHeight: 10, // 设置高度
          symbolKeepAspect: false,
          textStyle: {
            color: 'aliceBlue',
            rich: {
              name: {
                verticalAlign: 'center',
                align: 'left',
                width: 90,
                fontSize: 14,
                color: '#D8DDE3',
              },
              value: {
                align: 'left',
                width: 50,
                fontSize: 14,
                padding: [5, 5, 5, -0],
                color: '#D8DDE3',
              }
            }
          },
        },
        tooltip: {
          trigger: 'item'
        },
        title: [
          { // 第一个圆环标题
            text: '', // 主标题
            textStyle: { // 主标题样式
              color: '#333',
              fontWeight: 'bold',
              fontSize: 14
            },
            left: '29%', // 定位到适合的位置
            top: '39%', // 定位到适合的位置
            subtext: '', // 副标题
            subtextStyle: { // 副标题样式
              color: '#fff',
              fontSize: 13,
              fontWeight: 'bold'
            },
            textAlign: 'center' // 主、副标题水平居中显示
          }
        ],
        color: ['#0000FF', '#00FF4B', '#A9F207', '#EAF207', '#FFAB00', '#FF0000'],
        series: [
          {
            type: 'pie',
            radius: ['40%', '80%'],
            center: ['30%', '50%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 4,
              borderColor: 'rgba(255,255,255,0)',
              borderWidth: 2,
              opacity: 1,
            },
            label: {
              //echarts饼图内部显示百分比设置
              show: true,
              position: "inside", //outside 外部显示  inside 内部显示
              formatter: `{d}%`,
              color: "#ffffff", //颜色
              fontSize: 12 //字体大小
            },
            emphasis: {
              label: {
                show: true
              }
            },
            labelLine: {
              show: false
            },
            data: []
          }
        ]
      };
      option && myfxjgRsrp.setOption(option);
    },
    //分析结果
    fcjg_sinr () {
      let dom = document.getElementById('fxjg_sinrCharts')
      myfxjgSinr = echarts.init(dom)
      let option = {
        legend: {
          selectedMode: true, // 取消图例上的点击事件
          type: 'plain',
          orient: 'vertical',
          left: '63%',
          top: 'center',
          align: 'left',
          itemGap: 10,
          itemWidth: 15, // 设置宽度
          itemHeight: 10, // 设置高度
          symbolKeepAspect: false,
          textStyle: {
            color: 'aliceBlue',
            rich: {
              name: {
                verticalAlign: 'center',
                align: 'left',
                width: 90,
                fontSize: 14,
                color: '#D8DDE3',
              },
              value: {
                align: 'left',
                width: 50,
                fontSize: 14,
                padding: [5, 5, 5, -0],
                color: '#D8DDE3',
              }
            }
          },
        },
        tooltip: {
          trigger: 'item'
        },
        title: [
          { // 第一个圆环标题
            text: '', // 主标题
            textStyle: { // 主标题样式
              color: '#333',
              fontWeight: 'bold',
              fontSize: 14
            },
            left: '29%', // 定位到适合的位置
            top: '39%', // 定位到适合的位置
            subtext: '', // 副标题
            subtextStyle: { // 副标题样式
              color: '#fff',
              fontSize: 13,
              fontWeight: 'bold'
            },
            textAlign: 'center' // 主、副标题水平居中显示
          }
        ],
        color: ['#0000FF', '#00FF4B', '#A9F207', '#EAF207', '#FFAB00', '#FF0000'],
        series: [
          {
            type: 'pie',
            radius: ['40%', '80%'],
            center: ['30%', '50%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 4,
              borderColor: 'rgba(255,255,255,0)',
              borderWidth: 2,
              opacity: 1,
            },
            label: {
              //echarts饼图内部显示百分比设置
              show: true,
              position: "inside", //outside 外部显示  inside 内部显示
              formatter: `{d}%`,
              color: "#ffffff", //颜色
              fontSize: 12 //字体大小
            },
            emphasis: {
              label: {
                show: true
              }
            },
            labelLine: {
              show: false
            },
            data: []
          }
        ]
      };
      option && myfxjgSinr.setOption(option);
    },
    analysis () {
      this.jgfx_DataList = []
      this.shijiaoShow2 = false
      this.fxjgShow = !this.fxjgShow
      this.closeBtns.ion4 = !this.closeBtns.ion4
      let analysis_rsrpList = []
      let analysis_sinrList = []
      let analysis_rsrpNum = {
        "best_cnt": 0,  //最好的
        "better_cnt": 0, //较好的
        "good_cnt": 0,  //好的
        "ordinary_cnt": 0,  //普通的
        "bad_cnt": 0,  //较差的
        "worst_cnt": 0, //极差的
      };
      let analysis_sinrNum = {
        "best_cnt": 0,  //最好的
        "better_cnt": 0, //较好的
        "good_cnt": 0,  //好的
        "ordinary_cnt": 0,  //普通的
        "bad_cnt": 0,  //较差的
        "worst_cnt": 0, //极差的
      }
      this.electricalList.forEach(item => {
        //rsrp
        if (item.rsrp <= -60 && item.rsrp > -70) {
          analysis_rsrpNum.best_cnt++
        } else if (item.rsrp <= -70 && item.rsrp > -80) {
          analysis_rsrpNum.better_cnt++
        } else if (item.rsrp <= -80 && item.rsrp > -90) {
          analysis_rsrpNum.good_cnt++
        } else if (item.rsrp <= -90 && item.rsrp > -100) {
          analysis_rsrpNum.ordinary_cnt++
        } else if (item.rsrp <= -100 && item.rsrp > -110) {
          analysis_rsrpNum.bad_cnt++
        } else if (item.rsrp <= -110 && item.rsrp > -120) {
          analysis_rsrpNum.worst_cnt++
        }
        //sinr
        if (item.sinr <= 25 && item.sinr > 20) {
          analysis_sinrNum.best_cnt++
        } else if (item.sinr <= 20 && item.sinr > 10) {
          analysis_sinrNum.better_cnt++
        } else if (item.sinr <= 10 && item.sinr > 0) {
          analysis_sinrNum.good_cnt++
        } else if (item.sinr <= 0 && item.sinr > -5) {
          analysis_sinrNum.ordinary_cnt++
        } else if (item.sinr <= -5 && item.sinr > -10) {
          analysis_sinrNum.bad_cnt++
        } else if (item.sinr <= -10 && item.sinr > -20) {
          analysis_sinrNum.worst_cnt++
        }
      })
      //rsrp
      Object.keys(analysis_rsrpNum).forEach(item => {
        switch (item) {
          case 'best_cnt':
            analysis_rsrpList.push({
              rsrp: -60,
              cnt: analysis_rsrpNum[item],
              percnt: (analysis_rsrpNum[item] / this.electricalList.length * 100).toFixed(2) + '%',
              value: analysis_rsrpNum[item],
              name: '-60dBm(极好)',
              itemStyle:{
                color:'#3892FF'
              }
            })
            break;
          case 'better_cnt':
            analysis_rsrpList.push({
              rsrp: -70,
              cnt: analysis_rsrpNum[item],
              percnt: (analysis_rsrpNum[item] / this.electricalList.length * 100).toFixed(2) + '%',
              value: analysis_rsrpNum[item],
              name: '-70dBm(较好)',
              itemStyle:{
                color:'#00F6C5'
              }
            })
            break;
          case 'good_cnt':
            analysis_rsrpList.push({
              rsrp: -80,
              cnt: analysis_rsrpNum[item],
              percnt: (analysis_rsrpNum[item] / this.electricalList.length * 100).toFixed(2) + '%',
              value: analysis_rsrpNum[item],
              name: '-80dBm(好)',
              itemStyle:{
                color:'#00DAFF'
              }
            })
            break;
          case 'ordinary_cnt':
            analysis_rsrpList.push({
              rsrp: -90,
              cnt: analysis_rsrpNum[item],
              percnt: (analysis_rsrpNum[item] / this.electricalList.length * 100).toFixed(2) + '%',
              value: analysis_rsrpNum[item],
              name: '-90dBm(普通)',
              itemStyle:{
                color:'#FFB200'
              }
            })
            break;
          case 'bad_cnt':
            analysis_rsrpList.push({
              rsrp: -100,
              cnt: analysis_rsrpNum[item],
              percnt: (analysis_rsrpNum[item] / this.electricalList.length * 100).toFixed(2) + '%',
              value: analysis_rsrpNum[item],
              name: '-100dBm(较差)',
              itemStyle:{
                color:'#D156E7'
              }
            })
            break;
          case 'worst_cnt':
            analysis_rsrpList.push({
              rsrp: -110,
              cnt: analysis_rsrpNum[item],
              percnt: (analysis_rsrpNum[item] / this.electricalList.length * 100).toFixed(2) + '%',
              value: analysis_rsrpNum[item],
              name: '-110dBm(极差)',
              itemStyle:{
                color:'#E75656'
              }
            })
            break;
          default:
            break;
        }
      })
      //sinr
      Object.keys(analysis_sinrNum).forEach(item => {
        switch (item) {
          case 'best_cnt':
            analysis_sinrList.push({
              sinr: 25,
              cnt: analysis_sinrNum[item],
              percnt: (analysis_sinrNum[item] / this.electricalList.length * 100).toFixed(2) + '%',
              value: analysis_sinrNum[item],
              name: '25dB(极好)',
              itemStyle:{
                color:'#3892FF'
              }
            })
            break;
          case 'better_cnt':
            analysis_sinrList.push({
              sinr: 20,
              cnt: analysis_sinrNum[item],
              percnt: (analysis_sinrNum[item] / this.electricalList.length * 100).toFixed(2) + '%',
              value: analysis_sinrNum[item],
              name: '20dB(较好)',
              itemStyle:{
                color:'#00F6C5'
              }
            })
            break;
          case 'good_cnt':
            analysis_sinrList.push({
              sinr: 10,
              cnt: analysis_sinrNum[item],
              percnt: (analysis_sinrNum[item] / this.electricalList.length * 100).toFixed(2) + '%',
              value: analysis_sinrNum[item],
              name: '10dB(好)',
              itemStyle:{
                color:'#00DAFF'
              }
            })
            break;
          case 'ordinary_cnt':
            analysis_sinrList.push({
              sinr: 0,
              cnt: analysis_sinrNum[item],
              percnt: (analysis_sinrNum[item] / this.electricalList.length * 100).toFixed(2) + '%',
              value: analysis_sinrNum[item],
              name: '0dB(普通)',
              itemStyle:{
                color:'#FFB200'
              }
            })
            break;
          case 'bad_cnt':
            analysis_sinrList.push({
              sinr: -5,
              cnt: analysis_sinrNum[item],
              percnt: (analysis_sinrNum[item] / this.electricalList.length * 100).toFixed(2) + '%',
              value: analysis_sinrNum[item],
              name: '-5dB(较差)',
              itemStyle:{
                color:'#D156E7'
              }
            })
            break;
          case 'worst_cnt':
            analysis_sinrList.push({
              sinr: -10,
              cnt: analysis_sinrNum[item],
              percnt: (analysis_sinrNum[item] / this.electricalList.length * 100).toFixed(2) + '%',
              value: analysis_sinrNum[item],
              name: '-10dB(极差)',
              itemStyle:{
                color:'#E75656'
              }
            })
            break;
          default:
            break;
        }
      })
      this.legendOfRSRP = analysis_rsrpList
      this.legendOfSINR = analysis_sinrList
      
      const rsrpCopy = JSON.parse(JSON.stringify(analysis_rsrpList))
      rsrpCopy.map(item => { item.itemStyle.color += '50'})
      const sinrCopy = JSON.parse(JSON.stringify(analysis_sinrList))
      sinrCopy.map(item => { item.itemStyle.color += '50'})
      this.$nextTick(() => {
        doms['resultChart'].setOption({
          series: [{ data: analysis_rsrpList },{data:rsrpCopy}]
        })
        doms['resultChart2'].setOption({
          series: [{ data: analysis_sinrList },{data:sinrCopy}]
        })
        doms['resultChart'].resize()
        doms['resultChart2'].resize()
      })
      // myfxjgRsrp.setOption({
      //   title: [{ subtext: this.electricalList.length }],
      //   series: [{ data: analysis_rsrpList }]
      // })
      // myfxjgSinr.setOption({
      //   title: [{ subtext: this.electricalList.length }],
      //   series: [{ data: analysis_sinrList }]
      // })
      const list = []
      this.jgfx_DataList.push({
        zhibiao_sum: this.electricalList.length,
        rsrp_worst_cnt: analysis_rsrpList[0].cnt + '(' + analysis_rsrpList[0].percnt + ')',
        rsrp_worst_cnt1: analysis_rsrpList[0].cnt,
        rsrp_bad_cnt: analysis_rsrpList[1].cnt + '(' + analysis_rsrpList[1].percnt + ')',
        rsrp_bad_cnt1: analysis_rsrpList[1].cnt,
        rsrp_ordinary_cnt: analysis_rsrpList[2].cnt + '(' + analysis_rsrpList[2].percnt + ')',
        rsrp_ordinary_cnt1: analysis_rsrpList[2].cnt,
        rsrp_good_cnt: analysis_rsrpList[3].cnt + '(' + analysis_rsrpList[3].percnt + ')',
        rsrp_good_cnt1: analysis_rsrpList[3].cnt,
        rsrp_better_cnt: analysis_rsrpList[4].cnt + '(' + analysis_rsrpList[4].percnt + ')',
        rsrp_better_cnt1: analysis_rsrpList[4].cnt,
        rsrp_best_cnt: analysis_rsrpList[5].cnt + '(' + analysis_rsrpList[5].percnt + ')',
        rsrp_best_cnt1: analysis_rsrpList[5].cnt,
        SINR_worst_cnt: analysis_sinrList[0].cnt + '(' + analysis_sinrList[0].percnt + ')',
        SINR_worst_cnt1: analysis_sinrList[0].cnt,
        SINR_bad_cnt: analysis_sinrList[1].cnt + '(' + analysis_sinrList[1].percnt + ')',
        SINR_bad_cnt1: analysis_sinrList[1].cnt,
        SINR_ordinary_cnt: analysis_sinrList[2].cnt + '(' + analysis_sinrList[2].percnt + ')',
        SINR_ordinary_cnt1: analysis_sinrList[2].cnt,
        SINR_good_cnt: analysis_sinrList[3].cnt + '(' + analysis_sinrList[3].percnt + ')',
        SINR_good_cnt1: analysis_sinrList[3].cnt,
        SINR_better_cnt: analysis_sinrList[4].cnt + '(' + analysis_sinrList[4].percnt + ')',
        SINR_better_cnt1: analysis_sinrList[4].cnt,
        SINR_best_cnt: analysis_sinrList[5].cnt + '(' + analysis_sinrList[5].percnt + ')',
        SINR_best_cnt1: analysis_sinrList[5].cnt,
      })
      // let obj = {}
      // for(let t in list[0]){
      //   const item = list[0][t]
      //   if (item !== 0 && typeof item == 'number' ) {
      //     obj[t] = item
      //     if (t != 'zhibiao_sum') {
      //       obj[t.slice(0,t.length-1)] = list[0][t.slice(0,t.length-1)]
      //     }
      //   }
      // }
      // this.jgfx_DataList.push(obj)
      // console.log(this.jgfx_DataList);
    },
    stickHot () {
      /* 把热⼒图铺到地球上 */
      // 设置画布为⽣成的热⼒图
      canvas2 = document.getElementsByClassName('heatmap-canvas')
      // 控制台输出画布数据
      console.log(canvas2)
      // 添加热⼒图实例
      let heatMap = viewer.entities.add({
        name: 'heatmap',
        // 设置矩形
        rectangle: {
          // 指定矩形区域
          coordinates: Cesium.Rectangle.fromDegrees(lonMin, latMin, lonMax, latMax),
          // 设置矩形图⽚为据透明度的热⼒图
          material: new Cesium.ImageMaterialProperty({
            image: canvas2[0],
            transparent: true
          })
        }
      })
      heatMapjieshou = heatMap
    },
    //轨迹颜色
    rountColor (num) {
      if (num <= -60 && num > -70) {
        this.RSRPText = '极好'
        return '#0000FF'
      } else if (num <= -70 && num > -80) {
        this.RSRPText = '较好'
        return '#00FF4B'
      } else if (num <= -80 && num > -90) {
        this.RSRPText = '好'
        return '#A9F207'
      } else if (num <= -90 && num > -100) {
        this.RSRPText = '普通'
        return '#EAF207'
      } else if (num <= -100 && num > -110) {
        this.RSRPText = '较差'
        return '#FFAB00'
      } else if (num <= -110 && num > -120) {
        this.RSRPText = '极差'
        return '#FF0000'
      } else {
        this.RSRPText = '普通'
        return '#EAF207'
      }
    },
    //连线shader
    materialLine () {
      if (typeof Cesium !== 'undefined')
        (function (_0x28e6fb) {
          var _0x3a4a3c = {
            iKSVw: '1|4|0|5|3|2',
            loCcz: function (_0x8baaf6, _0x53c135) {
              return _0x8baaf6(_0x53c135)
            },
            ZVLvT: function (_0x5a6faa, _0x35dbb2) {
              return _0x5a6faa / _0x35dbb2
            },
            OOtAe: function (_0x5ac73d, _0x1d0c26) {
              return _0x5ac73d * _0x1d0c26
            },
            YUfBq: function (_0x150977, _0x34f301) {
              return _0x150977 - _0x34f301
            },
            AAzBp: function (_0x4456b6, _0x489fd6) {
              return _0x4456b6 === _0x489fd6
            },
            zWdDc: '0|1|5|4|6|2|3',
            phCaN: function (_0x22f7fe, _0x22460b, _0x584f20) {
              return _0x22f7fe(_0x22460b, _0x584f20)
            },
            xUWjD: function (_0x2d653f, _0x3fc619) {
              return _0x2d653f === _0x3fc619
            },
            iTcol: function (_0x4dea27, _0x5c701b) {
              return _0x4dea27 instanceof _0x5c701b
            },
            YLCPu: function (_0xbac2e7, _0x417f56) {
              return _0xbac2e7 + _0x417f56
            },
            kDWEL: 'wallType',
            jikaj: function (_0x37f580, _0xb90f4c) {
              return _0x37f580 * _0xb90f4c
            },
            uvOSD: 'color',
            foFmO:
              'czm_material\x20czm_getMaterial(czm_materialInput\x20materialInput)\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20czm_material\x20material\x20=\x20czm_getDefaultMaterial(materialInput);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20vec2\x20st\x20=\x20materialInput.st;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20if(texture2D(image,\x20vec2(0.0,\x200.0)).a\x20==\x201.0){\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20discard;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20}else{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20material.alpha\x20=\x20texture2D(image,\x20vec2(1.0\x20-\x20fract(time\x20-\x20st.s),\x20st.t)).a\x20*\x20color.a;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20material.diffuse\x20=\x20max(color.rgb\x20*\x20material.alpha\x20*\x203.0,\x20color.rgb);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20return\x20material;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20',
            aQpuV:
              'background:\x20#606060;\x20color:\x20#fff;\x20border-radius:\x203px\x200\x200\x203px;',
            XOzEg:
              'background:\x20#1475B2;\x20color:\x20#fff;\x20border-radius:\x200\x203px\x203px\x200;',
          }
          function _0x265ba3 (_0x4f3655) {
            var _0x5f1c70 = {
              KioEk: _0x3a4a3c['zWdDc'],
              mPDit: function (_0x1b1c92, _0x330c7a, _0x2b6846) {
                return _0x3a4a3c['phCaN'](_0x1b1c92, _0x330c7a, _0x2b6846)
              },
              zvyOY: function (_0x337a2c, _0x58ba2d) {
                return _0x3a4a3c['xUWjD'](_0x337a2c, _0x58ba2d)
              },
              UBCpg: function (_0x51b947, _0x1cee24) {
                return _0x3a4a3c['iTcol'](_0x51b947, _0x1cee24)
              },
            }
            var _0x578460 = _0x28e6fb['Color'],
              _0x18e133 = _0x28e6fb['defaultValue'],
              _0x4e36fd = _0x28e6fb['defined'],
              _0x285486 = Object['defineProperties'],
              _0xd32598 = _0x28e6fb['Event'],
              _0x35590 = _0x28e6fb['createPropertyDescriptor'],
              _0x1479bd = _0x28e6fb['Property'],
              _0x5236ab = _0x28e6fb['Material'],
              _0x26ead8 = _0x578460['WHITE'],
              _0xc22a2e =
                _0x4f3655['MaterialType'] ||
                _0x3a4a3c['YLCPu'](
                  _0x3a4a3c['kDWEL'],
                  _0x3a4a3c['loCcz'](
                    parseInt,
                    _0x3a4a3c['jikaj'](Math['random'](), 0x3e8)
                  )
                )

            function _0x467c57 (_0x22dfb5) {
              var _0x101c3d = _0x5f1c70['KioEk']['split']('|'),
                _0x53d876 = 0x0
              while (!![]) {
                switch (_0x101c3d[_0x53d876++]) {
                  case '0':
                    _0x22dfb5 = _0x5f1c70['mPDit'](
                      _0x18e133,
                      _0x22dfb5,
                      _0x18e133['EMPTY_OBJECT']
                    )
                    continue
                  case '1':
                    this['_definitionChanged'] = new _0xd32598()
                    continue
                  case '2':
                    this['duration'] = _0x22dfb5['duration'] || 0x3e8
                    continue
                  case '3':
                    this['_time'] = undefined
                    continue
                  case '4':
                    this['_colorSubscription'] = undefined
                    continue
                  case '5':
                    this['_color'] = undefined
                    continue
                  case '6':
                    this['color'] =
                      _0x22dfb5['color'] || _0x28e6fb['Color']['BLUE']
                    continue
                }
                break
              }
            }
            _0x3a4a3c['phCaN'](_0x285486, _0x467c57['prototype'], {
              isvarant: {
                get: function () {
                  return ![]
                },
              },
              definitionChanged: {
                get: function () {
                  return this['_definitionChanged']
                },
              },
              color: _0x3a4a3c['loCcz'](_0x35590, _0x3a4a3c['uvOSD']),
            })
            _0x467c57['prototype']['getType'] = function (_0x17bed1) {
              return _0xc22a2e
            }
            _0x467c57['prototype']['getValue'] = function (
              _0x52a446,
              _0x2c759a
            ) {
              var _0x57bbe9 = _0x3a4a3c['iKSVw']['split']('|'),
                _0x2a5c25 = 0x0
              while (!![]) {
                switch (_0x57bbe9[_0x2a5c25++]) {
                  case '0':
                    _0x2c759a['image'] = _0x4f3655['image']
                    continue
                  case '1':
                    if (!_0x3a4a3c['loCcz'](_0x4e36fd, _0x2c759a)) {
                      _0x2c759a = {}
                    }
                    continue
                  case '2':
                    return _0x2c759a
                  case '3':
                    _0x2c759a['time'] = _0x3a4a3c['ZVLvT'](
                      _0x3a4a3c['OOtAe'](
                        _0x3a4a3c['YUfBq'](
                          _0x52a446['secondsOfDay'],
                          this['_time']
                        ),
                        0x3e8
                      ),
                      this['duration']
                    )
                    continue
                  case '4':
                    _0x2c759a['color'] = _0x1479bd['getValueOrClonedDefault'](
                      this['_color'],
                      _0x52a446,
                      _0x26ead8,
                      _0x2c759a['color']
                    )
                    continue
                  case '5':
                    if (_0x3a4a3c['AAzBp'](this['_time'], undefined)) {
                      this['_time'] = _0x52a446['secondsOfDay']
                    }
                    continue
                }
                break
              }
            }
            _0x467c57['prototype']['equals'] = function (_0x47fd34) {
              return (
                _0x5f1c70['zvyOY'](this, _0x47fd34) ||
                (_0x5f1c70['UBCpg'](_0x47fd34, _0x467c57) &&
                  _0x1479bd['equals'](this['_color'], _0x47fd34['_color']))
              )
            }
            _0x5236ab['_materialCache']['addMaterial'](_0xc22a2e, {
              fabric: {
                type: _0xc22a2e,
                uniforms: {
                  color:
                    _0x4f3655['color'] ||
                    new _0x28e6fb['Color'](0x1, 0x0, 0x0, 0.5),
                  image: _0x4f3655['image'],
                  time: _0x4f3655['color']['time'] || 0x0,
                },
                source: _0x3a4a3c['foFmO'],
              },
              translucent: function (_0x5d5e53) {
                return !![]
              },
            })
            return new _0x467c57(_0x4f3655)
          }
          _0x28e6fb['CustomMaterialLine'] = _0x265ba3
          let _0x552062 = _0x3a4a3c['aQpuV']
          let _0x5de97c = _0x3a4a3c['XOzEg']
        })(Cesium)
    },
    indexMethod (index) {
      return index * 1;
    },
    exportExcel (num) {
      if (num == 1) {
        /* generate workbook object from table */
        var xlsxParam = { raw: true } // 导出的内容只做解析，不进行格式转换
        var wb = XLSX.utils.table_to_book(document.querySelector('#exportTab'), xlsxParam)
        /* get binary string as output */
        var wbout = XLSX.write(wb, { bookType: 'xlsx', bookSST: true, type: 'array' })
        try {
          FileSaver.saveAs(new Blob([wbout], { type: 'application/octet-stream' }), 'Analysis_results.xlsx')
        } catch (e) {
          if (typeof console !== 'undefined') {
            console.log(e, wbout)
          }
        }
        return wbout
      } else {
        /* generate workbook object from table */
        var xlsxParam = { raw: true } // 导出的内容只做解析，不进行格式转换
        var wb = XLSX.utils.table_to_book(document.querySelector('#jgfx_table'), xlsxParam)
        /* get binary string as output */
        var wbout = XLSX.write(wb, { bookType: 'xlsx', bookSST: true, type: 'array' })
        try {
          FileSaver.saveAs(new Blob([wbout], { type: 'application/octet-stream' }), 'Summary.xlsx')
        } catch (e) {
          if (typeof console !== 'undefined') {
            console.log(e, wbout)
          }
        }
        return wbout
      }
    },
  },
}
</script>

<style lang="less">
@import "~@/assets/css/index.less";

.cesium-widget-credits{ display: none !important;}   /* 去除logo */
#box { width: 100%; height: 100%;}







#search {
  position: absolute !important;
  top: 20px;
  left: 20px;
}

.cxBtn {
  background-color: #409eff !important;
  color: #fff !important;
  border-radius: 0 !important;
  border: 1px solid #409eff !important;
}

.is-horizontal {
  display: none !important;
}

.el-scrollbar__wrap {
  margin-bottom: -17px !important;
}

.buttom_lng {
  position: absolute;
  right: 10px;
  bottom: 0px;
  z-index: 9;
}

.navigation-controls {
  background-color: rgba(68, 67, 67, 0.473);
  bottom: 170px !important;
  right: 35px !important;
}

.compass {
  bottom: 250px !important;
  right: 2px !important;
}

.distance-legend {
  bottom: 7px !important;
  right: 470px !important;
}

.distance-legend-label {
  color: #fff !important;
}

.distance-legend-scale-bar {
  border-left: 1px solid #fff !important;
  border-right: 1px solid #fff !important;
  border-bottom: 1px solid #fff !important;
}

.map-operate {
  position: absolute;
  bottom: 50px;
  right: 5px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix2:before,
.clearfix2:after {
  display: table;
  content: "";
}

.jkxq .has-gutter {
  display: none;
}

.el-table__cell {
  padding: 5px 0 !important;
}

.clears {
  padding: 12px 0;
}

.el-divider {
  margin: 5px 0 !important;
}

#echarstb {
  width: 380px;
  height: 230px;
  text-align: center;
}

.updataBtns {
  padding: 15px 0 0 0;
}

html,
body {
  overflow: hidden;
}

.el-button--primary {
  padding: 13px 20px !important;
  border: none !important;
}

#citySelect {
  position: absolute;
  top: 20px;
  right: 20px;
}

.tksn {
  float: right;
  cursor: pointer;
  width: 32px;
  height: 32px;
}

.setClassName {
  left: unset !important;
  top: 20px !important;
  right: 120px !important;
}

.clearfix > span {
  display: inline-block;
  font-weight: 700;
  font-size: 22px;
}

.clearfix2 > span {
  display: inline-block;
  font-weight: 500;
  font-size: 18px;
}

.fnSwitch {
  position: absolute;
  top: 20px;
  left: 460px;
  height: 40px;
  width: 150px;
  text-align: center;
  background-color: rgba(250, 250, 250, 0.884);
}


.tianqis {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: rgba(255, 255, 255, 0.699);
  border-radius: 5px;
  padding: 5px 10px;
}

.home {
  position: absolute;
  right: 10px;
  top: 118px;
  bottom: 0px;
  width: 30px;
  height: 30px;
  background: url(../assets/home-color.png);
  background-size: 100% 100%;
  border-radius: 7px;
  z-index: 99;
  cursor: pointer;
}

.cesium-performanceDisplay-defaultContainer {
  top: 270px !important;
}

.loadData {
  position: absolute;
  top: 20px;
  left: 20px;
}

.queryServe {
  position: absolute;
  top: 30%;
  left: 30%;
}

.box-card {
  position: absolute;
  top: 105px;
  right: 390px;
  width: 300px;
  background-color: rgba(255, 255, 255, 0) !important;
  border: none !important;
  backdrop-filter: blur(50px);
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.288);
}

.demonstration {
  font-size: 17px;
  color: #fff;
  font-family: "pmzd";
  font-weight: 400;
}

.clearfix {
  font-family: "pmzd";
  font-size: 24px;
  color: #ffffff !important;
}

.clearfix2 {
  font-family: "pmzd";
  font-size: 24px;
  color: #ffffff !important;
}

.load_car {
  position: absolute;
  top: 20px;
  left: 20px;
}

#box1 {
  width: 461px;
  height: 327px;
  position: absolute;
  bottom: 115px;
  left: 730px;
  background-color: #fff;
  border-radius: 30px 30px 10px 10px;
}

#box1 .cesium-viewer {
  border-radius: 30px 30px 10px 10px;
}

.hud {
  position: absolute;
  width: 200px;
  height: 100px;
  left: 856px;
  top: 87px;
  /* background-color: rgba(255, 255, 255, 0.103); */
  border-radius: 5px;
  text-align: center;
}
@font-face {
  font-family: mFont;
  src: url(../assets/DS-DIGIB.ttf);
}

.span2 {
  display: inline-block;
  font-family: pmzd;
  line-height: 100px;
  font-size: 20px;
}

.span1 {
  display: inline-block;
  font-family: pmzd;
  line-height: 5rem;
  font-size: 20px;
}

.daping {
  position: absolute !important;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

/* .has-gutter {
  display: none !important;
} */
.baseXqing {
  background-color: rgba(141, 149, 156, 0.29);
  padding: 5px;
}
.baseXqing > td {
  color: #ffffff;
  font-weight: 400;
  font-size: 0.7vw;
}
.td1 {
  font-weight: 500;
  font-size: 0.8vw;
  color: #70e6ff !important;
  font-family: "pmzd";
}
.td2 {
  font-weight: 400;
  font-size: 0.6vw;
  color: #ffffff !important;
  font-family: "pmzd";
}
.loadingModelPromise {
  position: absolute;
  right: 90px;
  bottom: 20px;
  width: 200px;
  height: 50px;
  background-color: rgba(255, 255, 255, 0) !important;
  font-size: 17px;
  color: #fff;
  font-family: "pmzd";
  font-weight: 400;
}
.return {
  position: absolute;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  top: 120px;
  left: 400px;
  backdrop-filter: blur(50px);
  cursor: pointer;
  text-align: center;
}
.return :active {
  transform: scale(0.9);
}
.el-input__inner {
  width: 130px !important;
}
.el-input {
  top: -40 px;
  left: 100 px;
}

.el-dialog {
  height: 400px;
}

.el-dialog__footer {
  position: relative;
  bottom: 60px;
}
.pouqieshendu {
  position: absolute;
  background-color: #fff;
  top: 30px;
  left: 48%;
  width: 100px;
  height: 50px;
  border-radius: 5px;
  text-align: center;
}
.pouqieshendu span {
  display: inline-block;
  font-family: mFont;
  line-height: 50px;
  font-size: 40px;
  color: #70e6ff;
}
.jizhanHeatMap {
  position: absolute;
  top: 108px;
  right: 397px;
}


.zhedie,
.zhedie2,
.zhedie3 {
  position: absolute;
  width: 30px;
  height: 30px;
  top: 20px;
  left: 280px;
  background-image: url("../assets/open.svg");
  background-position: center center;
  background-size: cover;
  cursor: pointer;
}
.zhedie,
.zhedie2,
.zhedie3 {
  background-image: url("../assets/down.svg");
}

.zhedie2 {
  left: 340px !important;
}
.zhedie3 {
  left: 250px !important;
}
#wycz {
  width: 340px !important;
  height: 225px;
}

#wycz2 {
  width: 410px !important;
}

.cell {
  color: #000;
  font-family: "pmzd";
}

a {
  text-decoration: none;
}
.outGensui {
  margin-left: 60px !important;
}
.leftBack {
  width: 20%;
  height: calc(100vh - 105px);
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: 0;
  border-radius: 0 10px 0 0;
  box-shadow: 0 0 30px rgb(61, 61, 61);
  backdrop-filter: blur(50px);
}
.rightBack {
  width: 20%;
  height: calc(100vh - 105px);
  position: absolute;
  right: 0;
  bottom: 0;
  z-index: 0;
  border-radius: 10px 0 0 0;
  box-shadow: 0 0 30px rgb(61, 61, 61);
  backdrop-filter: blur(50px);
}
.screen {
  position: absolute;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  top: 120px;
  left: 400px;
  /* backdrop-filter: blur(50px); */
  cursor: pointer;
  text-align: center;
}
.screen :active {
  transform: scale(0.9);
}
.screen img {
  width: 100%;
  height: auto;
}
.wycz3 {
  right: 395px !important;
}

.fxjg {
  right: calc(50% - 400px) !important;
  width: 800px !important;
  height: 700px !important;
}
.fxjg_left {
  position: absolute;
  width: 750px;
  height: 40%;
}
.fxjg_right {
  position: absolute;
  width: 750px;
  height: 40%;
  bottom: 20px;
}
.fxjg_close {
  position: absolute;
  width: 30px;
  height: 30px;
  top: 20px;
  right: 20px;
  background-image: url("../assets/close.svg");
  background-position: center center;
  background-size: cover;
  cursor: pointer;
}
.hzjgdc {
  position: absolute;
  right: 30px;
  top: 30px;
}
</style>
