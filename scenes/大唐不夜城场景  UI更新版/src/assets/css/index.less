@import "~@/assets/css/mycss.less";
@import "~@/assets/css/publicCss.less";

@--fontColor:aliceblue;
@--bg-even:#00DAFF10;
@--font-title:{
  font-family: YouSheBiaoTiHei;
  font-size: 1.7rem;
  color: @--fontColor;
}

.top{
  position: absolute; top: 0;
  width: 100vw; height: 4.8rem;
  @--flexRow();
  justify-content: space-between;
  background: url("~@/assets/UIpic/toB-title.png") no-repeat left center;
  background-color: #00121577;
  theme-title{
    overflow: hidden;
    white-space: nowrap;  
    text-overflow:ellipsis;
    height: 100%;
    width: 50%;
    padding: 0 30px;
    display: inline-block;
    font-size: 2.5rem;
    font-family: YouSheBiaoTiHei;
    line-height: 4.7rem;
    color: @--fontColor;
    span{font-size: 2.1rem;}
  }
  .infoT{@--flexRow()}
  .Head_weather {
    width: auto;
    height: 100%;
    padding: 0 30px;
    @--flexRow-R();
    .Head_weather_child {
      @--flexC();
      img {
        height: 70%;
        width: 50%;
        min-width: 3rem;
        min-height: 3rem;
        padding: 7px;
        box-sizing: border-box;
      }
      span {
        font-size: 0.7vw;
        color: @--fontColor;
        padding: 0 10px 0 0;
      }
      &::after {
        content: "";
        width: 1px;
        height: 70%;
        border-left: 2px solid #acb9be;
      }
    }
    .Head_weather_air {
      display: flex;
      justify-content: space-evenly;
      flex-direction: column;
      color: @--fontColor;
      line-height: 50%;
      padding: 0 0 0 20px;
    }
  }
  .Head_date {
    width: auto;
    height: 100%;
    @--flexC();
    color: @--fontColor;
    > span:nth-child(1) {
      font-size: 1.8rem;
      margin-right: 1.7rem;
    }
    .Head_date_time {
      @--flexColumn();
      font-size: 1rem;
      color: #dbdbdb !important;
    }
  }
}
.left{ left: 1vw;}
.right{ right:1vw}
.bottom{
    top:inherit;bottom: -2vw;
    display: flex;
    div{
      width: 8rem; height: 8rem ;
      padding: 0 20px;
      position: relative;
      cursor: pointer;
      transition: scale 0.3s;
      span{
        color: @--fontColor;
        font-size: 1.2rem;
        @--Cpoa();
        top: 70%;
        text-shadow: 0 2px 4px #000000;
        white-space: nowrap;
        user-select: none;
      }
      &:hover{
        filter: hue-rotate(220deg) saturate(130%) brightness(140%);
        scale: 1.05;
      }
      &:active{ scale: 1;}
    }
}

.boxOfrsrp {
    display: flex;
    flex-direction: column;
    >div{
        flex-grow: 1;
        position: relative;
        .pie_title{
            @--flexRow();
            justify-content: space-between;
            >div{display: flex;}
            select{ 
                width: 4cqw; height: 100%;
                background: #00607030;
                border: 1px solid #00DAFF;
                border-radius: 3px;
                color: @--fontColor;
                option{ color: #777777;}
            }
        }
        .pie_content{ 
            height: calc(100% - 3rem);
            width: 100%;
            position: absolute;
        }
    }
}

.station,.tianxian{
    height: auto; width: 18vw;
    min-width: 24rem;
    right: 1vw;
    .card_title .card_title_content{
      width: 100%;
      background:url("~@/assets/UIpic/container-title.png") no-repeat center center;
      background-size: 100% 100%;
    }
    .card_content ul li{
      display: flex; justify-content: space-between;
      padding: 0.7rem 1rem;
      :first-child{color: @--fontColor;}
      :last-child{color:#00DAFF}
    }
    .card_content>.block{
      padding: 10px;color: @--fontColor;
      .demonstration{font-size: 1.1rem ;}
    }
    .card_content>.switch{margin-top: 0.7rem;}
}
.tianxian{
    width: 20vw;
    .block .el-slider__runway{margin: 8px !important;}
}

.fangan,.carConfig,.SamplingList,.fangzhenresult,.stationOfFenxi{
    left: 1vw;top: 10vh;
    height: 21rem; width: 30rem;
    padding: 0 !important;
    .card_title{
        padding: 0 1.7rem 0 0;
        .card_title_content{
          height: 3rem;
          background:url("~@/assets/UIpic/card_title-b.png") no-repeat left center;
          background-size: 100% 100%;
          @--flexC();
          span{padding: 0 4.5rem;}
        }
        .card_title_icon>img:hover{ filter: saturate(180%) brightness(180%);}
    }
  .card_content{height: calc(100% - 3rem - 4px);}
  .block{
    color: @--fontColor;
    padding: 0.9rem 2rem;
    img{width: 2rem;margin-right: 5px;}
  }
  &::after{
    content: '';
    display: inline-block;
    height: 4px;
    width: 7.2rem;
    background: url("~@/assets/UIpic/card_ts.png");
    float: right;
  }
}

.jifang{
    left: 1vw;top: 10vh;
    height: auto; width: 30rem;
    .card_title{
        padding: 0 1.7rem 0 0;
        .card_title_content{
            width: 100%;
          height: 3rem;
          background:url("~@/assets/UIpic/container-title.png") no-repeat left center;
          background-size: 100% 100%;
          @--flexC();
          span{padding: 0 4.5rem;}
        }
    }
    .card_content>.el-row{
        @--flexC();
        justify-content: space-between;
        padding: 10px;
        color: @--fontColor;
        >:first-child{ @--flexC(); flex-grow: 1;}
        >:last-child{
            button {
                color: @--fontColor;
                background: transparent;
                border: 1px solid #fff;
                margin-left: 10px;
                padding: 0.3rem 0.7rem;
                border-radius: 3px;
                cursor: pointer;
                &:hover {
                  background: #409eff !important;
                  border: 1px solid #409eff;
                }
            }
        }
    }
}

.chenjin{
    left: 1vw;top: 10vh;
    height: auto;
    .card_title{
      padding: 0 1.7rem 0 0;
      .card_title_content{
        height: 3rem;
        background:url("~@/assets/UIpic/container-title.png") no-repeat left center;
        background-size: 100% 100%;
        @--flexC();
        span{padding: 0 4.5rem;}
      }
      .card_title_icon>img:hover{ filter: saturate(180%) brightness(180%);}
    }
    .card_content>.el-row{
        display: flex; justify-content: space-between;
        padding: 10px;
        >div{ @--flexC()}
        >:first-child {
          color: @--fontColor;flex-grow: 1;
          img {
            width: 2.4rem;
            height: 2.4rem;
            margin: 5px;
          }
        }
        >:last-child {
            button {
              border: 0;
              padding: 0.5rem 1.3rem;
              border-radius: 3px;
              cursor: pointer;
              transition: all 0.1s;
              color: #fff;
            white-space: nowrap;
            }
            :first-child {
              background: #0fb97a;
              margin-right: 1rem;
              &:hover {
                background: #34c992;
              }
              &:active {
                scale: 0.9;
              }
            }
            :last-child {
              background: #e4b127;
              &:hover {
                background: #e7bf51;
              }
              &:active {
                scale: 0.9;
              }
            }
        }
    }
    .el-list{
        >:last-child{.el-row{height: 4rem;}}
    }
}

.carConfig{
    height: auto; left: 22vw;
    .card_content{padding: 10px;}
}

.SamplingList{
    left: inherit;right: 1vw;
    overflow-x: auto;
    height: auto;
    .card_content{
        >:first-child{
            display: flex;
            justify-content: space-around;
            margin-top: 20px;
        }
        button{
            padding: 8px 24px;
            background-color: #00DAFF;
            border: 0;
            border-radius: 5px;
            color: @--fontColor;
            cursor: pointer;
            &:hover{ background-color: #8feeff; color: #232429;}
        }
        .el-list{
            padding: 10px;
            >div{ width: 60rem; }
            >:last-child{ max-height: 40rem;}
        }
    }
}

.fangzhenresult{
  z-index: 99;
    height: 60vh; width:  42vw;
    top: 50% !important;
    left: 50% !important;
    .card_title_content{ width: 27rem !important; }
    .card_content{
        @--flexColumn();
        padding: 10px;
        >:first-child{
            flex-grow:4;
            display: flex;
            color: @--fontColor;
            .el-col{ 
                @--flexColumn();
                position: relative;
                >div{ flex-grow: 1; 
                    width:100%;height: 100%;
                    position: absolute;
                    display: flex; align-items: center;
                    .scale{     
                        width: 75%;
                        height: 0;
                        padding-bottom: 62.5%;
                        position: relative;
                        >div{
                            position: absolute;
                            background: url("~@/assets/UIpic/<EMAIL>") no-repeat center center;
                            background-size: 50% 50%;
                        }
                        @--after:{
                            color:@--fontColor;
                            font-size: 1.5rem;
                            font-weight: bold;
                            @--Cpoa()
                        }
                        #resultChart::before{ content:'RSRP';@--after()}
                        #resultChart2::before{ content:'SINR';@--after()}
                    }
                    ul{
                      width: 45%;
                      li{
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        width: 100%; height: 2.5rem;
                        color: #fff;
                        :first-child{
                          :first-child{
                            display: inline-block;
                            width: 1.4rem;
                            height: 0.7rem;
                            background: aqua;
                            margin-left: 5px;
                          }
                        }
                        &:hover{
                          background: linear-gradient(to right,#ffffff50, #ffffff00);
                        }
                      }
                    }
                }
            }
            @--font:{
                font-size: 1.2rem;
                margin-left: 2rem;
                padding: 5px;
            }
            >:first-child{ &::before{
                content: '⋮ 路线RSRP信号质量占比';
                @--font()
            }}
            >:last-child{ &::before{
                content: '⋮ 路线SINR信噪比质量占比';
                @--font()
            }}
        }
        >:last-child{
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            >:first-child{
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 0 2rem;
                span{
                    color: @--fontColor;
                    font-size: 1.2rem;
                }
                button{
                    padding: 8px 24px;
                    background-image: linear-gradient(to right,#0098FF, #00DAFF );
                    border: 0;
                    border-radius: 5px;
                    color: @--fontColor;
                }
            }
            .el-list{
                height: 100%;
                .el-table,.el-table tr,.el-table th.el-table__cell{ background-color: transparent !important;}
                .el-table td.el-table__cell, .el-table th.el-table__cell.is-leaf{ border-bottom: 0 !important;}
                .el-table--border::after, .el-table--group::after, .el-table::before{ background-color: transparent !important;}
                .cell{ color: @--fontColor;}
                .el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell{ background-color: transparent !important;}
            }
        }
    }
}

.stationOfFenxi{
  left: inherit; right: 22vw;
  height: auto;
  .card_title_content{ width: 100% !important;}
  .card_content{padding: 10px;}
  .card_content ul li{
    display: flex; justify-content: space-between;
    padding: 0.7rem 1rem;
    :first-child{color: @--fontColor;}
    :last-child{color:#00DAFF}
  }
}

.PanoramaClass{
  width: 1000px;
  height: 600px;
  border-radius: 10px;
  >div {
    position: relative;
    right: -950px;
    top: 17px;
    width: 32px;
    height: 32px;
    background: url("~@/assets/close.png");
    background-position: center center;
    background-size: cover;
    cursor: pointer;
    z-index: 9;
  }
  iframe{
    width: 100%; height: 100%; 
    position: absolute;
    top: 0;
  }
}


.hud2 {
  position: absolute;
  width: 20rem;
  height: 10rem;
  padding: 20px 10px;
  box-sizing: border-box;
  left: 856px;
  top: 300px;
  background: url("~@/assets/UIpic/hub.png");
  background-size: 100% 100%;
  border-radius: 5px;
  text-align: center;
  backdrop-filter: blur(50px);
}
