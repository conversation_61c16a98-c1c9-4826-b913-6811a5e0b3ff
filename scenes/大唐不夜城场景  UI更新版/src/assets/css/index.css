* {
  padding: 0;
  margin: 0;
}

@font-face {
  font-family: 'pmzd';
  src: url('../PangMenZhengDaoBiaoTiTi-1.ttf');
}

@font-face {
  font-family: 'siyuan';
  src: url('../SourceHanSansCN-Regular.ttf');
}

body {
  width: 100vw;
  height: 100vh;
  position: relative;
  /* background: url('../bg.png') no-repeat; */
  overflow: hidden;
  font-family: 'siyuan';
}

body::before {
  content: '';
  width: 98%;
  height: 9.3vh;
  position: absolute;
  left: 1%;
  bottom: 0;
  background: url('../bg-border-bottom.png') no-repeat;
  background-size: 100% 100%;
  background-position: center bottom;
  z-index: 5;
}

body::after {
  content: '';
  width: 100%;
  height: 13px;
  position: absolute;
  left: 0;
  top: 0;
  background: url('../bg-border-top.png') no-repeat;
  background-size: 100% 80%;
  background-position: center bottom;
}

.border-left {
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
  width: 18vw;
  height: 100%;
  background: url('../bg-border-left1.png') no-repeat, linear-gradient(90deg, rgba(14, 24, 35, 1), rgba(14, 24, 35, 0) 100%);
  background-size: 100% 100%;
  background-position: center center;
}

.border-right {
  position: absolute;
  right: 0;
  top: 0;
  width: 18vw;
  height: 100%;
  z-index: 1;
  background: url('../bg-border-right1.png') no-repeat, linear-gradient(270deg, rgba(14, 24, 35, 1), rgba(14, 24, 35, 0) 100%);
  background-size: 100% 100%;
  background-position: center center;
}

#app {
  width: 100%;
  height: 100%;
  position: relative;
}

#app::before {
  content: '';
  position: absolute;
  width: 22px;
  height: calc(100% - 130px - 4.3vh);
  left: 0;
  top: 130px;
  background: url('../bg-border-left.png') no-repeat;
  background-size: 100% 100%;
  background-position: center center;
  z-index: 9;
}

#app::after {
  content: '';
  position: absolute;
  width: 22px;
  height: calc(100% - 130px - 4.3vh);
  right: 0;
  top: 130px;
  background: url('../bg-border-right.png') no-repeat;
  background-size: 100% 100%;
  background-position: center center;
}

.header {
  position: absolute;
  width: 100%;
  left: 0%;
  top: 0%;
  z-index: 1;
  height: 7vh;
  padding: 10px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  background: linear-gradient(180deg, rgba(14, 24, 35, 1), rgba(14, 24, 35, 0));
}

.header-left {
  font-family: 'pmzd';
}

.header-right {
  width: 21%;
  height: 70%;
  float: right;
  color: #FFFFFF;
  display: flex;
  justify-content: space-around;
}

.time {
  width: 42%;
  height: 100%;
  line-height: 100%;
  display: flex;
  align-items: center;
}

.time>span:nth-child(1) {
  font-size: 1.1vw;
  margin-right: 5%;
}

.now-date {
  font-size: 0.5vw;
  color: #a4b0b5;
}

.temperature {
  height: 100%;
  width: 20%;
  display: flex;
  justify-content: space-around;
  align-items: center;
  position: relative;
  font-size: 0.8vw;
}

.temperature img {
  height: 80%;
  width: 40%;
}

.temperature::before {
  content: '';
  width: 1px;
  height: 100%;
  border-left: 1px solid #697e88;
  left: -10%;
  position: absolute;
}

.humidity {
  height: 100%;
  width: 20%;
  font-size: 0.8vw;
  display: flex;
  justify-content: space-around;
  align-items: center;
  position: relative;
}

.humidity img {
  height: 80%;
  width: 40%;
}

.humidity::before {
  content: '';
  width: 1px;
  height: 100%;
  border-left: 1px solid #697e88;
  left: -10%;
  position: absolute;
}

.air {
  width: 8%;
  height: 100%;
  font-size: 0.3vw;
  color: #a4b0b5;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  flex-direction: column;
  position: relative;
}

.air::before {
  content: '';
  width: 1px;
  height: 100%;
  border-left: 1px solid #697e88;
  left: -20%;
  position: absolute;
}

.title {
  font-size: 43px;
  font-weight: 400;
  color: #FFFFFF;
  background: linear-gradient(0deg, rgba(99, 177, 196, 1), rgba(255, 255, 255, 1));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.english {
  font-size: 19px;
  font-family: 'pmzd';
  font-weight: 400;
  color: #70E6FF;
}

.left {
  width: 17vw;
  height: calc(100vh - 9vh - 11vh);
  position: absolute;
  top: 11vh;
  left: 22px;
  z-index: 1;
  user-select: none;
}

/* .left::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 21px;
  left: 0;
  top: 0;
  background: url('../left-bg.png') no-repeat;
} */

.box-item {
  box-sizing: border-box;
  width: 100%;
  padding: 20px 10px;
  height: 33%;
  border-bottom: 6px solid transparent;
  border-image: url('../box-border.png') 30 30 stretch;
}

.box-item2 {
  width: 100%;
  height: 66%;
  box-sizing: border-box;
  /* margin-left: 5%; */
  padding: 20px 20px 0px 10px;
  border-bottom: 6px solid transparent;
  border-image: url('../box-border.png') 30 30 stretch;
}

.box-item::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
}

.content-title {
  height: 25%;
}

.content-title2 {
  height: 15%;
}

.title-up {
  font-family: 'pmzd';
  color: #FFFFFF;
  font-size: 21px;
  display: flex;
  align-items: center;
}

.title-down {
  font-size: 13px;
  color: #70E6FF;
  font-family: 'pmzd';
  font-weight: 400;
  padding-left: 29px;
}

.content-box {
  width: 17.36vw;
  height: 17vh;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-position: center center;
}

#RSRPCharts {
  position: absolute;
  left: 10px !important;
  top: 40px !important;
}

.content-box2 {
  width: 100%;
  height: 85%;
  /* margin-left: 10%; */
}

.echarts1 {
  background-image: url('../left1-echarts.png') !important;
}

.echarts2 {
  background-image: url('../left2-echarts.png') !important;
}


.box4 {
  width: 100%;
  height: 50%;
}

.box5 {
  width: 100%;
  height: 50%;
}

/* //  ---------------------------------- */
.box1-title {
  width: 90%;
  height: 3em;
  background-color: rgba(112, 230, 255, 0.16);
  color: #70E6FF;
  font-size: 0.7vw;
  line-height: 3em;
  font-weight: bold;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  z-index: 999999;
}

.box1-title .Ctitle {
  display: flex;
  align-items: center;
}

.box1-title .Ctitle::before {
  content: '';
  display: inline-block;
  width: 5px;
  height: 1.3em;
  background-color: #70E6FF;
  box-shadow: 0 0 10px #70E6FF;
  margin-right: 1em;
}

.box1-title .el-input__inner {
  height: 26px !important;
  line-height: 26px !important;
  padding: 0 10px !important;
  width: 100px !important;
}

.box1-title .el-input--suffix {
  width: 100%;
}

.box1-title .el-input__prefix {
  height: auto;
}

.el-select-dropdown__item {
  color: #3f3f41 !important;
}

.box1-content {
  width: 100%;
  height: 70%;
  /* background-image: url('../righ1-echarts.png') !important; */
  background-repeat: no-repeat;
  background-size: 90% 90%;
  background-position: center center;
}

.box1-bottom {
  display: flex;
  justify-content: space-between;
  color: #FFFFFF;
  margin-top: 0.5vh;
  font-size: 0.6vw;
}

.box1-bottom>span>span {
  font-size: 0.8vw;
  color: #70E6FF;
}

/* //  ---------------------------------- */

.right {
  width: 19vw;
  height: calc(100vh - 9vh - 11vh);
  position: absolute;
  top: 11vh;
  right: 10px;
  z-index: 1;
  /* backdrop-filter: blur(20px); */
  user-select: none;
}

/* .right::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 21px;
  left: 0;
  top: 0;
  background: url('../right-bg.png') no-repeat;
  background-size: 100% 100%;
  background-position: center center;
} */

table {
  width: 100%;
  height: 100%;
  border-spacing: 0 6px;
  text-align: center;
}

thead {
  color: #70E6FF;
}

tbody {
  color: #FFFFFF;
  font-weight: 400;
  font-size: 0.7vw;
}

tbody>tr {
  background-color: rgba(141, 149, 156, 0.29);
  padding: 5px;
}

tbody td {
  padding: 2px;
}

.btns {
  width: 100%;
  height: 12vh;
  position: absolute;
  left: 0;
  bottom: 0.5vh;
  z-index: 9;
  display: flex;
  justify-content: center;
  align-items: center;
}

.btns>div {
  margin: 0 50px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 5;
  color: #70E6FF;
  font-weight: bold;
  position: relative;
  cursor: pointer;
}

.btns>div::before {
  content: '';
  position: absolute;
  border-left: 8px solid #CCF2FF;
  border-right: 8px solid transparent;
  border-bottom: 8px solid transparent;
  border-top: 8px solid transparent;
  left: -1vw;
  margin: 0 auto;
}

.btns>div::after {
  content: '';
  position: absolute;
  border-left: 8px solid transparent;
  border-right: 8px solid #CCF2FF;
  border-bottom: 8px solid transparent;
  border-top: 8px solid transparent;
  right: -1vw;
  margin: 0 auto;
}

.btns>div>div {
  cursor: pointer;
  position: relative;
  width: 3.7vw;
  height: 8vh;
  background-size: 100% 100%;
  background-position: center center;
  background-repeat: no-repeat;
}

.btns>div.follow>div {
  background-image: url('../btn2.png');
}

.btns>div.follow.active>div {
  background-image: url('../btn2-active.png') !important;
}

.btns>div.start>div {
  background-image: url('../btn2.png');
}

.btns>div.start.active>div {
  background-image: url('../btn2-active.png') !important;
}

.btns>div.stop>div {
  background-image: url('../btn2.png');
}

.btns>div.stop.active>div {
  background-image: url('../btn2-active.png') !important;
}

.btns>div.selectsj>div {
  background-image: url('../btn2.png');
}

.btns>div.selectsj.active>div {
  background-image: url('../btn2-active.png') !important;
}

.btns>div.active>span {
  color: #feb670;
}

.btns>div>div::before {
  content: '';
  position: absolute;
  width: 45px;
  height: 5px;
  left: 0;
  bottom: 0;
}

.btns>div>div:active {
  transform: scale(0.9);
}

/* .btns>div.stop>div:active {
  transform: scale(0.9);
  background-image: url('../btn3-active.png');
} */

iframe {
  width: 100%;
  height: 100%;
  border: none;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 0;
}

.pop-frame {
  position: absolute;
  min-width: 10vw;
  height: 11%;
  background-color: rgba(15, 50, 82, 0.651);
  border-radius: 10px;
  z-index: 5;
  left: 50%;
  top: 33%;
  transform: translate(-50%, -50%);
  pointer-events: none;
  padding: 7px;
  /* box-sizing: border-box; */
  display: none;
}

.number {
  width: 20px;
  height: 20px;
  line-height: 20px;
  text-align: center;
  font-size: 14px;
  color: #FFFFFF;
  position: absolute;
  right: 10px;
  top: 10px;
  border-radius: 50%;
  background-color: rgb(188, 112, 21);
}

.pop-frame.chache {
  background-color: rgba(75, 43, 12, 0.651);
}

.pop-frame.goods {
  height: 10%;
  background-color: rgba(2, 54, 65, 0.651);
}

.pop-frame>ul {
  height: calc(100% - 4px);
  width: calc(100% - 4px);
  margin: 2px auto;
  border: 1px solid rgb(84, 175, 255);
  border-radius: 10px;
  list-style: none;
  padding: 2px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  white-space: nowrap;
}

.pop-frame.chache>ul {
  border: 1px solid #ff9920;
}

.pop-frame.goods>ul {
  border: 1px solid #70e6ff;
}

.pop-frame>ul::before {
  content: '';
  position: absolute;
  left: 0;
  top: 10%;
  width: 5px;
  height: 25%;
  background-color: #54afff;
}

.pop-frame>ul::after {
  content: '';
  position: absolute;
  left: 0;
  top: 45%;
  width: 5px;
  height: 8%;
  background-color: #54afff;
}

.pop-frame.chache>ul::before {
  background-color: #ff9920;
}

.pop-frame.chache>ul::after {
  background-color: #ff9920;
}

.pop-frame.goods>ul::before {
  background-color: #70e6ff;
}

.pop-frame.goods>ul::after {
  background-color: #70e6ff;
}

.pop-frame>img {
  width: 0.4vw;
  height: 0.4vw;
  position: absolute;
  right: 0.2vw;
  top: 0.2vw;
  cursor: pointer;
  z-index: 5;
  pointer-events: fill;
}

ul>li {
  width: 100%;
  font-size: 14px;
  margin: 2px 0;
  padding: 0 5px;
}

ul>li>span:first-child {
  color: #7ec3ff;
}

/* ul>li>span:last-child {
  color: #FFFFFF;
} */

.chache>ul>li>span:first-child {
  color: #ff9920;
}

.goods>ul>li>span:first-child {
  color: #70e6ff;
}

.pop-video {
  width: 23%;
  height: 27%;
  /* background: url(../video.png) no-repeat; */
  background-size: 100% 100%;
  position: absolute;
  top: 40%;
  left: 35%;
  z-index: 1;
  display: none;
}

.close {
  color: white;
  position: absolute;
  right: 1%;
  cursor: pointer;
}

.video-content {
  width: 93%;
  height: 90%;
  /* border: 1px solid black; */
  margin: auto;
  margin-top: 2.5%;
}

video {
  width: 100%;
  height: 100%;
  object-fit: fill;
}