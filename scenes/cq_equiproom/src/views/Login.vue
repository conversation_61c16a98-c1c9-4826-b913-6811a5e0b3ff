<template>
  <div id="loginviwe" @keyup.enter="login">
    <div class="loginbox">
      <!-- 头像 -->
      <div class="touxiang_box">
        <img src="../assets/city.svg" alt="" />
      </div>
      <!-- 登录表单 -->
      <el-form
        class="login_from"
        :model="from_model"
        :rules="from_rules"
        ref="from_ref"
      >
        <!-- 用户名 -->
        <el-form-item prop="username" label="用户名">
          <el-input v-model="from_model.username" placeholder="请输入用户名">
            <i slot="prefix" class="iconfont icon-renyuan1"></i>
          </el-input>
        </el-form-item>
        <!-- 密码 -->
        <el-form-item prop="password" label="密码">
          <el-input
            v-model="from_model.password"
            type="password"
            placeholder="请输入密码"
            show-password
          >
            <i slot="prefix" class="iconfont icon-mima"></i>
          </el-input>
        </el-form-item>
        <!-- 按钮区域 -->
        <el-form-item class="btns">
          <el-button
            style="width: 170px;"
            @click="dialogFormVisible = true"
            >注册</el-button
          >
          <el-button type="primary" @click="login" style="width: 170px;">登录</el-button>
          <!-- <el-button type="info" @click="resetLoginFrom">重置</el-button> -->
        </el-form-item>
      </el-form>
    </div>
    <!-- 注册页面 -->
    <div class="zc">
      <el-dialog title="用户注册" :visible.sync="dialogFormVisible">
        <!-- 登录表单 -->
        <el-form :model="userModel" :rules="from_rules" ref="from_ref">
          <!-- 用户名 -->
          <el-form-item prop="username" label="用户名">
            <el-input v-model="userModel.username" placeholder="请输入用户名">
              <i slot="prefix" class="iconfont icon-renyuan1"></i>
            </el-input>
          </el-form-item>
          <!-- 密码 -->
          <el-form-item prop="password" label="密码">
            <el-input
              v-model="userModel.password"
              type="password"
              placeholder="请输入密码"
              show-password
            >
              <i slot="prefix" class="iconfont icon-mima"></i>
            </el-input>
          </el-form-item>
          <!-- 确认密码 -->
          <el-form-item prop="password" label="确认密码">
            <el-input
              v-model="userModel.qrpassword"
              type="password"
              placeholder="请输入密码"
              @blur="jiaoyan"
              show-password
            >
              <i slot="prefix" class="iconfont icon-mima"></i>
            </el-input>
          </el-form-item>
          <!-- 所属地市 -->
          <el-form-item prop="city" label="所属地市">
            <el-select v-model="userModel.city" placeholder="请选择地市">
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                :disabled="item.disabled"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogFormVisible = false">取 消</el-button>
          <el-button type="primary" @click="addUser_usernameipt"
            >提交</el-button
          >
        </div>
      </el-dialog>
    </div>
  </div>
</template>


<script>
export default {
  data() {
    return {
      // 登录表单数据绑定对象
      from_model: {
        username: "",
        password: "",
      },
      //登录表单验证规则rules
      from_rules: {
        username: [
          { required: true, message: "请输入用户名", trigger: "blur" },
          {
            min: 3,
            max: 10,
            message: "长度应在 3 到 10 个字符之间",
            trigger: "blur",
          },
        ],
        password: [
          { required: true, message: "请输入密码", trigger: "blur" },
          {
            min: 6,
            max: 15,
            message: "长度应在 6 到 15 个字符之间",
            trigger: "blur",
          },
        ],
        city: [{ required: true, message: "请选择地市", trigger: "change" }],
      },
      dialogFormVisible: false,
      //用户注册表单
      userModel: {
        username: "",
        password: "",
        qrpassword: "",
        city: "",
      },
      options: [
        {
          value: "济南市",
          label: "济南市",
          disabled: false,
          lon: 117,
          lat: 36.65,
          height: 130000,
        },
        {
          value: "青岛市",
          label: "青岛市",
          disabled: false,
          lon: 120.33,
          lat: 36.07,
          height: 130000,
        },
        {
          value: "淄博市",
          label: "淄博市",
          disabled: false,
          lon: 118.05,
          lat: 36.78,
          height: 130000,
        },
        {
          value: "枣庄市",
          label: "枣庄市",
          disabled: false,
          lon: 117.57,
          lat: 34.86,
          height: 130000,
        },
        {
          value: "东营市",
          label: "东营市",
          disabled: false,
          lon: 118.49,
          lat: 37.46,
          height: 130000,
        },
        {
          value: "烟台市",
          label: "烟台市",
          disabled: false,
          lon: 121.39,
          lat: 37.52,
          height: 130000,
        },
        {
          value: "潍坊市",
          label: "潍坊市",
          disabled: false,
          lon: 119.1,
          lat: 36.62,
          height: 130000,
        },
        {
          value: "济宁市",
          label: "济宁市",
          disabled: false,
          lon: 116.59,
          lat: 35.38,
          height: 130000,
        },
        {
          value: "泰安市",
          label: "泰安市",
          disabled: false,
          lon: 117.13,
          lat: 36.18,
          height: 130000,
        },
        {
          value: "威海市",
          label: "威海市",
          disabled: false,
          lon: 122.1,
          lat: 37.5,
          height: 130000,
        },
        {
          value: "日照市",
          label: "日照市",
          disabled: false,
          lon: 119.46,
          lat: 35.42,
          height: 130000,
        },
        {
          value: "滨州市",
          label: "滨州市",
          disabled: false,
          lon: 118.03,
          lat: 37.36,
          height: 130000,
        },
        {
          value: "德州市",
          label: "德州市",
          disabled: false,
          lon: 116.29,
          lat: 37.45,
          height: 130000,
        },
        {
          value: "聊城市",
          label: "聊城市",
          disabled: false,
          lon: 115.97,
          lat: 36.45,
          height: 130000,
        },
        {
          value: "临沂市",
          label: "临沂市",
          disabled: false,
          lon: 118.35,
          lat: 35.05,
          height: 130000,
        },
        {
          value: "菏泽市",
          label: "菏泽市",
          disabled: false,
          lon: 115.43,
          lat: 35.24,
          height: 130000,
        },
      ],
      cityName: "",
      params_t_user: {
        //请求用户
        token: "",
        objectIds: "",
        where: "",
        geometry: "",
        inSR: "",
        spatialRel: "esriSpatialRelIntersects",
        returnIdsOnly: false,
        returnGeometry: true,
        outSR: 4326,
        outFields: "*",
        orderByFields: "",
        f: "json",
      },
      addUserList: [], //注册用户数据组装
    };
  },
  methods: {
    //点击重置按钮  重置表单
    resetLoginFrom() {
      //this.$refs.from_ref.resetFields() 方法可以对整个表单进行重置，将所有字段值重置为初始值并移除校验结果
      this.$refs.from_ref.resetFields();
    },
    //登录
    login() {
      this.$refs.from_ref.validate((valid) => {
        if (valid) {
          this.params_t_user.where = `username = '${this.from_model.username}'`;
          new Promise((reslove, reject) => {
            //调用加载olt方法
            reslove(this.getUser(this.params_t_user));
          }).then((res) => {
            if (res) {
              console.log(res);
              if (this.from_model.password === res[0].attributes.password) {
                window.sessionStorage.setItem("city", res[0].attributes.city);
                console.log(window.sessionStorage.getItem("city"));
                this.$router.push("/Map");
                return this.$message.success("登录成功");
              } else {
                return this.$message.error("密码错误！");
              }
            } else {
              return this.$message.error("用户名不存在！");
            }
          });
        } else {
          return this.$message.error("登录失败");
        }
      });
    },
    // 请求用户表
    async getUser(paramsdata) {
      const { data: res } = await this.$http.get(`5/query`, {
        params: paramsdata,
      });
      if (res) {
        return res.features;
      }
    },
    //验证用户是否存在
    addUser_usernameipt() {
      this.$refs.from_ref.validate((valid) => {
        if (valid) {
          this.params_t_user.where = `username = '${this.userModel.username}'`;
          new Promise(async (reslove, reject) => {
            //调用加载olt方法
            let data = await this.getUser(this.params_t_user);
            if (data && data.length > 0) {
              return this.$message.error("该用户已存在！");
            } else {
              this.addUser();
            }
          });
        }
      });
    },
    jiaoyan() {
      if (this.userModel.qrpassword !== this.userModel.password) {
        return this.$message.error("两次密码不一致，请重新输入！");
      }
    },
    //注册用户
    async addUser() {
      this.dialogFormVisible = false;
      this.addUserList = {
        features: [
          {
            geometry: {
              x: 0,
              y: 0,
            },
            attributes: {
              username: this.userModel.username,
              password: this.userModel.password,
              city: this.userModel.city,
            },
          },
        ],
        f: "json",
      };
      const { data: res } = await this.$http({
        method: "post",
        url: `http://*************:28080/giserver-server/rest/service/FeatureServer/sdlyk/5/addFeatures`,
        data: this.addUserList,
      });
      if (res) {
        if (res.addResults[0].success) {
          return this.$message.success("用户注册成功！");
        } else {
          return this.$message.error("用户注册失败！");
        }
      }
    },
  },
};
</script>

<style>
#loginviwe {
  background-image: url("../assets/backgroundImage.jpg");
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  min-height: 100vh;
  height: 100%;
}
.loginbox {
  width: 400px;
  height: 330px;
  background-color: rgba(0, 5, 5, 0.3);
  border-radius: 5px;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  margin: auto;
}
.loginbox .el-input__icon {
  width: 65px !important;
}
.touxiang_box {
  height: 130px;
  width: 130px;
  border: 1px solid #eee;
  border-radius: 50%;
  padding: 10px;
  box-shadow: 0 0 10px #eee;
  position: absolute;
  left: 50%;
  transform: translateY(-50%) translateX(-50%);
  background-color: rgba(0, 5, 5, 0.3);
}
img {
  width: 100%;
  height: 100%;
  opacity: 0.8;
}
.login_from {
  position: absolute;
  bottom: 0;
  width: 100%;
  padding: 0 20px;
  box-sizing: border-box;
}
.btns {
  display: flex;
  /* justify-content: flex-end; */
}
.el-form-item__label {
  color: #ccc !important;
}
.el-dialog {
  width: 23% !important;
}
.el-form-item__content .el-input__inner {
  width: 350px !important;
}
.zc .el-input__icon {
  width: 145px !important;
}
</style>
