<template>
  <div>
    <img class="weather-icon"
         :src="weatherImg"/>
    <div class="temperature">
        <h5 class="temperature-num">{{temperature}}</h5>
        <span class="temperature-unit">°</span>
    </div>
    <div class="weather-info">
        <p>{{city}}</p>
        <p>{{reporttime}}</p>
    </div>
    <div class="humiditys">
        <span>空气质量：</span>
        <span :style="{backgroundColor:humidity<=90 ? '#73d538' : '#FF4D4D'}" class="humidity_color">{{humidity}}</span>
    </div>
  </div>
</template>

<script>
import yun from '../assets/yun.png'
import qing from '../assets/qing.png'
import wu from '../assets/wu.png'
import yu from '../assets/yu.png'
import xue from '../assets/xue.png'
import bingbao from '../assets/bingbao.png'
import yin from '../assets/yin.png'
export default {
  data () {
    return {
      city: "",
      temperature: "",
      reporttime: "",
      weatherImg: "",
      humidity: "",
      weather: "",
    }
  },
  mounted () {
    this.getNowFormatDate()
    this.getTianqiApi()
  },
  methods: {
    //调用天气
    async getTianqiApi () {
      const { data: res } = await this.$http.get(window.g.tianqiUrl, {
        params: {
          city: 500000,
          key: "e369cdf747017217b4cb7e170651578c"
        },
      });
      if (res) {
        this.city = res.lives[0].city
        this.temperature = res.lives[0].temperature
        this.humidity = res.lives[0].humidity
        this.weather = res.lives[0].weather
        this.$emit("modifyName", this.weather);
        switch (res.lives[0].weather) {
          case "多云":
            this.weatherImg = yun
            break;
          case "晴":
            this.weatherImg = qing
            break;
          case "雾":
            this.weatherImg = wu
            break;
          case "霾":
            this.weatherImg = wu
            break;
          case "雨":
            this.weatherImg = yu
            break;
          case "雪":
            this.weatherImg = xue
            break;
          case "冰雹":
            this.weatherImg = bingbao
            break;
          case "阴":
            this.weatherImg = yin
            break;
          default:
            break;
        }
      }
    },
    //获取当前日期函数
    getNowFormatDate () {
      let date = new Date(),
        year = date.getFullYear(), //获取完整的年份(4位)
        month = date.getMonth() + 1, //获取当前月份(0-11,0代表1月)
        strDate = date.getDate() // 获取当前日(1-31)
      if (month >= 1 && month <= 9) month = '0' + month // 如果月份是个位数，在前面补0
      if (strDate >= 0 && strDate <= 9) strDate = '0' + strDate // 如果日是个位数，在前面补0

      let currentdate = `${month}月${strDate}日`
      this.reporttime = currentdate
      return this.reporttime
    }
  }
}
</script>

<style>
* {
  margin: 0;
  padding: 0;
}

A IMG {
  border: 0;
}

A:link {
  color: #000;
  text-decoration: none;
}

A:visited {
  color: #333;
  text-decoration: none;
}

A:hover {
  color: #ff5200;
  text-decoration: none;
}

A:active {
  color: #ff5200;
  text-decoration: none;
}

p {
  padding: 0;
  margin: 0;
}

dd {
  white-space: nowrap;
}

LI,
UL,
h2,
h3 {
  margin: 0;
  padding: 0;
  list-style-type: none;
}

button,
input,
select,
textarea {
  outline: 0;
}

.temperature {
  float: left;
  position: relative;
  padding-right: 10px;
  height: 56px;
  font-family: "Segoe UI";
  text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.2);
}

.temperature-num {
  font-size: 40px;
  line-height: 56px;
  font-weight: normal;
  height: 56px;
}

.temperature-unit {
  position: absolute;
  right: 3px;
  top: 6px;
  font-size: 22px;
  width: 8px;
}

.weather-icon {
  float: left;
  width: 56px;
  height: 56px;
  padding-right: 5px;
}

.weather-info {
  font-size: 14px;
  float: left;
  height: 56px;
  padding-left: 2px;
  padding-top: 8px;
  text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.2);
}
.humiditys {
  font-size: 14px;
  position: relative;
  left: 70px;
  bottom: 8px;
  text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.2);
}
.humidity_color {
  height: 20px;
  padding: 0 5px;
  border-radius: 2px;
  color: #fff;
  box-shadow: 1px 1px 1px rgba(0, 0, 0, 0.2);
}
</style>