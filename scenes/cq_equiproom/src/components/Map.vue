<template>
  <div
    v-loading="loading"
    element-loading-text="拼命加载中"
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.8)"
    style="width: 100%; height: 100%"
  >
    <div id="box"></div>
    <div class="home" @click="clickHome"></div>
    <Tianqi ref="tianqi" class="tianqis" @modifyName="tianqi"/>
    <!-- <div id="charts"></div> -->
    <About ref="about" class="map-operate" />
    <Lonlat ref="long" class="buttom_lng" />
  </div>
</template>

<script>
import flyTos from "../js/flyTo.js";
import cesiumMap from "../js/cesiumMap.js";
import * as turf from "@turf/turf";
import Lonlat from "./lonlat.vue";
import About from "./About.vue";
import Tianqi from "./tianqi.vue";
import colors from "../assets/colors.png";
import * as echarts from "echarts";
import facility from "../assets/facility.png";
import oltImage from "../assets/OLT.png";
// import lineColor from "../assets/line.png";
import colorss from '../assets/colors.png'
import positiveX3 from "../assets/sky/bk.jpg"; //雨
import negativeX3 from "../assets/sky/rt.jpg"; //雨
import positiveY3 from "../assets/sky/ft.jpg"; //雨
import negativeY3 from "../assets/sky/lf.jpg"; //雨
import positiveZ3 from "../assets/sky/up.jpg"; //雨
import negativeZ3 from "../assets/sky/dn.jpg"; //雨
import waterNormals from "../assets/Water_2_M_Normal.jpg"; //水面
import water from "../assets/water.jpg"; //水面
import PolylineTrailMaterialProperty from "../js/PolylineTrailMaterialProperty.js"; //线纹理
import circleRippleMaterialProperty from "../js/circleRippleMaterialProperty.js"; //点纹理
import '../js/SkyBoxOnGround' //天空盒
import '../js/fog' //雾
import '../js/rain' //雨
import '../js/circleSpiralMaterialProperty' //螺旋圆
import '../js/lineFlowMaterialProperty.js' //随机线
import lineColor from "../assets/line3.png" //道路贴图
// import daolu from "../js/daolu.json" //道路
import label from "../assets/label4.png"; //label


let marksIndex = 1
let pitchValue = -5
let remainTime = 0;
let usedTime = 0;
let lastStage = null
let exection;
let guge;
let marks = [
  { lng: 106.5845, lat: 29.5698, height: 500, flytime: 25 }, // height:相机高度(单位米) flytime:相机两个标注点飞行时间(单位秒)
  // { lng: 106.5536, lat: 29.6022, height: 800, flytime: 12 },
  // { lng: 106.5363, lat: 29.6273, height: 800, flytime: 12 },
  // { lng: 106.5349, lat: 29.6942, height: 800, flytime: 10 },
  // { lng: 106.5347, lat: 29.7593, height: 800, flytime: 10 },
  { lng: 106.5155, lat: 29.7995, height: 450, flytime: 5 },
]
let feiji = [
  {
    id: "document",
    name: "SampleFlight",
    version: "1.0",
    clock: {
      interval: "20221122T03Z/20221122T030300Z",
      currentTime: "20221122T03Z",
      multiplier: 3,
      range: "LOOP_STOP",
      step: "SYSTEM_CLOCK_MULTIPLIER",
    },
  },
  {
    name: "Aircraft1",
    availability: "20221122T03Z/20221122T030300Z",
    billboard: {
      color: { rgba: [0, 255, 255, 255] },
      eyeOffset: { cartesian: [0, 0, 0] },
      horizontalOrigin: "CENTER",
      image:
        "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAAjSURBVChTYyAa/EcDUGEIgIphAKg0XRSAAFQMDqDChAADAwDC13+BJ+0oDwAAAABJRU5ErkJgggAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==",
      pixelOffset: { cartesian2: [0, 0] },
      scale: 1,
      show: false,
      verticalOrigin: "CENTER",
    },
    id: "keji",
    model: {
      gltf: window.models.kejiUrl,
      minimumPixelSize: 125,
      maximumScale: 125,
    },
    position: {
      interpolationAlgorithm: "LAGRANGE",
      interpolationDegree: 1,
      epoch: "20221122T03Z",
      cartographicDegrees: [
        0, 106.45104873846681, 29.869447728269673, 1700,
        60, 106.44124972358978, 29.79603536891241, 1650.99,
        145, 106.49222090958474, 29.7251934964266, 1600.30,
        160, 106.52326198627252, 29.70834913351825, 1200.30,
        264, 106.6378537039566, 29.699274369296173, 900.11
      ],
    },
  }
];
export default {
  components: {
    Lonlat,
    About,
    Tianqi,
  },
  data () {
    return {
      token:
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJlYTQ2ZjdjNS1jM2E0LTQ1M2EtOWM0My1mODMzNzY3YjYzY2YiLCJpZCI6MjkzMjcsInNjb3BlcyI6WyJhc3IiLCJnYyJdLCJpYXQiOjE1OTE5NDIzNjB9.RzKlVTVDTQ9r7cqCo-PDydgUh8Frgw0Erul_BVxiS9c",
      loading: false,
      weather: "",
    };
  },
  created () {
    this.$nextTick(() => {
      // 禁用右键
      document.oncontextmenu = new Function("event.returnValue=false");
      // 禁用选择
      document.onselectstart = new Function("event.returnValue=false");
      //禁止f12
      // document.οnkeydοwn = new Function('event.returnValue=false')
    });
  },
  mounted () {
    this.common();
    this.loadBuild({
      lon: 106.51485784302207,
      lat: 29.80081310382411,
      height: 309,
      heading: 196,
      url: window.models.equiproomUrl
    }) //加载机楼
    this.loadBuild({
      lon: 106.51406636733195,
      lat: 29.801009247130935,
      height: 309,
      heading: 16,
      url: window.models.equiproomUrl
    }) //加载机楼
    this.loadBuild({
      lon: 106.51536381154597,
      lat: 29.800437870267057,
      height: 307.48,
      heading: 16,
      url: window.models.equiproomcpUrl
    })
    this.$nextTick(() => {
      this.$refs.long.mouseLonlat(window.viewer);
      // this.$refs.about.window.viewer = window.viewer
    });
    // this.getXqGEOjson(this.params)  // 请求小区数据
    // this.gethuGEOjson(this.params_hu)   // 加载分户数据
    this.materialLine(); //动态连线shader
    //标注
    this.poiIconLabelAdd({
      lon: 106.51485606000334,
      lat: 29.800783390636703,
      minheight: 330.151062626941787,
      maxheight: 355,
      name: "联通西南中心机房",
      color: "#FF6D47",
      x: -93,
      y: 0
    })
    //加载水面
    this.loadLj()
    //加载轮船
    // this.loadCzml()
    //加载云
    // this.createRandomClouds(8, -122.685, -122.67, 45.51, 45.525, 50, 250);
    //加载建筑
    this.loadBuilding(window.tileset.CQTitlesUrl)
    this.loadBuilding2(window.tileset.CQTitlesUrl1)
    // this.loadMolde()
    //开场动画
    this.setRotate();
  },
  methods: {
    tianqi (val) {
      this.weather = val
      switch (val) {
        case "多云":
          this.skyboxs({
            positiveX: positiveX3,
            negativeX: negativeX3,
            positiveY: positiveY3,
            negativeY: negativeY3,
            positiveZ: positiveZ3,
            negativeZ: negativeZ3
          })
          break;
        case "晴":
          this.skyboxs({
            positiveX: positiveX3,
            negativeX: negativeX3,
            positiveY: positiveY3,
            negativeY: negativeY3,
            positiveZ: positiveZ3,
            negativeZ: negativeZ3
          })
          break;
        case "阴":
          this.skyboxs({
            positiveX: positiveX3,
            negativeX: negativeX3,
            positiveY: positiveY3,
            negativeY: negativeY3,
            positiveZ: positiveZ3,
            negativeZ: negativeZ3
          })
          break;
        case "雨":
          this.skyboxs({
            positiveX: positiveX3,
            negativeX: negativeX3,
            positiveY: positiveY3,
            negativeY: negativeY3,
            positiveZ: positiveZ3,
            negativeZ: negativeZ3
          })
          break;
        default:
          break;
      }
    },
    common () {
      Cesium.Ion.defaultAccessToken = this.token;
      Cesium.Camera.DEFAULT_VIEW_RECTANGLE = Cesium.Rectangle.fromDegrees(
        80,
        22,
        130,
        50
      );
      window.viewer = new Cesium.Viewer("box", {
        scene3DOnly: true,
        infoBox: false, // 信息框
        shouldAnimate: true,
        vrButton: false, // VR按钮
        geocoder: false,
        homeButton: false, // 初始位置
        sceneModePicker: false,
        baseLayerPicker: false,
        navigationHelpButton: false,
        selectionIndicator: false, //选择框
        animation: true, //时间控件
        timeline: true, //时间线
        fullscreenButton: false,
        shadows: false, // 去掉阴影
        // terrainProvider: Cesium.createWorldTerrain(),
        terrainProvider: new Cesium.CesiumTerrainProvider({
          url: window.tileset.CQTerrainUrl,
          requestVertexNormals: true // 地形图上添加此属性 效果更好
        })
      });
      // 显示帧率
      // viewer.scene.debugShowFramesPerSecond = true;
      // 开启深度检测，默认是关闭的
      // viewer.scene.globe.depthTestAgainstTerrain = true;
      window.viewer.scene.globe.enableLighting = true; // 开启全球光照
      //分辨率调整函数
      window.viewer.scene.fxaa = true;
      window.viewer.scene.postProcessStages.fxaa.enabled = true;
      window.viewer.animation.container.style.visibility = 'hidden' // 不显示动画控件
      window.viewer.timeline.container.style.visibility = 'hidden' // 不显示动画控件
      var supportsImageRenderingPixelated =
        viewer.cesiumWidget._supportsImageRenderingPixelated;
      if (supportsImageRenderingPixelated) {
        var vtxfDpr = window.devicePixelRatio;
        while (vtxfDpr >= 2.0) {
          vtxfDpr /= 2.0;
        }
        window.viewer.resolutionScale = vtxfDpr;
      }
      window.viewer.imageryLayers.removeAll();
      //卫星影像
      viewer.imageryLayers.removeAll()
      guge = new Cesium.ImageryLayer(
        new Cesium.ArcGisMapServerImageryProvider({
          url: window.g.imageryLayerUrl,
          enablePickFeatures: !1,
        })
      );
      window.viewer.imageryLayers.add(guge);
      // var guge = new Cesium.ImageryLayer(new Cesium.UrlTemplateImageryProvider({
      //   url: 'http://map.geoq.cn/arcgis/rest/services/ChinaOnlineStreetPurplishBlue/MapServer/tile/{z}/{y}/{x}',
      //   tilingScheme: new Cesium.WebMercatorTilingScheme(),
      //   minimunlevel: 1,
      //   maximunlevel: 22,
      // }), {
      //   show: true
      // });
      // viewer.imageryLayers.add(guge);
      // var zhuji = new Cesium.ImageryLayer(new Cesium.WebMapTileServiceImageryProvider({
      //   url: "http://t0.tianditu.gov.cn/cia_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=cia&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles&tk=e08d99d7477e935bfa6b75d6fa484b7a",
      //   layer: "tdtCiaLayer",
      //   style: "default",
      //   format: "image/jpeg",
      //   tileMatrixSetID: "GoogleMapsCompatible"
      // }))
      // viewer.imageryLayers.add(zhuji);
      // 去除时间原因影响模型颜色
      // viewer.scene.light = new Cesium.DirectionalLight({
      //   //去除时间原因影响模型颜色
      //   direction: new Cesium.Cartesian3.fromDegrees(
      //     0.35492591601301104, -0.8909182691839401, -0.2833588392420772
      //   ),
      // });
      // var utc = Cesium.JulianDate.fromDate(new Date());//UTC
      // viewer.clock.currentTime = Cesium.JulianDate.addHours(utc, 8, new Cesium.JulianDate());//北京时间=UTC+8=GMT+8
      viewer.clock.currentTime = new Cesium.JulianDate(
        2459905,
        54037
      );
      viewer.cesiumWidget.screenSpaceEventHandler.removeInputAction(
        Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK
      );
      this.clickFeatures() //点击
    },
    terrain_loding (url) {
      var terrainProvider = new Cesium.CesiumTerrainProvider({
        url: url,
      });
      viewer.terrainProvider = terrainProvider;
    },
    createBuildingShader () {
      console.log(111)
      return new Cesium.CustomShader({
        lightingModel: Cesium.LightingModel.UNLIT,
        varyings: {
          v_normalMC: Cesium.VaryingType.VEC3
        },
        uniforms: {
          u_roof_texture: {
            value: new Cesium.TextureUniform({
              url: 'https://gis2.tiantugis.com:8443/3dmodels/cq/material/g_1_t_0.png'
            }),
            type: Cesium.UniformType.SAMPLER_2D
          },
          u_texture: {
            value: new Cesium.TextureUniform({
              url: 'https://gis2.tiantugis.com:8443/3dmodels/cq/material/g_1_w_6.png'
            }),
            type: Cesium.UniformType.SAMPLER_2D
          }
        },
        vertexShaderText: /* glsl */ `
void vertexMain(VertexInput vsInput, inout czm_modelVertexOutput vsOutput) {
  v_normalMC = vsInput.attributes.normalMC;
}`,
        fragmentShaderText: /* glsl */ `
void fragmentMain(FragmentInput fsInput, inout czm_modelMaterial material) {
  vec3 positionMC = fsInput.attributes.positionMC;
  float width = 100.0;
  float height = 100.0;
  if (dot(vec3(0.0, 0.0, 1.0), v_normalMC) > 0.95) {
    float textureX = mod(positionMC.x, width) / width;
    float textureY = mod(positionMC.y, height) / height;
    vec3 rgb = texture2D(u_roof_texture, vec2(textureX, textureY)).rgb;
    material.diffuse = rgb;
    return;
  } else {
    float textureX = 0.0;
    float dotXAxis = dot(vec3(0.0, 1.0, 0.0), v_normalMC);
    if (dotXAxis > 0.52 || dotXAxis < -0.52) {
      textureX = mod(positionMC.x, width) / width;
    } else {
      textureX = mod(positionMC.y, width) / width;
    }
    float textureY = mod(positionMC.z, height) / height;
    vec3 rgb = texture2D(u_texture, vec2(textureX, textureY)).rgb;
    material.diffuse = rgb;
    return;
  }
  material.diffuse = vec3(0.129, 0.157, 0.161);
}`
      })
    },
    loadBuilding (url) {
      const jizhan = new Cesium.Cesium3DTileset({
        url: url,
        maximumScreenSpaceError: 5, //用于驱动细节细化级别的最大屏幕空间误差。 默认16
        maximumMemoryUsage: 2048 * 2, //瓦片集可以使用的最大内存量（以 MB 为单位）。 默认512
        cullWithChildrenBounds: false, //优化选项。是否使用子边界体积的并集来剔除瓦片。默认true
        cullRequestsWhileMoving: false, //优化选项。不要请求由于相机移动而在返回时可能未使用的图块。这种优化只适用于静止的瓦片集。默认true
        cullRequestsWhileMovingMultiplier: 110.0, //优化选项。移动时用于剔除请求的乘数。较大的是更积极的剔除，较小的较不积极的剔除。 默认值60
        preloadWhenHidden: true, //tileset.show时 预加载瓷砖false。加载图块，就好像图块集可见但不渲染它们。 默认false
        preloadFlightDestinations: true, //优化选项。在相机飞行时在相机的飞行目的地预加载图块。。 默认true
        preferLeaves: true, //优化选项 最好先装载叶子。 默认false
        dynamicScreenSpaceError: true, //优化选项。减少距离相机较远的图块的屏幕空间错误。 默认false
        dynamicScreenSpaceErrorDensity: 0.00278, //用于调整动态屏幕空间误差的密度，类似于雾密度。
        skipLevelOfDetail: true,
        baseScreenSpaceError: 2048,
        skipScreenSpaceErrorFactor: 1,
        skipLevels: 0,
        immediatelyLoadDesiredLevelOfDetail: true,
        loadSiblings: false,
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
        shadows: Cesium.ShadowMode.CAST_ONLY,
        show: true,
        customShader: this.createBuildingShader(),
      });
      jizhan.style = new Cesium.Cesium3DTileStyle({
        color: {
          conditions: [
            ['${OBJECTID} === 319', `color("#83FF95")`],
            ["true", "color('#fff',0.8)"],
          ],
        }
      });
      viewer.scene.primitives.add(jizhan);
    },
    loadBuilding2 (url) {
      const jizhan = new Cesium.Cesium3DTileset({
        url: url,
        maximumScreenSpaceError: 5, //用于驱动细节细化级别的最大屏幕空间误差。 默认16
        maximumMemoryUsage: 2048 * 2, //瓦片集可以使用的最大内存量（以 MB 为单位）。 默认512
        cullWithChildrenBounds: false, //优化选项。是否使用子边界体积的并集来剔除瓦片。默认true
        cullRequestsWhileMoving: false, //优化选项。不要请求由于相机移动而在返回时可能未使用的图块。这种优化只适用于静止的瓦片集。默认true
        cullRequestsWhileMovingMultiplier: 110.0, //优化选项。移动时用于剔除请求的乘数。较大的是更积极的剔除，较小的较不积极的剔除。 默认值60
        preloadWhenHidden: true, //tileset.show时 预加载瓷砖false。加载图块，就好像图块集可见但不渲染它们。 默认false
        preloadFlightDestinations: true, //优化选项。在相机飞行时在相机的飞行目的地预加载图块。。 默认true
        preferLeaves: true, //优化选项 最好先装载叶子。 默认false
        dynamicScreenSpaceError: true, //优化选项。减少距离相机较远的图块的屏幕空间错误。 默认false
        dynamicScreenSpaceErrorDensity: 0.00278, //用于调整动态屏幕空间误差的密度，类似于雾密度。
        skipLevelOfDetail: true,
        baseScreenSpaceError: 2048,
        skipScreenSpaceErrorFactor: 1,
        skipLevels: 0,
        immediatelyLoadDesiredLevelOfDetail: true,
        loadSiblings: false,
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
        // shadows: Cesium.ShadowMode.ENABLED,
        show: true,
        customShader: this.createBuildingShader(),
        shadows: Cesium.ShadowMode.CAST_ONLY,
      });
      jizhan.style = new Cesium.Cesium3DTileStyle({
        color: {
          conditions: [
            ['${OBJECTID} === 319', `color("#83FF95")`],
            ["true", "color('#fff',0.8)"],
          ],
        },
        show: '${OBJECTID} !== 319 && ${OBJECTID} !== 320'
      });
      viewer.scene.primitives.add(jizhan);
    },
    //矩阵
    update3dtilesMaxtrix (params) {
      //旋转
      let mx = Cesium.Matrix3.fromRotationX(Cesium.Math.toRadians(params.rx));
      let my = Cesium.Matrix3.fromRotationY(Cesium.Math.toRadians(params.ry));
      let mz = Cesium.Matrix3.fromRotationZ(Cesium.Math.toRadians(params.rz));
      let rotationX = Cesium.Matrix4.fromRotationTranslation(mx);
      let rotationY = Cesium.Matrix4.fromRotationTranslation(my);
      let rotationZ = Cesium.Matrix4.fromRotationTranslation(mz);
      //平移
      let position = Cesium.Cartesian3.fromDegrees(params.tx, params.ty, params.tz);
      let m = Cesium.Transforms.eastNorthUpToFixedFrame(position);
      let scale = Cesium.Matrix4.fromUniformScale(1);
      // //缩放
      Cesium.Matrix4.multiply(m, scale, m);
      //旋转、平移矩阵相乘
      Cesium.Matrix4.multiply(m, rotationX, m);
      Cesium.Matrix4.multiply(m, rotationY, m);
      Cesium.Matrix4.multiply(m, rotationZ, m);
      //赋值给tileset
      console.log(params.tiles)
      params.tiles.modelMatrix = m;
    },
    //天空
    skyboxs (skyImg) {
      let viewer = window.viewer
      let groundSkybox = new Cesium.GroundSkyBox({
        sources: {
          positiveX: skyImg.positiveX,
          negativeX: skyImg.negativeX,
          positiveY: skyImg.positiveY,
          negativeY: skyImg.negativeY,
          positiveZ: skyImg.positiveZ,
          negativeZ: skyImg.negativeZ
        }
      });
      let defaultSkybox = viewer.scene.skyBox;
      // // 渲染前监听并判断相机位置
      viewer.scene.preUpdate.addEventListener(() => {
        let position = viewer.scene.camera.position;
        let cameraHeight = Cesium.Cartographic.fromCartesian(position).height;
        if (cameraHeight < 240000) {
          viewer.scene.skyBox = groundSkybox;
          viewer.scene.skyAtmosphere.show = false;
        } else {
          viewer.scene.skyBox = defaultSkybox;
          viewer.scene.skyAtmosphere.show = true;
        }
      })
    },
    //加载水面
    loadLj () {
      //水面  shader
      let polygonMaterial = new Cesium.EllipsoidSurfaceAppearance({
        material: new Cesium.Material({
          fabric: {
            type: 'Water',
            uniforms: {
              // baseWaterColor: new Cesium.Color(173 / 255, 165 / 255, 125 / 255, 0.5),  灰色
              baseWaterColor: new Cesium.Color(81 / 255, 195 / 255, 225 / 255, 0.6),
              // specularMap: water,
              normalMap: waterNormals,
              frequency: 1000.0,
              animationSpeed: 0.02,
              amplitude: 100.0,
              specularIntensity: 5,
              time: 20
            },
          },
        }),
        // fragmentShaderSource: fs,
        fragmentShaderSource: 'varying vec3 v_positionMC;\n' +
          'varying vec3 v_positionEC;\n' +
          'varying vec2 v_st;\n' +
          'void main()\n' +
          '{\n' +
          'czm_materialInput materialInput;\n' +
          'vec3 normalEC = normalize(czm_normal3D * czm_geodeticSurfaceNormal(v_positionMC, vec3(0.0), vec3(1.0)));\n' +
          '#ifdef FACE_FORWARD\n' +
          'normalEC = faceforward(normalEC, vec3(0.0, 0.0, 1.0), -normalEC);\n' +
          '#endif\n' +
          'materialInput.s = v_st.s;\n' +
          'materialInput.st = v_st;\n' +
          'materialInput.str = vec3(v_st, 0.0);\n' +
          'materialInput.normalEC = normalEC;\n' +
          'materialInput.tangentToEyeMatrix = czm_eastNorthUpToEyeCoordinates(v_positionMC, materialInput.normalEC);\n' +
          'vec3 positionToEyeEC = -v_positionEC;\n' +
          'materialInput.positionToEyeEC = positionToEyeEC;\n' +
          'czm_material material = czm_getMaterial(materialInput);\n' +
          '#ifdef FLAT\n' +
          'gl_FragColor = vec4(material.diffuse + material.emission, material.alpha);\n' +
          '#else\n' +
          'gl_FragColor = czm_phong(normalize(positionToEyeEC), material, czm_lightDirectionEC);\n' +
          'gl_FragColor.a=0.6;\n' +
          '#endif\n' +
          '}\n',
        vertexShader: [
          '#include <common>',
          '#include <fog_pars_vertex>',
          '#include <logdepthbuf_pars_vertex>',

          'uniform mat4 textureMatrix;',

          'varying vec4 vCoord;',
          'varying vec2 vUv;',
          'varying vec3 vToEye;',

          'void main() {',

          'vUv = uv;',
          'vCoord = textureMatrix * vec4( position, 1.0 );',

          'vec4 worldPosition = modelMatrix * vec4( position, 1.0 );',
          'vToEye = cameraPosition - worldPosition.xyz;',

          'vec4 mvPosition =  viewMatrix * worldPosition;', // used in fog_vertex
          'gl_Position = projectionMatrix * mvPosition;',

          '#include <logdepthbuf_vertex>',
          '#include <fog_vertex>',

          '}'
        ].join('\n')
      })
      //加载水面
      let lj = Cesium.GeoJsonDataSource.load(window.Geojson.liangjiang);
      lj.then(function (dataSource) {
        var entities = dataSource.entities.values;	//这里的entities包含了geojson中所有的实体
        for (var i = 0; i < entities.length; i++) {
          var positions = entities[i].polygon.hierarchy.getValue().positions;	//因为只有一个polygon，所以直接通过索引获取到其坐标属性
          var polygonHierarchy = {
            positions: positions	//	因为原本就是笛卡尔坐标，所以不需变动，否则需要转换
          }
          viewer.scene.primitives.add(new Cesium.GroundPrimitive({
            geometryInstances: new Cesium.GeometryInstance({
              geometry: new Cesium.PolygonGeometry({
                polygonHierarchy: polygonHierarchy,
                height: 0.2
              })
            }),
            appearance: polygonMaterial,//自定义appearance
            show: true
          }))
        }
      });
    },
    //地球旋转
    setRotate () { //传入所需定位的经纬度 及旋转的速度 旋转的圈数
      let that = this
      // let viewer = window.viewer
      // if (!obj.x || !obj.y) {
      //   return;
      // }
      // var v = 0.25;
      // var i = 0.6;
      // var q = 1;
      // var x = obj.x;
      // var y = obj.y;
      // var z = obj.z;
      // var interVal = window.setInterval(function () {
      //   x = x + v;
      //   if (x >= 108) {
      //     x = 108
      //     i += 0.2;
      //   }
      //   viewer.scene.camera.setView({
      //     destination: new Cesium.Cartesian3.fromDegrees(x, y, z || 21000000)
      //   });

      //   if (i == q) {
      //     clearInterval(interVal);
      //     callback();
      //   }
      // }, 6);
      //加载国界
      var promise = Cesium.GeoJsonDataSource.load(window.Geojson.gjx);
      //加载省界线
      var promise1 = Cesium.GeoJsonDataSource.load(window.Geojson.cqsj);
      //加载省界面
      var promise2 = Cesium.GeoJsonDataSource.load(window.Geojson.xianjie);
      //加载区界线
      var promise3 = Cesium.GeoJsonDataSource.load(window.Geojson.bpq);
      //加载区界面
      var promise4 = Cesium.GeoJsonDataSource.load(window.Geojson.bpqLine);
      //定位到中国
      flyTos({
        lon: 108.9423,
        lat: 34.2610,
        height: 8000000.0,
        heading: 357.8,
        pitch: -90,
        roll: 360,
      })
      promise.then(function (dataSource) {
        viewer.dataSources.add(dataSource);
        var entities = dataSource.entities.values;
        for (var i = 0; i < entities.length; i++) {
          entities[i]._show = true
          var entity = entities[i]
          entity.polyline.width = 3;
          entity.polyline.material = new Cesium.PolylineGlowMaterialProperty({
            glowPower: .3, //一个数字属性，指定发光强度，占总线宽的百分比。
            color: Cesium.Color.AQUA.withAlpha(0.5)
          })
        }
      });
      //移除国界 加载省界
      setTimeout(() => {
        //移除国界
        promise.then(function (dataSource) {
          viewer.dataSources.remove(dataSource);
        });
        //加载省界线
        promise1.then(function (dataSource) {
          viewer.dataSources.add(dataSource);
          var entities = dataSource.entities.values;
          for (var i = 0; i < entities.length; i++) {
            var entity = entities[i]
            entity.polyline.width = 6;
            entity.polyline.material = new Cesium.LineFlowMaterialProperty({
              // 尾迹线材质
              color: Cesium.Color.AQUA,
              trailLength: 0.2,
              period: 100,
            })
          }
        });
        //加载省界面
        promise2.then(function (dataSource) {
          viewer.dataSources.add(dataSource);
          var entities = dataSource.entities.values;
          for (var i = 0; i < entities.length; i++) {
            var entity = entities[i]
            entity.polygon.heightReference = Cesium.HeightReference.RELATIVE_TO_GROUND;  // 贴地
            entity.polygon.height = 0 // 距地高度0米
            entity.polygon.extrudedHeight = 300
            entity.polygon.material = that.getColorRamp(
              'rgba(0,158,255,0.6)',
              'rgba(0,255,231,0.3)'
            )
            entity.polygon.outline = false
            entity.polygon.outlineColor =
              Cesium.Color.fromCssColorString('#F14A5E').withAlpha(0.5)
            entity.polygon.outlineWidth = 5
          }
        });
        //定位到重庆
        flyTos({
          lon: 107.8529,
          lat: 29.8948,
          height: 900000.0,
          heading: 357.8,
          pitch: -90,
          roll: 360,
        })
        //动态墙
        // this.drawWall({
        //   positions: [106.51539796534094, 29.80032806965644, 106.51566740858311, 29.801193373629612,
        //     106.51565559046462, 29.801258776423445, 106.51552931477315, 29.801348289466922, 106.51363692505821,
        //     29.801733750550746, 106.51337418479123, 29.80172621704622, 106.5133371862373, 29.80171760840711,
        //     106.51324353207507, 29.80162185217012, 106.51312810502351, 29.800744679794875, 106.51312670439494, 29.80059597077043,
        //     106.51505627852194, 29.800273995280236, 106.5152139974679, 29.800271603493005
        //   ],
        //   maxheight: 360,
        //   minheight: 250,
        //   color: Cesium.Color.fromBytes(5, 246, 153).withAlpha(0.5),
        // });
        //关闭大气
        // viewer.scene.globe.showGroundAtmosphere = false;
        // 定位到北培区
        setTimeout(() => {
          promise1.then(function (dataSource) {
            viewer.dataSources.remove(dataSource);
          });
          promise2.then(function (dataSource) {
            viewer.dataSources.remove(dataSource);
          });
          //北培区面
          promise3.then(function (dataSource) {
            viewer.dataSources.add(dataSource);
            var entities = dataSource.entities.values;
            for (var i = 0; i < entities.length; i++) {
              var entity = entities[i]
              entity.polygon.heightReference = Cesium.HeightReference.RELATIVE_TO_GROUND;  // 贴地
              entity.polygon.height = 0 // 距地高度0米
              entity.polygon.extrudedHeight = 300
              entity.polygon.material = that.getColorRamp(
                'rgba(0,158,255,0.6)',
                'rgba(0,255,231,0.3)'
              )
              entity.polygon.outline = false
              entity.polygon.outlineColor =
                Cesium.Color.fromCssColorString('#F14A5E').withAlpha(0.5)
              entity.polygon.outlineWidth = 5
            }
          });
          //北培区线
          promise4.then(function (dataSource) {
            viewer.dataSources.add(dataSource);
            var entities = dataSource.entities.values;
            for (var i = 0; i < entities.length; i++) {
              var entity = entities[i]
              entity.polyline.width = 6;
              entity.polyline.material = new Cesium.LineFlowMaterialProperty({
                // 尾迹线材质
                color: Cesium.Color.AQUA,
                trailLength: 0.2,
                period: 100,
              })
            }
          });
          //定位到北培区
          flyTos({
            lon: 106.5365,
            lat: 29.8233,
            height: 103000.0,
            heading: 357.8,
            pitch: -90,
            roll: 360,
          })
          //定位到主城区
          setTimeout(() => {
            promise3.then(function (dataSource) {
              viewer.dataSources.remove(dataSource);
            });
            promise4.then(function (dataSource) {
              viewer.dataSources.remove(dataSource);
            });
            //设置地图色调
            // guge.brightness = 0.38
            // guge.contrast = 1.06
            // guge.saturation = 3
            //深度检测
            viewer.scene.globe.depthTestAgainstTerrain = true;
            //定位到主城区
            flyTos({
              lon: 106.5845,
              lat: 29.5698,
              height: 1200.0,
              heading: 357.8,
              pitch: -5,
              roll: 360,
            })
            //漫游
            setTimeout(() => {
              //漫游
              this.flyExtent()
              //天气效果
              if (this.weather == "雾") {
                new Cesium.FogEffect(viewer, {
                  visibility: 0.1,
                  color: new Cesium.Color(0.8, 0.8, 0.8, 0.3)
                });
              } else if (this.weather == "阴") {
                new Cesium.FogEffect(viewer, {
                  visibility: 0.05,
                  color: new Cesium.Color(0.8, 0.8, 0.8, 0.3)
                });
              } else if (this.weather == "霾") {
                new Cesium.FogEffect(viewer, {
                  visibility: 0.2,
                  color: new Cesium.Color(0.8, 0.8, 0.8, 0.3)
                });
              } else if (this.weather == "雨") {
                new Cesium.RainEffect(viewer, {
                  tiltAngle: -0.6, //倾斜角度
                  rainSize: 0.6, // 雨大小
                  rainSpeed: 120.0 // 雨速
                })
              }
              //绕点旋转
              setTimeout(() => {
                this.flyEvent('pause')
                //绕点旋转
                this.rotateHeading()
                setTimeout(() => {
                  viewer.clock.onTick.removeEventListener(exection)
                  setTimeout(() => {
                    viewer.clock.shouldAnimate = true
                    // //加载车
                    // this.loadCars()
                    // //加载飞机
                    this.loadCzml({
                      czml: feiji,
                      id: 'keji',
                      trackedEntity: false
                    })
                    console.log(viewer.clock.currentTime)
                    this.drawWall({
                      positions: [
                        106.51508715636092, 29.800460732640737,
                        106.51445205904403, 29.800621600440792,
                        106.5146317683158, 29.801161817800498,
                        106.51526169549413, 29.80100328201742,
                        106.51508715636092, 29.800460732640737,
                      ],
                      maxheight: 320.34,
                      minheight: 315.04,
                      color: Cesium.Color.fromBytes(5, 246, 153).withAlpha(0.5),
                    });
                    this.loadLine([
                      {
                        data: [
                          106.5150824435328,
                          29.800467913369577,
                          309.04,
                          106.5150824435328,
                          29.800467913369577,
                          333.32
                        ],
                        color: '#FF8F00'
                      },
                      {
                        data: [
                          106.51445764528262,
                          29.80062414883354,
                          309.04,
                          106.51445764528262,
                          29.80062414883354,
                          333.32
                        ],
                        color: '#FF8F00'
                      },
                      {
                        data: [
                          106.51463330150371,
                          29.8011583132876,
                          309.04,
                          106.51463330150371,
                          29.8011583132876,
                          333.32
                        ],
                        color: '#FF8F00'
                      },
                      {
                        data: [
                          106.51525802036268,
                          29.801002051130816,
                          309.04,
                          106.51525802036268,
                          29.801002051130816,
                          333.32
                        ],
                        color: '#FF8F00'
                      },
                      {
                        data: [
                          106.5150824435328,
                          29.800467913369577,
                          333.32,
                          106.51445764528262,
                          29.80062414883354,
                          333.32
                        ],
                        color: '#FF8F00'
                      },
                      {
                        data: [
                          106.51445764528262,
                          29.80062414883354,
                          333.32,
                          106.51463330150371,
                          29.8011583132876,
                          333.32
                        ],
                        color: '#FF8F00'
                      },
                      {
                        data: [
                          106.51463330150371,
                          29.8011583132876,
                          333.32,
                          106.51525802036268,
                          29.801002051130816,
                          333.32
                        ],
                        color: '#FF8F00'
                      },
                      {
                        data: [
                          106.51525802036268,
                          29.801002051130816,
                          333.32,
                          106.5150824435328,
                          29.800467913369577,
                          333.32
                        ],
                        color: '#FF8F00'
                      }
                    ])
                    // this.loadSlz({
                    //   lon: 106.51485606000334,
                    //   lat: 29.800783390636703,
                    //   height: 340,
                    // })
                    //随机线
                    // this.lineFlowInit(viewer, [106.51485606000334, 29.800783390636703], 180);
                    //加载道路
                    // this.loadRoadGeojson()
                    // 调用波纹圆
                    // this.loadEllipse({
                    //   position: { x: 106.51485606000334, y: 29.800783390636703, z: 0 },
                    //   semiMinorAxis: 1000.0,
                    //   semiMajorAxis: 1000.0,
                    //   color: "#EF911A",
                    //   alpha: 1,
                    //   speed: 6.0,
                    //   count: 4,
                    //   gradient: 0.2,
                    // })
                    setTimeout(() => {
                      flyTos({
                        lon: 106.515138,
                        lat: 29.799936,
                        height: 324.81650036411594,
                        heading: 340.16,
                        pitch: -2.66,
                        roll: 360.00,
                      })
                      setTimeout(() => {
                        if (this.$route.query?.target) {
                          window.top.postMessage('close', '*')
                        }
                      }, 3000)
                    }, 500)
                  }, 200)
                }, 4000)
              }, 4500)
            }, 3000)
          }, 4000)
        }, 4000)
      }, 4000)
    },
    //加载四棱锥
    loadSlz (pmaras) {
      let position = Cesium.Cartesian3.fromDegrees(
        pmaras.lon,
        pmaras.lat,
        pmaras.height
      )
      let heading = 0
      function diaoyong () {
        heading = heading + Cesium.Math.toRadians(30)
        var hpr = new Cesium.HeadingPitchRoll(heading, 0, 0)
        var orientation = Cesium.Transforms.headingPitchRollQuaternion(
          position,
          hpr
        )
        return orientation
      }
      viewer.entities.add({
        position: position, //椎体位置
        //通过CallbackProperty延迟回调函数一直调用封装的偏航角方法
        //false，返回的值如果改变则一直调用自身，diaoyong()返回的值是orientation，而orientation会根据每次heading 的不同而发生改变
        orientation: new Cesium.CallbackProperty(diaoyong, false),
        model: {
          show: true,
          uri: 'http://123.14.199.58:59083/models/gltf/sileizhui1.gltf',
          scale: 10,
          minimumPixelSize: 10,
          maximumScale: 10,
          lightcolor: new Cesium.Cartesian3(100, 100, 100)
        },
      })
    },
    //机楼
    loadBuild (pmaras) {
      let position = Cesium.Cartesian3.fromDegrees(
        pmaras.lon,
        pmaras.lat,
        pmaras.height,
      )
      let heading = Cesium.Math.toRadians(pmaras.heading)
      let pitch = Cesium.Math.toRadians(0)
      let roll = Cesium.Math.toRadians(0)
      let hpRolls = new Cesium.HeadingPitchRoll(heading, pitch, roll)
      let fixedFrameTransforms =
        Cesium.Transforms.localFrameToFixedFrameGenerator('south', 'east')
      var models2 = viewer.scene.primitives.add(
        Cesium.Model.fromGltf({
          position: position,
          url: pmaras.url,
          modelMatrix: Cesium.Transforms.headingPitchRollToFixedFrame(
            position,
            hpRolls,
            Cesium.Ellipsoid.WGS84,
            fixedFrameTransforms
          ),
          scale: 1,
          lightColor: new Cesium.Cartesian3(10, 10, 10),
          // shadows: Cesium.ShadowMode.CAST_ONLY,
          // color: new Cesium.Color(1, 1, 1, 0.4),
        })
      )

      models2.readyPromise.then(function (model) {
        // model.sphericalHarmonicCoefficients = coefficients;
        // model.specularEnvironmentMaps = environmentMapURL;
      })
    },
    //创建连线
    loadLine (data) {
      let getCustomMaterialLine = (image, color) => {
        return new Cesium.CustomMaterialLine({
          image: image,
          color: color,
          duration: 15000,
        })
      }
      data.forEach((item, index) => {
        viewer.entities.add({
          polyline: {
            positions: Cesium.Cartesian3.fromDegreesArrayHeights(item.data),
            width: 5,
            arcType: 1,
            material: getCustomMaterialLine(
              colorss,
              Cesium.Color.fromCssColorString(item.color).withAlpha(0.7)
            ),
          },
        })
      })
    },
    //yanse 
    getColorRamp (rampColor, centerColor) {
      var ramp = document.createElement('canvas')
      ramp.width = 50
      ramp.height = 50
      var ctx = ramp.getContext('2d')

      var grd = ctx.createRadialGradient(25, 25, 0, 25, 25, 50)
      grd.addColorStop(0, centerColor) // "rgba(255,255,255,0)"
      grd.addColorStop(1, rampColor)

      ctx.fillStyle = grd
      ctx.fillRect(0, 0, 50, 50)

      // return ramp;

      return new Cesium.ImageMaterialProperty({
        image: ramp,
        transparent: true,
      })
    },
    getRandomNumberInRange (minValue, maxValue) {
      return (
        minValue + Cesium.Math.nextRandomNumber() * (maxValue - minValue)
      );
    },
    //云
    createRandomClouds (
      numClouds,
      startLong,
      stopLong,
      startLat,
      stopLat,
      minHeight,
      maxHeight
    ) {
      const clouds = new Cesium.CloudCollection();
      const rangeLong = stopLong - startLong;
      const rangeLat = stopLat - startLat;
      for (let i = 0; i < numClouds; i++) {
        long = startLong + this.getRandomNumberInRange(0, rangeLong);
        lat = startLat + this.getRandomNumberInRange(0, rangeLat);
        height = this.getRandomNumberInRange(minHeight, maxHeight);
        scaleX = this.getRandomNumberInRange(150, 350);
        scaleY = scaleX / 2.0 - this.getRandomNumberInRange(0, scaleX / 4.0);
        slice = this.getRandomNumberInRange(0.3, 0.7);
        depth = this.getRandomNumberInRange(5, 20);
        aspectRatio = this.getRandomNumberInRange(1.5, 2.1);
        cloudHeight = this.getRandomNumberInRange(5, 20);
        clouds.add({
          position: Cesium.Cartesian3.fromDegrees(long, lat, height),
          scale: new Cesium.Cartesian2(scaleX, scaleY),
          maximumSize: new Cesium.Cartesian3(
            aspectRatio * cloudHeight,
            cloudHeight,
            depth
          ),
          slice: slice,
        });
      }
    },
    //随机点位函数
    generateRandomPosition (position, num) {
      let list = []
      for (let i = 0; i < num; i++) {
        // random产生的随机数范围是0-1，需要加上正负模拟
        let lon = position[0] + Math.random() * 0.04 * (i % 2 == 0 ? 1 : -1);
        let lat = position[1] + Math.random() * 0.04 * (i % 2 == 0 ? 1 : -1);
        list.push([lon, lat])
      }
      return list
    },
    //设置随机高度生成随机线
    lineFlowInit (_viewer, _center, _num) {
      let _positions = this.generateRandomPosition(_center, _num);
      _positions.forEach(item => {
        // 经纬度
        let start_lon = item[0];
        let start_lat = item[1];

        let startPoint = new Cesium.Cartesian3.fromDegrees(start_lon, start_lat, 430);

        // 随机高度
        let height = 5000 * Math.random();
        let endPoint = new Cesium.Cartesian3.fromDegrees(start_lon, start_lat, height);
        let linePositions = [];
        linePositions.push(startPoint);
        linePositions.push(endPoint);
        _viewer.entities.add({
          polyline: {
            positions: linePositions,
            material: new Cesium.LineFlowMaterialProperty({
              color: new Cesium.Color(27 / 255, 161 / 255, 205 / 255, 0.8),
              speed: 35 * Math.random(),
              percent: 0.1,
              gradient: 0.01
            })
          }
        })
      })
    },
    clickHome () {
      flyTos({
        lon: 106.5156,
        lat: 29.7981,
        height: 350.0,
        heading: 345.8,
        pitch: -5,
        roll: 360,
      })
    },
    //标注
    poiIconLabelAdd (labelCanshu) {
      viewer.entities.add({
        position: Cesium.Cartesian3.fromDegrees(
          labelCanshu.lon,
          labelCanshu.lat,
          370
        ),
        // label: {
        //   // 文本。支持显式换行符“ \ n”
        //   text:
        //     '联通西南数据中心1号楼数据机房' +
        //     '\n' +
        //     ' ' +
        //     '\n' +
        //     '              机架数：' +
        //     (parseInt(window.g.jiguiNum)) +
        //     '个',
        //   color: Cesium.Color.fromCssColorString('#fff'),
        //   // 字体样式，以CSS语法指定字体
        //   font: '10pt Helvetica',
        //   // 字体颜色
        //   fillColor: Cesium.Color.WHITE,
        //   // 背景颜色
        //   backgroundColor: Cesium.Color.fromCssColorString("rgba(254, 129, 6, 0.2)"),
        //   // 是否显示背景颜色
        //   showBackground: false,
        //   // 字体边框
        //   outline: true,
        //   // 字体边框颜色
        //   outlineColor: Cesium.Color.WHITE,
        //   // 字体边框尺寸
        //   outlineWidth: 1,
        //   // 应用于图像的统一比例。比例大于会1.0放大标签，而比例小于会1.0缩小标签。
        //   scale: 1,
        //   // 设置样式：FILL：填写标签的文本，但不要勾勒轮廓；OUTLINE：概述标签的文本，但不要填写；FILL_AND_OUTLINE：填写并概述标签文本。
        //   style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        //   // 相对于坐标的水平位置
        //   verticalOrigin: Cesium.VerticalOrigin.CENTER,
        //   // 相对于坐标的水平位置
        //   horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
        //   // 该属性指定标签在屏幕空间中距此标签原点的像素偏移量
        //   pixelOffset: new Cesium.Cartesian2(-95, 30),
        //   // 是否显示
        //   show: true,
        //   distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0, 1500),
        //   eyeOffset: new Cesium.Cartesian3(0, 0, -1),
        //   // translucencyByDistance: new Cesium.NearFarScalar(0, 1, 3000, 1)//随着距离改变透明度
        // },
        billboard: {
          // 图像地址，URI或Canvas的属性
          image: label,
          // 设置颜色和透明度
          color: Cesium.Color.WHITE.withAlpha(1),
          // 高度（以像素为单位）
          height: 160.06,
          // 宽度（以像素为单位）
          width: 270.17,
          // 逆时针旋转
          // rotation: 20,
          // 大小是否以米为单位
          sizeInMeters: false,
          // 相对于坐标的垂直位置
          // verticalOrigin: Cesium.VerticalOrigin.CENTER,
          // 相对于坐标的水平位置
          // horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
          // 该属性指定标签在屏幕空间中距此标签原点的像素偏移量
          eyeOffset: new Cesium.Cartesian3(0, 0, 1),
          pixelOffset: new Cesium.Cartesian2(0, 35),
          // 应用于图像的统一比例。比例大于会1.0放大标签，而比例小于会1.0缩小标签。
          scale: 1.5,
          // 是否显示
          show: true,
          //视角缩放范围 控制显示隐藏
          // 按距离缩放
          scaleByDistance: new Cesium.NearFarScalar(1.5e2, 1, 10000, 0.0),
          distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0, 1500),
          // translucencyByDistance: new Cesium.NearFarScalar(0, 1, 2000, 0)//随着距离改变透明度
        },
        // label: {
        //   //文字标签
        //   text: labelCanshu.name,
        //   font: "20px sans-serif",
        //   style: Cesium.LabelStyle.FILL,
        //   // 对齐方式(水平和竖直)
        //   fillColor: Cesium.Color.fromCssColorString("#5F6060"),
        //   horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
        //   verticalOrigin: Cesium.VerticalOrigin.CENTER,
        //   pixelOffset: new Cesium.Cartesian2(labelCanshu.x, labelCanshu.y),
        //   showBackground: true,
        //   backgroundColor: Cesium.Color.fromCssColorString("#AFEB80"),
        //   distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
        //     0,
        //     10000
        //   ),
        // },
      });
      // 先画线后画点，防止线压盖点
      let linePositions = [];
      linePositions.push(
        new Cesium.Cartesian3.fromDegrees(
          labelCanshu.lon,
          labelCanshu.lat,
          labelCanshu.minheight
        )
      );
      linePositions.push(
        new Cesium.Cartesian3.fromDegrees(
          labelCanshu.lon,
          labelCanshu.lat,
          labelCanshu.maxheight
        )
      );
      // viewer.entities.add({
      //   polyline: {
      //     positions: linePositions,
      //     width: 1.5,
      //     material: Cesium.Color.fromCssColorString(labelCanshu.color),
      //     distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
      //       0,
      //       2000
      //     ),
      //   },
      // });
      // // 画点
      // viewer.entities.add({
      //   // 给初始点位设置一定的离地高度，否者会被压盖
      //   position: Cesium.Cartesian3.fromDegrees(
      //     labelCanshu.lon,
      //     labelCanshu.lat,
      //     labelCanshu.minheight
      //   ),
      //   point: {
      //     color: Cesium.Color.fromCssColorString(labelCanshu.color),
      //     pixelSize: 8,
      //     distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
      //       0,
      //       2000
      //     ),
      //   },
      // });
    },
    //漫游
    flyExtent () {
      let viewer = window.viewer
      let that = this
      // 相机看点的角度，如果大于0那么则是从地底往上看，所以要为负值
      var pitch = Cesium.Math.toRadians(pitchValue)
      // 时间间隔2秒钟
      this.setExtentTime(marks[marksIndex].flytime)
      exection = function TimeExecution () {
        var preIndex = marksIndex - 1
        if (marksIndex == 0) {
          preIndex = marks.length - 1
        }
        var heading = that.bearing(
          marks[preIndex].lat,
          marks[preIndex].lng,
          marks[marksIndex].lat,
          marks[marksIndex].lng
        )
        heading = Cesium.Math.toRadians(heading)
        // 当前已经过去的时间，单位s
        var delTime = Cesium.JulianDate.secondsDifference(
          viewer.clock.currentTime,
          viewer.clock.startTime
        )
        var originLat =
          marksIndex == 0 ? marks[marks.length - 1].lat : marks[marksIndex - 1].lat
        var originLng =
          marksIndex == 0 ? marks[marks.length - 1].lng : marks[marksIndex - 1].lng
        var endPosition = Cesium.Cartesian3.fromDegrees(
          originLng +
          ((marks[marksIndex].lng - originLng) / marks[marksIndex].flytime) *
          delTime,
          originLat +
          ((marks[marksIndex].lat - originLat) / marks[marksIndex].flytime) *
          delTime,
          marks[marksIndex].height
        )
        viewer.scene.camera.setView({
          destination: endPosition,
          orientation: {
            heading: heading,
            pitch: pitch,
          },
        })
        if (
          Cesium.JulianDate.compare(
            viewer.clock.currentTime,
            viewer.clock.stopTime
          ) >= 0
        ) {
          viewer.clock.onTick.removeEventListener(exection)
          that.changeCameraHeading()
        }
      }
      viewer.clock.onTick.addEventListener(exection)
    },
    //daolu
    loadRoadGeojson () {
      let viewer = window.viewer;
      let colors = new Cesium.Color(77 / 255, 201 / 255, 255 / 255, 1);
      let colors1 = new Cesium.Color(244 / 255, 243 / 255, 21 / 255, 0.5);
      let colors2 = new Cesium.Color(77 / 255, 255 / 255, 52 / 255, 1);
      let colors3 = new Cesium.Color(30 / 255, 150 / 255, 255 / 255, 1);
      let duration = [1500, 5000]
      let getCustomMaterialLine = (image, color, duration) => {
        return new Cesium.CustomMaterialLine({
          image: image,
          color: color,
          duration: duration
        })
      }
      //国道
      let zzguodao = Cesium.GeoJsonDataSource.load(daolu, {
        clampToGround: true
      });
      zzguodao.then(dataSource => {
        viewer.dataSources.add(dataSource);
        var entities = dataSource.entities.values;
        for (var i = 0; i < entities.length; i++) {
          var line = entities[i];
          line.polyline.material = getCustomMaterialLine(lineColor, colors1, 8000)
          line.polyline.width = 25;
          line.polyline.glowPower = 1;
        }
      })
    },
    //相机定点转向
    changeCameraHeading () {
      let that = this
      var nextIndex = marksIndex + 1
      if (marksIndex == marks.length - 1) {
        nextIndex = 0
      }
      // 计算两点之间的方向
      var heading = this.bearing(
        marks[marksIndex].lat,
        marks[marksIndex].lng,
        marks[nextIndex].lat,
        marks[nextIndex].lng
      )
      // 相机看点的角度，如果大于0那么则是从地底往上看，所以要为负值
      var pitch = Cesium.Math.toRadians(pitchValue)
      // 给定飞行一周所需时间，比如10s, 那么每秒转动度数
      var angle = (heading - Cesium.Math.toDegrees(viewer.camera.heading)) / 2
      if (angle < -90) {
        angle += 180
      } else if (angle > 90) {
        angle -= 180
      }
      // 时间间隔2秒钟
      this.setExtentTime(2)
      // 相机的当前heading
      var initialHeading = viewer.camera.heading
      var Exection = function TimeExecution () {
        // 当前已经过去的时间，单位s
        var delTime = Cesium.JulianDate.secondsDifference(
          viewer.clock.currentTime,
          viewer.clock.startTime
        )
        var heading = Cesium.Math.toRadians(delTime * angle) + initialHeading
        viewer.scene.camera.setView({
          orientation: {
            heading: heading,
            pitch: pitch,
          },
        })
        if (
          Cesium.JulianDate.compare(
            viewer.clock.currentTime,
            viewer.clock.stopTime
          ) >= 0
        ) {
          viewer.clock.onTick.removeEventListener(Exection)
          marksIndex = ++marksIndex >= marks.length ? 0 : marksIndex
          that.flyExtent()
        }
      }
      viewer.clock.onTick.addEventListener(Exection)
    },
    //点击事件
    clickFeatures () {
      new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas).setInputAction(
        (e) => {
          let pick = viewer.scene.pick(e.position);
          let cartesian = viewer.scene.pickPosition(e.position);
          let lng, lat, alt;
          if (cartesian) {
            let cartographic = Cesium.Cartographic.fromCartesian(cartesian);
            lng = Cesium.Math.toDegrees(cartographic.longitude);
            lat = Cesium.Math.toDegrees(cartographic.latitude);
            alt = cartographic.height;
          }
          // 方位角
          let heading = Cesium.Math.toDegrees(viewer.camera.heading).toFixed(2);
          // 俯仰角
          let pitch = Cesium.Math.toDegrees(viewer.camera.pitch).toFixed(2);
          // 翻滚角
          let roll = Cesium.Math.toDegrees(viewer.camera.roll).toFixed(2);
          var position = viewer.scene.camera.positionCartographic
          var longitude = Cesium.Math.toDegrees(position.longitude).toFixed(6)
          var latitude = Cesium.Math.toDegrees(position.latitude).toFixed(6)
          var height = position.height
          console.log(heading, pitch, roll, longitude, latitude, height);
          // alert("经度：" + lng + "纬度：" + lat + "高度：" + height)
          if (pick.id instanceof Cesium.Entity && pick.id._name == "机楼") {
            console.log('机楼')
            if (this.$route.query?.target) {
              window.top.postMessage('close', this.$route.query.target)
            }
          }
        }, Cesium.ScreenSpaceEventType.LEFT_CLICK)
    },
    // 设置飞行的时间到viewer的时钟里
    setExtentTime (time) {
      var startTime = Cesium.JulianDate.fromDate(new Date())
      var stopTime = Cesium.JulianDate.addSeconds(
        startTime,
        time,
        new Cesium.JulianDate()
      )
      viewer.clock.startTime = startTime.clone() // 开始时间
      viewer.clock.stopTime = stopTime.clone() // 结速时间
      viewer.clock.currentTime = startTime.clone() // 当前时间
      viewer.clock.clockRange = Cesium.ClockRange.CLAMPED // 行为方式
      viewer.clock.clockStep = Cesium.ClockStep.SYSTEM_CLOCK_MULTIPLIER // 时钟设置为当前系统时间; 忽略所有其他设置。
    },
    toRadians (degrees) {
      return (degrees * Math.PI) / 180
    },
    toDegrees (radians) {
      return (radians * 180) / Math.PI
    },
    bearing (startLat, startLng, destLat, destLng) {
      startLat = this.toRadians(startLat)
      startLng = this.toRadians(startLng)
      destLat = this.toRadians(destLat)
      destLng = this.toRadians(destLng)

      let y = Math.sin(destLng - startLng) * Math.cos(destLat)
      let x =
        Math.cos(startLat) * Math.sin(destLat) -
        Math.sin(startLat) * Math.cos(destLat) * Math.cos(destLng - startLng)
      let brng = Math.atan2(y, x)
      let brngDgr = this.toDegrees(brng)
      return (brngDgr + 360) % 360
    },
    flyEvent (eventType) {
      if (eventType == 'play') {
        this.flyExtent()
        remainTime = 0
      } else if (eventType == 'pause') {
        remainTime = Cesium.JulianDate.secondsDifference(
          viewer.clock.stopTime,
          viewer.clock.currentTime
        )
        usedTime += Cesium.JulianDate.secondsDifference(
          viewer.clock.currentTime,
          viewer.clock.startTime
        )
        var len = viewer.clock.onTick.numberOfListeners
        for (var i = 0; i < len; i++) {
          viewer.clock.onTick.removeEventListener(viewer.clock.onTick._listeners[i])
        }
        marksIndex = 1
        remainTime = 0
        usedTime = 0
        var len = viewer.clock.onTick.numberOfListeners
        for (var i = 0; i < len; i++) {
          viewer.clock.onTick.removeEventListener(viewer.clock.onTick._listeners[i])
        }
      } else {
        // 停止按钮
        marksIndex = 1
        remainTime = 0
        usedTime = 0
        var len = viewer.clock.onTick.numberOfListeners
        for (var i = 0; i < len; i++) {
          viewer.clock.onTick.removeEventListener(viewer.clock.onTick._listeners[i])
        }
      }
    },
    //键盘控制
    disableRefresh (evt) {
      evt = evt ? evt : window.event
      if (evt.keyCode) {
        if (evt.keyCode == 32) {
          if (remainTime > 0) {
            this.flyEvent('play')
          } else {
            this.flyEvent('pause')
          }
        }
      }
    },
    //轮船轨迹
    loadCzml (model) {
      let dronePromise = Cesium.CzmlDataSource.load(model.czml);
      let drone;
      dronePromise.then(function (dataSource) {
        viewer.dataSources.add(dataSource);
        drone = dataSource.entities.values[0];
        var vehicleEntity = dataSource.entities.getById(model.id);
        //绑定镜头
        if (model.trackedEntity) {
          window.viewer.trackedEntity = vehicleEntity
        }
        drone.orientation = new Cesium.VelocityOrientationProperty(
          drone.position
        )
        drone.position.setInterpolationOptions({
          interpolationAlgorithm: Cesium.HermitePolynomialApproximation, //插值算法
          interpolationDegree: 2,
        })
      })
    },
    //绕点旋转
    rotateHeading () {
      // console.log(lng, lat, distance);
      var position = Cesium.Cartesian3.fromDegrees(106.51485606000334, 29.800783390636703, 350);
      // 相机看点的角度，如果大于0那么则是从地底往上看，所以要为负值，这里取-30度
      var pitch = Cesium.Math.toRadians(-2);
      // 给定飞行一周所需时间，比如10s, 那么每秒转动度数
      var angle = 360 / 4.2;
      // 给定相机距离点多少距离飞行，这里取值为5000m
      var distance = 240;
      var startTime = Cesium.JulianDate.fromDate(new Date());
      var stopTime = Cesium.JulianDate.addSeconds(startTime, 6, new Cesium.JulianDate());
      viewer.clock.startTime = startTime.clone();  // 开始时间
      viewer.clock.stopTime = stopTime.clone();     // 结速时间
      viewer.clock.currentTime = startTime.clone(); // 当前时间
      viewer.clock.clockRange = Cesium.ClockRange.CLAMPED; // 行为方式
      viewer.clock.clockStep = Cesium.ClockStep.SYSTEM_CLOCK; // 时钟设置为当前系统时间; 忽略所有其他设置。
      // 相机的当前heading
      var initialHeading = viewer.camera.heading;
      exection = function TimeExecution () {
        // 当前已经过去的时间，单位s
        var delTime = Cesium.JulianDate.secondsDifference(viewer.clock.currentTime, viewer.clock.startTime);
        var heading = Cesium.Math.toRadians(delTime * angle) + initialHeading;
        viewer.scene.camera.setView({
          destination: position, // 点的坐标
          orientation: {
            heading: heading,
            pitch: pitch,
          }
        });
        viewer.scene.camera.moveBackward(distance);
        if (Cesium.JulianDate.compare(viewer.clock.currentTime, viewer.clock.stopTime) >= 0) {
          viewer.clock.onTick.removeEventListener(exection);
        }
      };
      viewer.clock.onTick.addEventListener(exection);
    },
    //扩散圆
    loadEllipse (ellipsesCanshu) {
      let ellipses = viewer.entities.add({
        position: Cesium.Cartesian3.fromDegrees(
          ellipsesCanshu.position.x,
          ellipsesCanshu.position.y,
          ellipsesCanshu.position.z
        ),
        name: "机楼",
        ellipse: {
          semiMinorAxis: ellipsesCanshu.semiMinorAxis,
          semiMajorAxis: ellipsesCanshu.semiMajorAxis,
          material: new Cesium.CircleSpiralMaterialProperty({
            color: new Cesium.Color(1, 109 / 255, 71 / 255, 0.7),
            speed: 60.0,
          })
        },
      });
      viewer.cesiumWidget.screenSpaceEventHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK);
      viewer.cesiumWidget.screenSpaceEventHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
    },
    //动态墙材质
    DynamicWallMaterialProperty () {
      let viewer = window.viewer;
      function DynamicWallMaterialProperty (options) {
        // 默认参数设置
        this._definitionChanged = new Cesium.Event();
        this._color = undefined;
        this._colorSubscription = undefined;
        this.color = options.color;
        this.duration = options.duration;
        this.trailImage = options.trailImage;
        this._time = new Date().getTime();
      }
      Object.defineProperties(DynamicWallMaterialProperty.prototype, {
        isConstant: {
          get: function () {
            return false;
          },
        },
        definitionChanged: {
          get: function () {
            return this._definitionChanged;
          },
        },
        color: Cesium.createPropertyDescriptor("color"),
      });
      DynamicWallMaterialProperty.prototype.getType = function (time) {
        return "DynamicWall";
      };
      DynamicWallMaterialProperty.prototype.getValue = function (time, result) {
        if (!Cesium.defined(result)) {
          result = {};
        }
        result.color = Cesium.Property.getValueOrClonedDefault(
          this._color,
          time,
          Cesium.Color.WHITE,
          result.color
        );
        if (this.trailImage) {
          result.image = this.trailImage;
        } else {
          result.image = Cesium.Material.DynamicWallImage;
        }

        if (this.duration) {
          result.time =
            ((new Date().getTime() - this._time) % this.duration) /
            this.duration;
        }
        viewer.scene.requestRender();
        return result;
      };
      DynamicWallMaterialProperty.prototype.equals = function (other) {
        return (
          this === other ||
          (other instanceof DynamicWallMaterialProperty &&
            Cesium.Property.equals(this._color, other._color))
        );
      };
      Cesium.DynamicWallMaterialProperty = DynamicWallMaterialProperty;
      Cesium.Material.DynamicWallType = "DynamicWall";
      Cesium.Material.DynamicWallImage = colors;
      Cesium.Material.DynamicWallSource =
        "czm_material czm_getMaterial(czm_materialInput materialInput)\n\
                                            {\n\
                                            czm_material material = czm_getDefaultMaterial(materialInput);\n\
                                            vec2 st = materialInput.st;\n\
                                            vec4 colorImage = texture2D(image, vec2(fract(st.t - time), st.t));\n\
                                            vec4 fragColor;\n\
                                            fragColor.rgb = color.rgb / 1.0;\n\
                                            fragColor = czm_gammaCorrect(fragColor);\n\
                                            material.alpha = colorImage.a * color.a;\n\
                                            material.diffuse = color.rgb;\n\
                                            material.emission = fragColor.rgb;\n\
                                            return material;\n\
                                            }";
      Cesium.Material._materialCache.addMaterial(
        Cesium.Material.DynamicWallType,
        {
          fabric: {
            type: Cesium.Material.DynamicWallType,
            uniforms: {
              color: new Cesium.Color(1.0, 1.0, 1.0, 1),
              image: Cesium.Material.DynamicWallImage,
              time: 0,
            },
            source: Cesium.Material.DynamicWallSource,
          },
          translucent: function (material) {
            return true;
          },
        }
      );
    },
    //立墙效果
    drawWall (wallcanshu) {
      this.DynamicWallMaterialProperty()
      let viewer = window.viewer;
      viewer.entities.add({
        name: "立体墙效果",
        wall: {
          positions: Cesium.Cartesian3.fromDegreesArray(wallcanshu.positions),
          // 设置高度
          maximumHeights: new Array(wallcanshu.positions.length / 2).fill(
            wallcanshu.maxheight
          ),
          minimumHeights: new Array(wallcanshu.positions.length / 2).fill(
            wallcanshu.minheight
          ),
          material: new Cesium.DynamicWallMaterialProperty({
            color: wallcanshu.color,
            duration: 3000,
          }),
          // distanceDisplayCondition: new Cesium.DistanceDisplayCondition(8000, 1200000)
        },
      });
    },
    //车叠加地形行驶
    loadCar (canshu) {
      let positions = canshu.positions
      Cesium.Math.setRandomNumberSeed(35);
      let stTime = canshu.times[0];
      let endTime = canshu.times[1];
      var start = stTime.clone();
      var stop = endTime.clone();
      viewer.clock.startTime = start.clone();
      viewer.clock.stopTime = stop.clone();
      viewer.clock.currentTime = start.clone();
      viewer.clock.clockRange = Cesium.ClockRange.LOOP_STOP; //Loop at the end
      viewer.clock.multiplier = canshu.speed;
      viewer.timeline.zoomTo(start, stop);
      // function computeCirclularFlight (lon, lat, radius) {
      //   var property = new Cesium.SampledPositionProperty();
      //   for (var i = 0; i <= 360; i += 45) {
      //     var radians = Cesium.Math.toRadians(i);
      //     var time = Cesium.JulianDate.addSeconds(start, i, new Cesium.JulianDate());
      //     var position = Cesium.Cartesian3.fromDegrees(
      //       lon + radius * 1.5 * Math.cos(radians),
      //       lat + radius * Math.sin(radians),
      //       Cesium.Math.nextRandomNumber() * 500 + 1750
      //     );
      //     property.addSample(time, position);
      //     console.log(time.toString(), " -> ", position.toString());

      //     //Also create a point for each sample we generate.
      //     viewer.entities.add({
      //       position: position,
      //       point: {
      //         pixelSize: 8,
      //         color: Cesium.Color.TRANSPARENT,
      //         outlineColor: Cesium.Color.YELLOW,
      //         outlineWidth: 3,
      //       },
      //     });
      //   }
      //   return property;
      // }
      let stPos = positions[0];
      let endPos = positions[1];

      // 采样位置的时间分辨率
      let timeOfResolution = 6;

      // using sampled property to get sampled data
      let oriSamples = new Cesium.SampledProperty(Cesium.Cartesian3);
      oriSamples.addSamples(canshu.times, positions);
      let geodesic = new Cesium.EllipsoidGeodesic(
        Cesium.Cartographic.fromCartesian(stPos),
        Cesium.Cartographic.fromCartesian(endPos)
      );
      let lenInMeters = Math.ceil(geodesic.surfaceDistance); // avoid overflow when take samples
      let samplesNum = Math.floor(
        Cesium.JulianDate.secondsDifference(endTime, stTime) / timeOfResolution
      );
      let sampledPositions = [];
      let sampledTimes = [];
      for (let i = 0; i < samplesNum + 1; i++) {
        let sampleTime = Cesium.JulianDate.addSeconds(
          stTime,
          i * timeOfResolution,
          new Cesium.JulianDate()
        );
        let tmpPos = oriSamples.getValue(sampleTime);
        sampledPositions.push(Cesium.Cartographic.fromCartesian(tmpPos));
        sampledTimes.push(sampleTime);
      }

      let promise = Cesium.sampleTerrainMostDetailed(
        viewer.terrainProvider,
        sampledPositions
      ).then(() => {
        let carPositionProperty = new Cesium.SampledPositionProperty();

        // add positions which are clamped to ground to the carPositionProperty
        for (let i = 0; i < samplesNum + 1; i++) {
          carPositionProperty.addSample(
            sampledTimes[i],
            // new Cesium.Cartesian3.fromDegrees( // this way of changing pos is not right, all should be under WGS84
            // sampledPositions[i].longitude,
            // sampledPositions[i].latitude,
            // sampledPositions[i].height));
            Cesium.Ellipsoid.WGS84.cartographicToCartesian(sampledPositions[i])
          );
          // console.log(sampledTimes[i], " ------->>> ", sampledPositions[i]);
        }

        // 经夹地数据计算后，动态显示路径
        // let isConstant = false;
        // let curSegmentNo = 0; // 折线被划分为samplesNum的段
        // let lastSegementNo = -1;
        // let p2 = Cesium.Ellipsoid.WGS84.cartographicToCartesian(sampledPositions[1]);
        // let curPolyline = [stPos];
        // viewer.entities.add({
        //   polyline: {
        //     // 这个回调函数更新每一帧的位置。
        //     // Ellipsoid.WGS84.cartographicArrayToCartesianArray(sampledPositions),
        //     positions: new Cesium.CallbackProperty(function (time, result) {
        //       //console.log("len: ", lenInMeters, "samplesNum", samplesNum, "timeOfResolution", timeOfResolution);

        //       //let st
        //       curSegmentNo = Math.floor(
        //         Cesium.JulianDate.secondsDifference(time, stTime) / timeOfResolution
        //       );
        //       console.log(curSegmentNo);
        //       if (curSegmentNo !== lastSegementNo) {
        //         //console.log("curSegmentNo is: ", curSegmentNo.toString(), "\ncurTime: ", time.toString(), "\nstTime: ", stTime.toString());
        //         // tmmP => curPolyine[lastSegementNo+1 : CurSegmentNo]
        //         let tmpP = Cesium.Ellipsoid.WGS84.cartographicToCartesian(
        //           sampledPositions[curSegmentNo]
        //         );
        //         //console.log("adding new points: ", tmpP.toString(), "\nsize is:", curPolyline.length);
        //         curPolyline.push(tmpP);
        //         lastSegementNo = 30000;
        //       }
        //       // if reach the end of sampled positions, clear the polyline's positions
        //       if (curSegmentNo === samplesNum - 1) {
        //         curSegmentNo = 0;
        //         curPolyline = [];
        //         console.log("cleared!");
        //       }

        //       return curPolyline;
        //     }, isConstant),
        //     //clampToGround: true,
        //     width: 5,
        //     material: Cesium.Color.RED,
        //     availability: new Cesium.TimeIntervalCollection([
        //       new Cesium.TimeInterval({
        //         start: stTime,
        //         stop: endTime,
        //       }),
        //     ]),
        //   },
        // });
        var position = carPositionProperty;
        //Actually create the entity
        var entity = viewer.entities.add({
          //Set the entity availability to the same interval as the simulation time.
          availability: new Cesium.TimeIntervalCollection([
            new Cesium.TimeInterval({
              start: start,
              stop: stop,
            }),
          ]),

          //Use our computed positions
          position: position,

          //Automatically compute orientation based on position movement.
          orientation: new Cesium.VelocityOrientationProperty(position),

          //Load the Cesium plane model to represent the entity
          model: {
            uri: canshu.car,
            minimumPixelSize: 32,
            maximumScale: 32,
          },
          //Show the path as a pink line sampled in 1 second increments.
          // path: {
          //   resolution: 1,
          //   material: new Cesium.PolylineGlowMaterialProperty({
          //     glowPower: 0.1,
          //     color: Cesium.Color.YELLOW,
          //   }),
          //   width: 1,
          // },
        });

        //Add button to view the path from the top down
        Sandcastle.addDefaultToolbarButton("View Top Down", function () {
          viewer.trackedEntity = undefined;
          viewer.zoomTo(
            viewer.entities,
            new Cesium.HeadingPitchRange(0, Cesium.Math.toRadians(-90))
          );
        });

        //Add button to view the path from the side
        Sandcastle.addToolbarButton("View Side", function () {
          viewer.trackedEntity = undefined;
          viewer.zoomTo(
            viewer.entities,
            new Cesium.HeadingPitchRange(
              Cesium.Math.toRadians(-90),
              Cesium.Math.toRadians(-15),
              7500
            )
          );
        });

        //Add button to track the entity as it moves
        Sandcastle.addToolbarButton("View Aircraft", function () {
          // viewer.trackedEntity = entity;
          viewer.zoomTo(
            viewer.entities,
            new Cesium.HeadingPitchRange(
              Cesium.Math.toRadians(15),
              Cesium.Math.toRadians(-45),
              2500
            )
          );
        });

        //Add a combo box for selecting each interpolation mode.
        Sandcastle.addToolbarMenu(
          [
            {
              text: "Interpolation: Linear Approximation",
              onselect: function () {
                entity.position.setInterpolationOptions({
                  interpolationDegree: 1,
                  interpolationAlgorithm: Cesium.LinearApproximation,
                });
              },
            },
            {
              text: "Interpolation: Lagrange Polynomial Approximation",
              onselect: function () {
                entity.position.setInterpolationOptions({
                  interpolationDegree: 5,
                  interpolationAlgorithm: Cesium.LagrangePolynomialApproximation,
                });
              },
            },
            {
              text: "Interpolation: Hermite Polynomial Approximation",
              onselect: function () {
                entity.position.setInterpolationOptions({
                  interpolationDegree: 2,
                  interpolationAlgorithm: Cesium.HermitePolynomialApproximation,
                });
              },
            },
          ],
          "interpolationMenu"
        );
      });
    },
    //连线shader
    materialLine () {
      if (typeof Cesium !== "undefined")
        (function (_0x28e6fb) {
          var _0x3a4a3c = {
            iKSVw: "1|4|0|5|3|2",
            loCcz: function (_0x8baaf6, _0x53c135) {
              return _0x8baaf6(_0x53c135);
            },
            ZVLvT: function (_0x5a6faa, _0x35dbb2) {
              return _0x5a6faa / _0x35dbb2;
            },
            OOtAe: function (_0x5ac73d, _0x1d0c26) {
              return _0x5ac73d * _0x1d0c26;
            },
            YUfBq: function (_0x150977, _0x34f301) {
              return _0x150977 - _0x34f301;
            },
            AAzBp: function (_0x4456b6, _0x489fd6) {
              return _0x4456b6 === _0x489fd6;
            },
            zWdDc: "0|1|5|4|6|2|3",
            phCaN: function (_0x22f7fe, _0x22460b, _0x584f20) {
              return _0x22f7fe(_0x22460b, _0x584f20);
            },
            xUWjD: function (_0x2d653f, _0x3fc619) {
              return _0x2d653f === _0x3fc619;
            },
            iTcol: function (_0x4dea27, _0x5c701b) {
              return _0x4dea27 instanceof _0x5c701b;
            },
            YLCPu: function (_0xbac2e7, _0x417f56) {
              return _0xbac2e7 + _0x417f56;
            },
            kDWEL: "wallType",
            jikaj: function (_0x37f580, _0xb90f4c) {
              return _0x37f580 * _0xb90f4c;
            },
            uvOSD: "color",
            foFmO:
              "czm_material\x20czm_getMaterial(czm_materialInput\x20materialInput)\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20czm_material\x20material\x20=\x20czm_getDefaultMaterial(materialInput);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20vec2\x20st\x20=\x20materialInput.st;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20if(texture2D(image,\x20vec2(0.0,\x200.0)).a\x20==\x201.0){\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20discard;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20}else{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20material.alpha\x20=\x20texture2D(image,\x20vec2(1.0\x20-\x20fract(time\x20-\x20st.s),\x20st.t)).a\x20*\x20color.a;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20material.diffuse\x20=\x20max(color.rgb\x20*\x20material.alpha\x20*\x203.0,\x20color.rgb);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20return\x20material;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20",
            aQpuV:
              "background:\x20#606060;\x20color:\x20#fff;\x20border-radius:\x203px\x200\x200\x203px;",
            XOzEg:
              "background:\x20#1475B2;\x20color:\x20#fff;\x20border-radius:\x200\x203px\x203px\x200;",
          };

          function _0x265ba3 (_0x4f3655) {
            var _0x5f1c70 = {
              KioEk: _0x3a4a3c["zWdDc"],
              mPDit: function (_0x1b1c92, _0x330c7a, _0x2b6846) {
                return _0x3a4a3c["phCaN"](_0x1b1c92, _0x330c7a, _0x2b6846);
              },
              zvyOY: function (_0x337a2c, _0x58ba2d) {
                return _0x3a4a3c["xUWjD"](_0x337a2c, _0x58ba2d);
              },
              UBCpg: function (_0x51b947, _0x1cee24) {
                return _0x3a4a3c["iTcol"](_0x51b947, _0x1cee24);
              },
            };
            var _0x578460 = _0x28e6fb["Color"],
              _0x18e133 = _0x28e6fb["defaultValue"],
              _0x4e36fd = _0x28e6fb["defined"],
              _0x285486 = Object["defineProperties"],
              _0xd32598 = _0x28e6fb["Event"],
              _0x35590 = _0x28e6fb["createPropertyDescriptor"],
              _0x1479bd = _0x28e6fb["Property"],
              _0x5236ab = _0x28e6fb["Material"],
              _0x26ead8 = _0x578460["WHITE"],
              _0xc22a2e =
                _0x4f3655["MaterialType"] ||
                _0x3a4a3c["YLCPu"](
                  _0x3a4a3c["kDWEL"],
                  _0x3a4a3c["loCcz"](
                    parseInt,
                    _0x3a4a3c["jikaj"](Math["random"](), 0x3e8)
                  )
                );

            function _0x467c57 (_0x22dfb5) {
              var _0x101c3d = _0x5f1c70["KioEk"]["split"]("|"),
                _0x53d876 = 0x0;
              while (!![]) {
                switch (_0x101c3d[_0x53d876++]) {
                  case "0":
                    _0x22dfb5 = _0x5f1c70["mPDit"](
                      _0x18e133,
                      _0x22dfb5,
                      _0x18e133["EMPTY_OBJECT"]
                    );
                    continue;
                  case "1":
                    this["_definitionChanged"] = new _0xd32598();
                    continue;
                  case "2":
                    this["duration"] = _0x22dfb5["duration"] || 0x3e8;
                    continue;
                  case "3":
                    this["_time"] = undefined;
                    continue;
                  case "4":
                    this["_colorSubscription"] = undefined;
                    continue;
                  case "5":
                    this["_color"] = undefined;
                    continue;
                  case "6":
                    this["color"] =
                      _0x22dfb5["color"] || _0x28e6fb["Color"]["BLUE"];
                    continue;
                }
                break;
              }
            }
            _0x3a4a3c["phCaN"](_0x285486, _0x467c57["prototype"], {
              isvarant: {
                get: function () {
                  return ![];
                },
              },
              definitionChanged: {
                get: function () {
                  return this["_definitionChanged"];
                },
              },
              color: _0x3a4a3c["loCcz"](_0x35590, _0x3a4a3c["uvOSD"]),
            });
            _0x467c57["prototype"]["getType"] = function (_0x17bed1) {
              return _0xc22a2e;
            };
            _0x467c57["prototype"]["getValue"] = function (
              _0x52a446,
              _0x2c759a
            ) {
              var _0x57bbe9 = _0x3a4a3c["iKSVw"]["split"]("|"),
                _0x2a5c25 = 0x0;
              while (!![]) {
                switch (_0x57bbe9[_0x2a5c25++]) {
                  case "0":
                    _0x2c759a["image"] = _0x4f3655["image"];
                    continue;
                  case "1":
                    if (!_0x3a4a3c["loCcz"](_0x4e36fd, _0x2c759a)) {
                      _0x2c759a = {};
                    }
                    continue;
                  case "2":
                    return _0x2c759a;
                  case "3":
                    _0x2c759a["time"] = _0x3a4a3c["ZVLvT"](
                      _0x3a4a3c["OOtAe"](
                        _0x3a4a3c["YUfBq"](
                          _0x52a446["secondsOfDay"],
                          this["_time"]
                        ),
                        0x3e8
                      ),
                      this["duration"]
                    );
                    continue;
                  case "4":
                    _0x2c759a["color"] = _0x1479bd["getValueOrClonedDefault"](
                      this["_color"],
                      _0x52a446,
                      _0x26ead8,
                      _0x2c759a["color"]
                    );
                    continue;
                  case "5":
                    if (_0x3a4a3c["AAzBp"](this["_time"], undefined)) {
                      this["_time"] = _0x52a446["secondsOfDay"];
                    }
                    continue;
                }
                break;
              }
            };
            _0x467c57["prototype"]["equals"] = function (_0x47fd34) {
              return (
                _0x5f1c70["zvyOY"](this, _0x47fd34) ||
                (_0x5f1c70["UBCpg"](_0x47fd34, _0x467c57) &&
                  _0x1479bd["equals"](this["_color"], _0x47fd34["_color"]))
              );
            };
            _0x5236ab["_materialCache"]["addMaterial"](_0xc22a2e, {
              fabric: {
                type: _0xc22a2e,
                uniforms: {
                  color:
                    _0x4f3655["color"] ||
                    new _0x28e6fb["Color"](0x1, 0x0, 0x0, 0.5),
                  image: _0x4f3655["image"],
                  time: _0x4f3655["color"]["time"] || 0x0,
                },
                source: _0x3a4a3c["foFmO"],
              },
              translucent: function (_0x5d5e53) {
                return !![];
              },
            });
            return new _0x467c57(_0x4f3655);
          }
          _0x28e6fb["CustomMaterialLine"] = _0x265ba3;
          let _0x552062 = _0x3a4a3c["aQpuV"];
          let _0x5de97c = _0x3a4a3c["XOzEg"];
        })(Cesium);
    },
    //随机颜色方法
    randomColor () {
      let col = "#";
      for (let i = 0; i < 6; i++)
        col += parseInt(Math.random() * 16).toString(16);
      return col;
    },
    //统计图表
    pieCharts (yb, wb) {
      let myChart = echarts.init(document.getElementById("echarstb"));
      let option;
      option = {
        tooltip: {
          trigger: "item",
        },
        color: ["#F14A5E", "#1FE580"],
        legend: {
          orient: "vertical",
          left: "left",
          textStyle: {
            //图例文字的样式
            color: "#000",
            fontSize: 16,
          },
        },
        series: [
          {
            name: "办理业务量",
            type: "pie",
            radius: "70%",
            data: [
              {
                value: yb,
                name: "已办理",
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      {
                        offset: 0,
                        color: "rgba(241, 74, 94, 0.9)",
                      },
                      {
                        offset: 1,
                        color: "rgba(241, 74, 94, 0.9)",
                      },
                    ]),
                  },
                },
              },
              {
                value: wb,
                name: "未办理",
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      {
                        offset: 0,
                        color: "rgba(31, 230, 128, 0.9)",
                      },
                      {
                        offset: 1,
                        color: "rgba(31, 230, 128, 0.9)",
                      },
                    ]),
                  },
                },
              },
            ],
            label: {
              normal: {
                position: "inner",
                show: true,
                formatter: `{c}户({d}%)`, //自定义显示格式(b:name, c:value, d:百分比)
                textStyle: {
                  color: "#fff",
                  fontSize: 10,
                },
              },
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 15,
                shadowColor: "rgba(0, 0, 0, 0.5)",
              },
            },
          },
        ],
      };
      option && myChart.setOption(option);
    },
    wkaifa () {
      this.$message.error("功能待开发！");
    },
  },
};
</script>

<style>
.cesium-widget-credits {
  /* 去除logo */
  display: none !important;
}

body {
  margin: 0;
  padding: 0;
}

#box {
  width: 100%;
  height: 100%;
}

#search {
  position: absolute !important;
  top: 20px;
  left: 20px;
}

.cxBtn {
  background-color: #409eff !important;
  color: #fff !important;
  border-radius: 0 !important;
  border: 1px solid #409eff !important;
}

.is-horizontal {
  display: none !important;
}

.el-scrollbar__wrap {
  margin-bottom: -17px !important;
}

.buttom_lng {
  position: absolute;
  right: 10px;
  bottom: 0px;
}

.navigation-controls {
  background-color: rgba(68, 67, 67, 0.473);
  bottom: 170px !important;
  right: 35px !important;
}

.compass {
  bottom: 250px !important;
  right: 2px !important;
}

.distance-legend {
  bottom: 7px !important;
  right: 470px !important;
}

.distance-legend-label {
  color: #fff !important;
}

.distance-legend-scale-bar {
  border-left: 1px solid #fff !important;
  border-right: 1px solid #fff !important;
  border-bottom: 1px solid #fff !important;
}

.map-operate {
  position: absolute;
  bottom: 50px;
  right: 5px;
}

.one {
  position: absolute;
}

.one_1 {
  transform: rotate(45deg);
  width: 40px;
  height: 40px;
  position: relative;
  background-color: #fff;
  left: -20px;
  /* top: 280px; */
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

/* .box-card {
  width: 400px;
} */

.jkxq .has-gutter {
  display: none;
}

.el-table__cell {
  padding: 5px 0 !important;
}

.clears {
  padding: 12px 0;
}

.el-divider {
  margin: 5px 0 !important;
}

#echarstb {
  width: 380px;
  height: 230px;
  text-align: center;
}

.updataBtns {
  padding: 15px 0 0 0;
}

html,
body {
  overflow: hidden;
}

.el-button--primary {
  padding: 13px 20px !important;
  border: none !important;
}

#charts {
  position: absolute;
  width: 230px;
  height: 320px;
  bottom: 0px;
  left: 0px;
  background-image: url("../assets/chart.png");
  background-size: cover;
}

.el-input__inner {
  border-radius: 0 !important;
  width: 350px !important;
}

#citySelect {
  position: absolute;
  top: 20px;
  right: 20px;
}

.el-select .el-input__inner {
  width: 90px !important;
}

.tksn {
  float: right;
  cursor: pointer;
  width: 32px;
  height: 32px;
}

.setClassName {
  left: unset !important;
  top: 20px !important;
  right: 120px !important;
}

.clearfix > span {
  display: inline-block;
  font-weight: 700;
  font-size: 18px;
}

.fnSwitch {
  position: absolute;
  top: 20px;
  left: 460px;
  height: 40px;
  width: 150px;
  text-align: center;
  background-color: rgba(250, 250, 250, 0.884);
}

.el-switch {
  margin-top: 10px;
}
.tianqis {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: rgba(255, 255, 255, 0.699);
  border-radius: 5px;
  padding: 5px 10px;
}
.home {
  position: absolute;
  right: 10px;
  top: 118px;
  bottom: 0px;
  width: 30px;
  height: 30px;
  background: url(../assets/home-color.png);
  background-size: 100% 100%;
  border-radius: 7px;
  z-index: 99;
  cursor: pointer;
}
.cesium-performanceDisplay-defaultContainer {
  top: 156px !important;
}
</style>
