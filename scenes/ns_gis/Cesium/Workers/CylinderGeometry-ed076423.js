/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.99
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["exports","./Transforms-3ea76111","./Matrix2-7a2bab7e","./Matrix3-edb29a7e","./ComponentDatatype-e86a9f87","./CylinderGeometryLibrary-29e6b863","./defaultValue-135942ca","./GeometryAttribute-dacddb3f","./GeometryAttributes-899f8bd0","./GeometryOffsetAttribute-d3a42805","./IndexDatatype-3a8ea78f","./Math-a304e2d6","./VertexFormat-7d5b4d7e"],(function(t,e,a,n,o,r,i,s,u,m,l,d,c){"use strict";const f=new a.Cartesian2,p=new n.Cartesian3,y=new n.Cartesian3,b=new n.Cartesian3,A=new n.Cartesian3;function x(t){const e=(t=i.defaultValue(t,i.defaultValue.EMPTY_OBJECT)).length,a=t.topRadius,n=t.bottomRadius,o=i.defaultValue(t.vertexFormat,c.VertexFormat.DEFAULT),r=i.defaultValue(t.slices,128);this._length=e,this._topRadius=a,this._bottomRadius=n,this._vertexFormat=c.VertexFormat.clone(o),this._slices=r,this._offsetAttribute=t.offsetAttribute,this._workerName="createCylinderGeometry"}x.packedLength=c.VertexFormat.packedLength+5,x.pack=function(t,e,a){return a=i.defaultValue(a,0),c.VertexFormat.pack(t._vertexFormat,e,a),a+=c.VertexFormat.packedLength,e[a++]=t._length,e[a++]=t._topRadius,e[a++]=t._bottomRadius,e[a++]=t._slices,e[a]=i.defaultValue(t._offsetAttribute,-1),e};const g=new c.VertexFormat,_={vertexFormat:g,length:void 0,topRadius:void 0,bottomRadius:void 0,slices:void 0,offsetAttribute:void 0};let h;x.unpack=function(t,e,a){e=i.defaultValue(e,0);const n=c.VertexFormat.unpack(t,e,g);e+=c.VertexFormat.packedLength;const o=t[e++],r=t[e++],s=t[e++],u=t[e++],m=t[e];return i.defined(a)?(a._vertexFormat=c.VertexFormat.clone(n,a._vertexFormat),a._length=o,a._topRadius=r,a._bottomRadius=s,a._slices=u,a._offsetAttribute=-1===m?void 0:m,a):(_.length=o,_.topRadius=r,_.bottomRadius=s,_.slices=u,_.offsetAttribute=-1===m?void 0:m,new x(_))},x.createGeometry=function(t){let c=t._length;const x=t._topRadius,g=t._bottomRadius,_=t._vertexFormat,h=t._slices;if(c<=0||x<0||g<0||0===x&&0===g)return;const F=h+h,v=h+F,C=F+F,w=r.CylinderGeometryLibrary.computePositions(c,x,g,h,!0),G=_.st?new Float32Array(2*C):void 0,V=_.normal?new Float32Array(3*C):void 0,D=_.tangent?new Float32Array(3*C):void 0,R=_.bitangent?new Float32Array(3*C):void 0;let T;const O=_.normal||_.tangent||_.bitangent;if(O){const t=_.tangent||_.bitangent;let e=0,a=0,o=0;const r=Math.atan2(g-x,c),i=p;i.z=Math.sin(r);const s=Math.cos(r);let u=b,m=y;for(T=0;T<h;T++){const r=T/h*d.CesiumMath.TWO_PI,l=s*Math.cos(r),c=s*Math.sin(r);O&&(i.x=l,i.y=c,t&&(u=n.Cartesian3.normalize(n.Cartesian3.cross(n.Cartesian3.UNIT_Z,i,u),u)),_.normal&&(V[e++]=i.x,V[e++]=i.y,V[e++]=i.z,V[e++]=i.x,V[e++]=i.y,V[e++]=i.z),_.tangent&&(D[a++]=u.x,D[a++]=u.y,D[a++]=u.z,D[a++]=u.x,D[a++]=u.y,D[a++]=u.z),_.bitangent&&(m=n.Cartesian3.normalize(n.Cartesian3.cross(i,u,m),m),R[o++]=m.x,R[o++]=m.y,R[o++]=m.z,R[o++]=m.x,R[o++]=m.y,R[o++]=m.z))}for(T=0;T<h;T++)_.normal&&(V[e++]=0,V[e++]=0,V[e++]=-1),_.tangent&&(D[a++]=1,D[a++]=0,D[a++]=0),_.bitangent&&(R[o++]=0,R[o++]=-1,R[o++]=0);for(T=0;T<h;T++)_.normal&&(V[e++]=0,V[e++]=0,V[e++]=1),_.tangent&&(D[a++]=1,D[a++]=0,D[a++]=0),_.bitangent&&(R[o++]=0,R[o++]=1,R[o++]=0)}const L=12*h-12,M=l.IndexDatatype.createTypedArray(C,L);let P=0,k=0;for(T=0;T<h-1;T++)M[P++]=k,M[P++]=k+2,M[P++]=k+3,M[P++]=k,M[P++]=k+3,M[P++]=k+1,k+=2;for(M[P++]=F-2,M[P++]=0,M[P++]=1,M[P++]=F-2,M[P++]=1,M[P++]=F-1,T=1;T<h-1;T++)M[P++]=F+T+1,M[P++]=F+T,M[P++]=F;for(T=1;T<h-1;T++)M[P++]=v,M[P++]=v+T,M[P++]=v+T+1;let z=0;if(_.st){const t=Math.max(x,g);for(T=0;T<C;T++){const e=n.Cartesian3.fromArray(w,3*T,A);G[z++]=(e.x+t)/(2*t),G[z++]=(e.y+t)/(2*t)}}const E=new u.GeometryAttributes;_.position&&(E.position=new s.GeometryAttribute({componentDatatype:o.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:w})),_.normal&&(E.normal=new s.GeometryAttribute({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:V})),_.tangent&&(E.tangent=new s.GeometryAttribute({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:D})),_.bitangent&&(E.bitangent=new s.GeometryAttribute({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:R})),_.st&&(E.st=new s.GeometryAttribute({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:G})),f.x=.5*c,f.y=Math.max(g,x);const N=new e.BoundingSphere(n.Cartesian3.ZERO,a.Cartesian2.magnitude(f));if(i.defined(t._offsetAttribute)){c=w.length;const e=t._offsetAttribute===m.GeometryOffsetAttribute.NONE?0:1,a=new Uint8Array(c/3).fill(e);E.applyOffset=new s.GeometryAttribute({componentDatatype:o.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:a})}return new s.Geometry({attributes:E,indices:M,primitiveType:s.PrimitiveType.TRIANGLES,boundingSphere:N,offsetAttribute:t._offsetAttribute})},x.getUnitCylinder=function(){return i.defined(h)||(h=x.createGeometry(new x({topRadius:1,bottomRadius:1,length:1,vertexFormat:c.VertexFormat.POSITION_ONLY}))),h},t.CylinderGeometry=x}));
