/*! pako 2.0.4 https://github.com/nodeca/pako @license (MIT AND Zlib) */(function(y,z){typeof exports=="object"&&typeof module<"u"?z(exports):typeof define=="function"&&define.amd?define(["exports"],z):z((y=typeof globalThis<"u"?globalThis:y||self).pako={})})(this,function(y){"use strict";function z(e){let n=e.length;for(;--n>=0;)e[n]=0}const he=256,ze=286,H=30,M=15,oe=new Uint8Array([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0]),$=new Uint8Array([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13]),ea=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7]),ke=new Uint8Array([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),E=new Array(576);z(E);const P=new Array(60);z(P);const j=new Array(512);z(j);const K=new Array(256);z(K);const de=new Array(29);z(de);const ee=new Array(H);function ue(e,n,r,i,a){this.static_tree=e,this.extra_bits=n,this.extra_base=r,this.elems=i,this.max_length=a,this.has_stree=e&&e.length}let xe,Ae,Ee;function fe(e,n){this.dyn_tree=e,this.max_code=0,this.stat_desc=n}z(ee);const Ze=e=>e<256?j[e]:j[256+(e>>>7)],Y=(e,n)=>{e.pending_buf[e.pending++]=255&n,e.pending_buf[e.pending++]=n>>>8&255},w=(e,n,r)=>{e.bi_valid>16-r?(e.bi_buf|=n<<e.bi_valid&65535,Y(e,e.bi_buf),e.bi_buf=n>>16-e.bi_valid,e.bi_valid+=r-16):(e.bi_buf|=n<<e.bi_valid&65535,e.bi_valid+=r)},k=(e,n,r)=>{w(e,r[2*n],r[2*n+1])},Re=(e,n)=>{let r=0;do r|=1&e,e>>>=1,r<<=1;while(--n>0);return r>>>1},Ue=(e,n,r)=>{const i=new Array(16);let a,_,t=0;for(a=1;a<=M;a++)i[a]=t=t+r[a-1]<<1;for(_=0;_<=n;_++){let s=e[2*_+1];s!==0&&(e[2*_]=Re(i[s]++,s))}},Se=e=>{let n;for(n=0;n<ze;n++)e.dyn_ltree[2*n]=0;for(n=0;n<H;n++)e.dyn_dtree[2*n]=0;for(n=0;n<19;n++)e.bl_tree[2*n]=0;e.dyn_ltree[512]=1,e.opt_len=e.static_len=0,e.last_lit=e.matches=0},Te=e=>{e.bi_valid>8?Y(e,e.bi_buf):e.bi_valid>0&&(e.pending_buf[e.pending++]=e.bi_buf),e.bi_buf=0,e.bi_valid=0},Le=(e,n,r,i)=>{const a=2*n,_=2*r;return e[a]<e[_]||e[a]===e[_]&&i[n]<=i[r]},ce=(e,n,r)=>{const i=e.heap[r];let a=r<<1;for(;a<=e.heap_len&&(a<e.heap_len&&Le(n,e.heap[a+1],e.heap[a],e.depth)&&a++,!Le(n,i,e.heap[a],e.depth));)e.heap[r]=e.heap[a],r=a,a<<=1;e.heap[r]=i},Fe=(e,n,r)=>{let i,a,_,t,s=0;if(e.last_lit!==0)do i=e.pending_buf[e.d_buf+2*s]<<8|e.pending_buf[e.d_buf+2*s+1],a=e.pending_buf[e.l_buf+s],s++,i===0?k(e,a,n):(_=K[a],k(e,_+he+1,n),t=oe[_],t!==0&&(a-=de[_],w(e,a,t)),i--,_=Ze(i),k(e,_,r),t=$[_],t!==0&&(i-=ee[_],w(e,i,t)));while(s<e.last_lit);k(e,256,n)},pe=(e,n)=>{const r=n.dyn_tree,i=n.stat_desc.static_tree,a=n.stat_desc.has_stree,_=n.stat_desc.elems;let t,s,h,l=-1;for(e.heap_len=0,e.heap_max=573,t=0;t<_;t++)r[2*t]!==0?(e.heap[++e.heap_len]=l=t,e.depth[t]=0):r[2*t+1]=0;for(;e.heap_len<2;)h=e.heap[++e.heap_len]=l<2?++l:0,r[2*h]=1,e.depth[h]=0,e.opt_len--,a&&(e.static_len-=i[2*h+1]);for(n.max_code=l,t=e.heap_len>>1;t>=1;t--)ce(e,r,t);h=_;do t=e.heap[1],e.heap[1]=e.heap[e.heap_len--],ce(e,r,1),s=e.heap[1],e.heap[--e.heap_max]=t,e.heap[--e.heap_max]=s,r[2*h]=r[2*t]+r[2*s],e.depth[h]=(e.depth[t]>=e.depth[s]?e.depth[t]:e.depth[s])+1,r[2*t+1]=r[2*s+1]=h,e.heap[1]=h++,ce(e,r,1);while(e.heap_len>=2);e.heap[--e.heap_max]=e.heap[1],((o,u)=>{const g=u.dyn_tree,c=u.max_code,te=u.stat_desc.static_tree,La=u.stat_desc.has_stree,Fa=u.stat_desc.extra_bits,$e=u.stat_desc.extra_base,Q=u.stat_desc.max_length;let V,v,B,f,_e,ye,le=0;for(f=0;f<=M;f++)o.bl_count[f]=0;for(g[2*o.heap[o.heap_max]+1]=0,V=o.heap_max+1;V<573;V++)v=o.heap[V],f=g[2*g[2*v+1]+1]+1,f>Q&&(f=Q,le++),g[2*v+1]=f,v>c||(o.bl_count[f]++,_e=0,v>=$e&&(_e=Fa[v-$e]),ye=g[2*v],o.opt_len+=ye*(f+_e),La&&(o.static_len+=ye*(te[2*v+1]+_e)));if(le!==0){do{for(f=Q-1;o.bl_count[f]===0;)f--;o.bl_count[f]--,o.bl_count[f+1]+=2,o.bl_count[Q]--,le-=2}while(le>0);for(f=Q;f!==0;f--)for(v=o.bl_count[f];v!==0;)B=o.heap[--V],B>c||(g[2*B+1]!==f&&(o.opt_len+=(f-g[2*B+1])*g[2*B],g[2*B+1]=f),v--)}})(e,n),Ue(r,l,e.bl_count)},Oe=(e,n,r)=>{let i,a,_=-1,t=n[1],s=0,h=7,l=4;for(t===0&&(h=138,l=3),n[2*(r+1)+1]=65535,i=0;i<=r;i++)a=t,t=n[2*(i+1)+1],++s<h&&a===t||(s<l?e.bl_tree[2*a]+=s:a!==0?(a!==_&&e.bl_tree[2*a]++,e.bl_tree[32]++):s<=10?e.bl_tree[34]++:e.bl_tree[36]++,s=0,_=a,t===0?(h=138,l=3):a===t?(h=6,l=3):(h=7,l=4))},De=(e,n,r)=>{let i,a,_=-1,t=n[1],s=0,h=7,l=4;for(t===0&&(h=138,l=3),i=0;i<=r;i++)if(a=t,t=n[2*(i+1)+1],!(++s<h&&a===t)){if(s<l)do k(e,a,e.bl_tree);while(--s!=0);else a!==0?(a!==_&&(k(e,a,e.bl_tree),s--),k(e,16,e.bl_tree),w(e,s-3,2)):s<=10?(k(e,17,e.bl_tree),w(e,s-3,3)):(k(e,18,e.bl_tree),w(e,s-11,7));s=0,_=a,t===0?(h=138,l=3):a===t?(h=6,l=3):(h=7,l=4)}};let Ne=!1;const Ie=(e,n,r,i)=>{w(e,0+(i?1:0),3),((a,_,t,s)=>{Te(a),s&&(Y(a,t),Y(a,~t)),a.pending_buf.set(a.window.subarray(_,_+t),a.pending),a.pending+=t})(e,n,r,!0)};var aa={_tr_init:e=>{Ne||((()=>{let n,r,i,a,_;const t=new Array(16);for(i=0,a=0;a<28;a++)for(de[a]=i,n=0;n<1<<oe[a];n++)K[i++]=a;for(K[i-1]=a,_=0,a=0;a<16;a++)for(ee[a]=_,n=0;n<1<<$[a];n++)j[_++]=a;for(_>>=7;a<H;a++)for(ee[a]=_<<7,n=0;n<1<<$[a]-7;n++)j[256+_++]=a;for(r=0;r<=M;r++)t[r]=0;for(n=0;n<=143;)E[2*n+1]=8,n++,t[8]++;for(;n<=255;)E[2*n+1]=9,n++,t[9]++;for(;n<=279;)E[2*n+1]=7,n++,t[7]++;for(;n<=287;)E[2*n+1]=8,n++,t[8]++;for(Ue(E,287,t),n=0;n<H;n++)P[2*n+1]=5,P[2*n]=Re(n,5);xe=new ue(E,oe,257,ze,M),Ae=new ue(P,$,0,H,M),Ee=new ue(new Array(0),ea,0,19,7)})(),Ne=!0),e.l_desc=new fe(e.dyn_ltree,xe),e.d_desc=new fe(e.dyn_dtree,Ae),e.bl_desc=new fe(e.bl_tree,Ee),e.bi_buf=0,e.bi_valid=0,Se(e)},_tr_stored_block:Ie,_tr_flush_block:(e,n,r,i)=>{let a,_,t=0;e.level>0?(e.strm.data_type===2&&(e.strm.data_type=(s=>{let h,l=4093624447;for(h=0;h<=31;h++,l>>>=1)if(1&l&&s.dyn_ltree[2*h]!==0)return 0;if(s.dyn_ltree[18]!==0||s.dyn_ltree[20]!==0||s.dyn_ltree[26]!==0)return 1;for(h=32;h<he;h++)if(s.dyn_ltree[2*h]!==0)return 1;return 0})(e)),pe(e,e.l_desc),pe(e,e.d_desc),t=(s=>{let h;for(Oe(s,s.dyn_ltree,s.l_desc.max_code),Oe(s,s.dyn_dtree,s.d_desc.max_code),pe(s,s.bl_desc),h=18;h>=3&&s.bl_tree[2*ke[h]+1]===0;h--);return s.opt_len+=3*(h+1)+5+5+4,h})(e),a=e.opt_len+3+7>>>3,_=e.static_len+3+7>>>3,_<=a&&(a=_)):a=_=r+5,r+4<=a&&n!==-1?Ie(e,n,r,i):e.strategy===4||_===a?(w(e,2+(i?1:0),3),Fe(e,E,P)):(w(e,4+(i?1:0),3),((s,h,l,o)=>{let u;for(w(s,h-257,5),w(s,l-1,5),w(s,o-4,4),u=0;u<o;u++)w(s,s.bl_tree[2*ke[u]+1],3);De(s,s.dyn_ltree,h-1),De(s,s.dyn_dtree,l-1)})(e,e.l_desc.max_code+1,e.d_desc.max_code+1,t+1),Fe(e,e.dyn_ltree,e.dyn_dtree)),Se(e),i&&Te(e)},_tr_tally:(e,n,r)=>(e.pending_buf[e.d_buf+2*e.last_lit]=n>>>8&255,e.pending_buf[e.d_buf+2*e.last_lit+1]=255&n,e.pending_buf[e.l_buf+e.last_lit]=255&r,e.last_lit++,n===0?e.dyn_ltree[2*r]++:(e.matches++,n--,e.dyn_ltree[2*(K[r]+he+1)]++,e.dyn_dtree[2*Ze(n)]++),e.last_lit===e.lit_bufsize-1),_tr_align:e=>{w(e,2,3),k(e,256,E),(n=>{n.bi_valid===16?(Y(n,n.bi_buf),n.bi_buf=0,n.bi_valid=0):n.bi_valid>=8&&(n.pending_buf[n.pending++]=255&n.bi_buf,n.bi_buf>>=8,n.bi_valid-=8)})(e)}},Ce=(e,n,r,i)=>{let a=65535&e|0,_=e>>>16&65535|0,t=0;for(;r!==0;){t=r>2e3?2e3:r,r-=t;do a=a+n[i++]|0,_=_+a|0;while(--t);a%=65521,_%=65521}return a|_<<16|0};const na=new Uint32Array((()=>{let e,n=[];for(var r=0;r<256;r++){e=r;for(var i=0;i<8;i++)e=1&e?3988292384^e>>>1:e>>>1;n[r]=e}return n})());var Z=(e,n,r,i)=>{const a=na,_=i+r;e^=-1;for(let t=i;t<_;t++)e=e>>>8^a[255&(e^n[t])];return-1^e},ae={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"},ge={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_MEM_ERROR:-4,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8};const{_tr_init:ra,_tr_stored_block:ia,_tr_flush_block:sa,_tr_tally:R,_tr_align:ta}=aa,{Z_NO_FLUSH:O,Z_PARTIAL_FLUSH:_a,Z_FULL_FLUSH:la,Z_FINISH:U,Z_BLOCK:Be,Z_OK:x,Z_STREAM_END:He,Z_STREAM_ERROR:b,Z_DATA_ERROR:ha,Z_BUF_ERROR:we,Z_DEFAULT_COMPRESSION:oa,Z_FILTERED:da,Z_HUFFMAN_ONLY:ne,Z_RLE:ua,Z_FIXED:fa,Z_DEFAULT_STRATEGY:ca,Z_UNKNOWN:pa,Z_DEFLATED:re}=ge,D=258,m=262,ie=103,N=113,G=666,S=(e,n)=>(e.msg=ae[n],n),Me=e=>(e<<1)-(e>4?9:0),T=e=>{let n=e.length;for(;--n>=0;)e[n]=0};let L=(e,n,r)=>(n<<e.hash_shift^r)&e.hash_mask;const F=e=>{const n=e.state;let r=n.pending;r>e.avail_out&&(r=e.avail_out),r!==0&&(e.output.set(n.pending_buf.subarray(n.pending_out,n.pending_out+r),e.next_out),e.next_out+=r,n.pending_out+=r,e.total_out+=r,e.avail_out-=r,n.pending-=r,n.pending===0&&(n.pending_out=0))},p=(e,n)=>{sa(e,e.block_start>=0?e.block_start:-1,e.strstart-e.block_start,n),e.block_start=e.strstart,F(e.strm)},d=(e,n)=>{e.pending_buf[e.pending++]=n},X=(e,n)=>{e.pending_buf[e.pending++]=n>>>8&255,e.pending_buf[e.pending++]=255&n},ga=(e,n,r,i)=>{let a=e.avail_in;return a>i&&(a=i),a===0?0:(e.avail_in-=a,n.set(e.input.subarray(e.next_in,e.next_in+a),r),e.state.wrap===1?e.adler=Ce(e.adler,n,a,r):e.state.wrap===2&&(e.adler=Z(e.adler,n,a,r)),e.next_in+=a,e.total_in+=a,a)},Pe=(e,n)=>{let r,i,a=e.max_chain_length,_=e.strstart,t=e.prev_length,s=e.nice_match;const h=e.strstart>e.w_size-m?e.strstart-(e.w_size-m):0,l=e.window,o=e.w_mask,u=e.prev,g=e.strstart+D;let c=l[_+t-1],te=l[_+t];e.prev_length>=e.good_match&&(a>>=2),s>e.lookahead&&(s=e.lookahead);do if(r=n,l[r+t]===te&&l[r+t-1]===c&&l[r]===l[_]&&l[++r]===l[_+1]){_+=2,r++;do;while(l[++_]===l[++r]&&l[++_]===l[++r]&&l[++_]===l[++r]&&l[++_]===l[++r]&&l[++_]===l[++r]&&l[++_]===l[++r]&&l[++_]===l[++r]&&l[++_]===l[++r]&&_<g);if(i=D-(g-_),_=g-D,i>t){if(e.match_start=n,t=i,i>=s)break;c=l[_+t-1],te=l[_+t]}}while((n=u[n&o])>h&&--a!=0);return t<=e.lookahead?t:e.lookahead},I=e=>{const n=e.w_size;let r,i,a,_,t;do{if(_=e.window_size-e.lookahead-e.strstart,e.strstart>=n+(n-m)){e.window.set(e.window.subarray(n,n+n),0),e.match_start-=n,e.strstart-=n,e.block_start-=n,i=e.hash_size,r=i;do a=e.head[--r],e.head[r]=a>=n?a-n:0;while(--i);i=n,r=i;do a=e.prev[--r],e.prev[r]=a>=n?a-n:0;while(--i);_+=n}if(e.strm.avail_in===0)break;if(i=ga(e.strm,e.window,e.strstart+e.lookahead,_),e.lookahead+=i,e.lookahead+e.insert>=3)for(t=e.strstart-e.insert,e.ins_h=e.window[t],e.ins_h=L(e,e.ins_h,e.window[t+1]);e.insert&&(e.ins_h=L(e,e.ins_h,e.window[t+3-1]),e.prev[t&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=t,t++,e.insert--,!(e.lookahead+e.insert<3)););}while(e.lookahead<m&&e.strm.avail_in!==0)},be=(e,n)=>{let r,i;for(;;){if(e.lookahead<m){if(I(e),e.lookahead<m&&n===O)return 1;if(e.lookahead===0)break}if(r=0,e.lookahead>=3&&(e.ins_h=L(e,e.ins_h,e.window[e.strstart+3-1]),r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),r!==0&&e.strstart-r<=e.w_size-m&&(e.match_length=Pe(e,r)),e.match_length>=3)if(i=R(e,e.strstart-e.match_start,e.match_length-3),e.lookahead-=e.match_length,e.match_length<=e.max_lazy_match&&e.lookahead>=3){e.match_length--;do e.strstart++,e.ins_h=L(e,e.ins_h,e.window[e.strstart+3-1]),r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart;while(--e.match_length!=0);e.strstart++}else e.strstart+=e.match_length,e.match_length=0,e.ins_h=e.window[e.strstart],e.ins_h=L(e,e.ins_h,e.window[e.strstart+1]);else i=R(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++;if(i&&(p(e,!1),e.strm.avail_out===0))return 1}return e.insert=e.strstart<2?e.strstart:2,n===U?(p(e,!0),e.strm.avail_out===0?3:4):e.last_lit&&(p(e,!1),e.strm.avail_out===0)?1:2},C=(e,n)=>{let r,i,a;for(;;){if(e.lookahead<m){if(I(e),e.lookahead<m&&n===O)return 1;if(e.lookahead===0)break}if(r=0,e.lookahead>=3&&(e.ins_h=L(e,e.ins_h,e.window[e.strstart+3-1]),r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),e.prev_length=e.match_length,e.prev_match=e.match_start,e.match_length=2,r!==0&&e.prev_length<e.max_lazy_match&&e.strstart-r<=e.w_size-m&&(e.match_length=Pe(e,r),e.match_length<=5&&(e.strategy===da||e.match_length===3&&e.strstart-e.match_start>4096)&&(e.match_length=2)),e.prev_length>=3&&e.match_length<=e.prev_length){a=e.strstart+e.lookahead-3,i=R(e,e.strstart-1-e.prev_match,e.prev_length-3),e.lookahead-=e.prev_length-1,e.prev_length-=2;do++e.strstart<=a&&(e.ins_h=L(e,e.ins_h,e.window[e.strstart+3-1]),r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart);while(--e.prev_length!=0);if(e.match_available=0,e.match_length=2,e.strstart++,i&&(p(e,!1),e.strm.avail_out===0))return 1}else if(e.match_available){if(i=R(e,0,e.window[e.strstart-1]),i&&p(e,!1),e.strstart++,e.lookahead--,e.strm.avail_out===0)return 1}else e.match_available=1,e.strstart++,e.lookahead--}return e.match_available&&(i=R(e,0,e.window[e.strstart-1]),e.match_available=0),e.insert=e.strstart<2?e.strstart:2,n===U?(p(e,!0),e.strm.avail_out===0?3:4):e.last_lit&&(p(e,!1),e.strm.avail_out===0)?1:2};function A(e,n,r,i,a){this.good_length=e,this.max_lazy=n,this.nice_length=r,this.max_chain=i,this.func=a}const W=[new A(0,0,0,0,(e,n)=>{let r=65535;for(r>e.pending_buf_size-5&&(r=e.pending_buf_size-5);;){if(e.lookahead<=1){if(I(e),e.lookahead===0&&n===O)return 1;if(e.lookahead===0)break}e.strstart+=e.lookahead,e.lookahead=0;const i=e.block_start+r;if((e.strstart===0||e.strstart>=i)&&(e.lookahead=e.strstart-i,e.strstart=i,p(e,!1),e.strm.avail_out===0)||e.strstart-e.block_start>=e.w_size-m&&(p(e,!1),e.strm.avail_out===0))return 1}return e.insert=0,n===U?(p(e,!0),e.strm.avail_out===0?3:4):(e.strstart>e.block_start&&(p(e,!1),e.strm.avail_out),1)}),new A(4,4,8,4,be),new A(4,5,16,8,be),new A(4,6,32,32,be),new A(4,4,16,16,C),new A(8,16,32,32,C),new A(8,16,128,128,C),new A(8,32,128,256,C),new A(32,128,258,1024,C),new A(32,258,258,4096,C)];function wa(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=re,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new Uint16Array(1146),this.dyn_dtree=new Uint16Array(122),this.bl_tree=new Uint16Array(78),T(this.dyn_ltree),T(this.dyn_dtree),T(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new Uint16Array(16),this.heap=new Uint16Array(573),T(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new Uint16Array(573),T(this.depth),this.l_buf=0,this.lit_bufsize=0,this.last_lit=0,this.d_buf=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}const je=e=>{if(!e||!e.state)return S(e,b);e.total_in=e.total_out=0,e.data_type=pa;const n=e.state;return n.pending=0,n.pending_out=0,n.wrap<0&&(n.wrap=-n.wrap),n.status=n.wrap?42:N,e.adler=n.wrap===2?0:1,n.last_flush=O,ra(n),x},Ke=e=>{const n=je(e);var r;return n===x&&((r=e.state).window_size=2*r.w_size,T(r.head),r.max_lazy_match=W[r.level].max_lazy,r.good_match=W[r.level].good_length,r.nice_match=W[r.level].nice_length,r.max_chain_length=W[r.level].max_chain,r.strstart=0,r.block_start=0,r.lookahead=0,r.insert=0,r.match_length=r.prev_length=2,r.match_available=0,r.ins_h=0),n},Ye=(e,n,r,i,a,_)=>{if(!e)return b;let t=1;if(n===oa&&(n=6),i<0?(t=0,i=-i):i>15&&(t=2,i-=16),a<1||a>9||r!==re||i<8||i>15||n<0||n>9||_<0||_>fa)return S(e,b);i===8&&(i=9);const s=new wa;return e.state=s,s.strm=e,s.wrap=t,s.gzhead=null,s.w_bits=i,s.w_size=1<<s.w_bits,s.w_mask=s.w_size-1,s.hash_bits=a+7,s.hash_size=1<<s.hash_bits,s.hash_mask=s.hash_size-1,s.hash_shift=~~((s.hash_bits+3-1)/3),s.window=new Uint8Array(2*s.w_size),s.head=new Uint16Array(s.hash_size),s.prev=new Uint16Array(s.w_size),s.lit_bufsize=1<<a+6,s.pending_buf_size=4*s.lit_bufsize,s.pending_buf=new Uint8Array(s.pending_buf_size),s.d_buf=1*s.lit_bufsize,s.l_buf=3*s.lit_bufsize,s.level=n,s.strategy=_,s.method=r,Ke(e)};var q={deflateInit:(e,n)=>Ye(e,n,re,15,8,ca),deflateInit2:Ye,deflateReset:Ke,deflateResetKeep:je,deflateSetHeader:(e,n)=>e&&e.state?e.state.wrap!==2?b:(e.state.gzhead=n,x):b,deflate:(e,n)=>{let r,i;if(!e||!e.state||n>Be||n<0)return e?S(e,b):b;const a=e.state;if(!e.output||!e.input&&e.avail_in!==0||a.status===G&&n!==U)return S(e,e.avail_out===0?we:b);a.strm=e;const _=a.last_flush;if(a.last_flush=n,a.status===42)if(a.wrap===2)e.adler=0,d(a,31),d(a,139),d(a,8),a.gzhead?(d(a,(a.gzhead.text?1:0)+(a.gzhead.hcrc?2:0)+(a.gzhead.extra?4:0)+(a.gzhead.name?8:0)+(a.gzhead.comment?16:0)),d(a,255&a.gzhead.time),d(a,a.gzhead.time>>8&255),d(a,a.gzhead.time>>16&255),d(a,a.gzhead.time>>24&255),d(a,a.level===9?2:a.strategy>=ne||a.level<2?4:0),d(a,255&a.gzhead.os),a.gzhead.extra&&a.gzhead.extra.length&&(d(a,255&a.gzhead.extra.length),d(a,a.gzhead.extra.length>>8&255)),a.gzhead.hcrc&&(e.adler=Z(e.adler,a.pending_buf,a.pending,0)),a.gzindex=0,a.status=69):(d(a,0),d(a,0),d(a,0),d(a,0),d(a,0),d(a,a.level===9?2:a.strategy>=ne||a.level<2?4:0),d(a,3),a.status=N);else{let t=re+(a.w_bits-8<<4)<<8,s=-1;s=a.strategy>=ne||a.level<2?0:a.level<6?1:a.level===6?2:3,t|=s<<6,a.strstart!==0&&(t|=32),t+=31-t%31,a.status=N,X(a,t),a.strstart!==0&&(X(a,e.adler>>>16),X(a,65535&e.adler)),e.adler=1}if(a.status===69)if(a.gzhead.extra){for(r=a.pending;a.gzindex<(65535&a.gzhead.extra.length)&&(a.pending!==a.pending_buf_size||(a.gzhead.hcrc&&a.pending>r&&(e.adler=Z(e.adler,a.pending_buf,a.pending-r,r)),F(e),r=a.pending,a.pending!==a.pending_buf_size));)d(a,255&a.gzhead.extra[a.gzindex]),a.gzindex++;a.gzhead.hcrc&&a.pending>r&&(e.adler=Z(e.adler,a.pending_buf,a.pending-r,r)),a.gzindex===a.gzhead.extra.length&&(a.gzindex=0,a.status=73)}else a.status=73;if(a.status===73)if(a.gzhead.name){r=a.pending;do{if(a.pending===a.pending_buf_size&&(a.gzhead.hcrc&&a.pending>r&&(e.adler=Z(e.adler,a.pending_buf,a.pending-r,r)),F(e),r=a.pending,a.pending===a.pending_buf_size)){i=1;break}i=a.gzindex<a.gzhead.name.length?255&a.gzhead.name.charCodeAt(a.gzindex++):0,d(a,i)}while(i!==0);a.gzhead.hcrc&&a.pending>r&&(e.adler=Z(e.adler,a.pending_buf,a.pending-r,r)),i===0&&(a.gzindex=0,a.status=91)}else a.status=91;if(a.status===91)if(a.gzhead.comment){r=a.pending;do{if(a.pending===a.pending_buf_size&&(a.gzhead.hcrc&&a.pending>r&&(e.adler=Z(e.adler,a.pending_buf,a.pending-r,r)),F(e),r=a.pending,a.pending===a.pending_buf_size)){i=1;break}i=a.gzindex<a.gzhead.comment.length?255&a.gzhead.comment.charCodeAt(a.gzindex++):0,d(a,i)}while(i!==0);a.gzhead.hcrc&&a.pending>r&&(e.adler=Z(e.adler,a.pending_buf,a.pending-r,r)),i===0&&(a.status=ie)}else a.status=ie;if(a.status===ie&&(a.gzhead.hcrc?(a.pending+2>a.pending_buf_size&&F(e),a.pending+2<=a.pending_buf_size&&(d(a,255&e.adler),d(a,e.adler>>8&255),e.adler=0,a.status=N)):a.status=N),a.pending!==0){if(F(e),e.avail_out===0)return a.last_flush=-1,x}else if(e.avail_in===0&&Me(n)<=Me(_)&&n!==U)return S(e,we);if(a.status===G&&e.avail_in!==0)return S(e,we);if(e.avail_in!==0||a.lookahead!==0||n!==O&&a.status!==G){let t=a.strategy===ne?((s,h)=>{let l;for(;;){if(s.lookahead===0&&(I(s),s.lookahead===0)){if(h===O)return 1;break}if(s.match_length=0,l=R(s,0,s.window[s.strstart]),s.lookahead--,s.strstart++,l&&(p(s,!1),s.strm.avail_out===0))return 1}return s.insert=0,h===U?(p(s,!0),s.strm.avail_out===0?3:4):s.last_lit&&(p(s,!1),s.strm.avail_out===0)?1:2})(a,n):a.strategy===ua?((s,h)=>{let l,o,u,g;const c=s.window;for(;;){if(s.lookahead<=D){if(I(s),s.lookahead<=D&&h===O)return 1;if(s.lookahead===0)break}if(s.match_length=0,s.lookahead>=3&&s.strstart>0&&(u=s.strstart-1,o=c[u],o===c[++u]&&o===c[++u]&&o===c[++u])){g=s.strstart+D;do;while(o===c[++u]&&o===c[++u]&&o===c[++u]&&o===c[++u]&&o===c[++u]&&o===c[++u]&&o===c[++u]&&o===c[++u]&&u<g);s.match_length=D-(g-u),s.match_length>s.lookahead&&(s.match_length=s.lookahead)}if(s.match_length>=3?(l=R(s,1,s.match_length-3),s.lookahead-=s.match_length,s.strstart+=s.match_length,s.match_length=0):(l=R(s,0,s.window[s.strstart]),s.lookahead--,s.strstart++),l&&(p(s,!1),s.strm.avail_out===0))return 1}return s.insert=0,h===U?(p(s,!0),s.strm.avail_out===0?3:4):s.last_lit&&(p(s,!1),s.strm.avail_out===0)?1:2})(a,n):W[a.level].func(a,n);if(t!==3&&t!==4||(a.status=G),t===1||t===3)return e.avail_out===0&&(a.last_flush=-1),x;if(t===2&&(n===_a?ta(a):n!==Be&&(ia(a,0,0,!1),n===la&&(T(a.head),a.lookahead===0&&(a.strstart=0,a.block_start=0,a.insert=0))),F(e),e.avail_out===0))return a.last_flush=-1,x}return n!==U?x:a.wrap<=0?He:(a.wrap===2?(d(a,255&e.adler),d(a,e.adler>>8&255),d(a,e.adler>>16&255),d(a,e.adler>>24&255),d(a,255&e.total_in),d(a,e.total_in>>8&255),d(a,e.total_in>>16&255),d(a,e.total_in>>24&255)):(X(a,e.adler>>>16),X(a,65535&e.adler)),F(e),a.wrap>0&&(a.wrap=-a.wrap),a.pending!==0?x:He)},deflateEnd:e=>{if(!e||!e.state)return b;const n=e.state.status;return n!==42&&n!==69&&n!==73&&n!==91&&n!==ie&&n!==N&&n!==G?S(e,b):(e.state=null,n===N?S(e,ha):x)},deflateSetDictionary:(e,n)=>{let r=n.length;if(!e||!e.state)return b;const i=e.state,a=i.wrap;if(a===2||a===1&&i.status!==42||i.lookahead)return b;if(a===1&&(e.adler=Ce(e.adler,n,r,0)),i.wrap=0,r>=i.w_size){a===0&&(T(i.head),i.strstart=0,i.block_start=0,i.insert=0);let h=new Uint8Array(i.w_size);h.set(n.subarray(r-i.w_size,r),0),n=h,r=i.w_size}const _=e.avail_in,t=e.next_in,s=e.input;for(e.avail_in=r,e.next_in=0,e.input=n,I(i);i.lookahead>=3;){let h=i.strstart,l=i.lookahead-2;do i.ins_h=L(i,i.ins_h,i.window[h+3-1]),i.prev[h&i.w_mask]=i.head[i.ins_h],i.head[i.ins_h]=h,h++;while(--l);i.strstart=h,i.lookahead=2,I(i)}return i.strstart+=i.lookahead,i.block_start=i.strstart,i.insert=i.lookahead,i.lookahead=0,i.match_length=i.prev_length=2,i.match_available=0,e.next_in=t,e.input=s,e.avail_in=_,i.wrap=a,x},deflateInfo:"pako deflate (from Nodeca project)"};const ba=(e,n)=>Object.prototype.hasOwnProperty.call(e,n);var ma=function(e){const n=Array.prototype.slice.call(arguments,1);for(;n.length;){const r=n.shift();if(r){if(typeof r!="object")throw new TypeError(r+"must be non-object");for(const i in r)ba(r,i)&&(e[i]=r[i])}}return e},va=e=>{let n=0;for(let i=0,a=e.length;i<a;i++)n+=e[i].length;const r=new Uint8Array(n);for(let i=0,a=0,_=e.length;i<_;i++){let t=e[i];r.set(t,a),a+=t.length}return r};let ya=!0;try{String.fromCharCode.apply(null,new Uint8Array(1))}catch{ya=!1}const me=new Uint8Array(256);for(let e=0;e<256;e++)me[e]=e>=252?6:e>=248?5:e>=240?4:e>=224?3:e>=192?2:1;me[254]=me[254]=1;var Ge=e=>{if(typeof TextEncoder=="function"&&TextEncoder.prototype.encode)return new TextEncoder().encode(e);let n,r,i,a,_,t=e.length,s=0;for(a=0;a<t;a++)r=e.charCodeAt(a),(64512&r)==55296&&a+1<t&&(i=e.charCodeAt(a+1),(64512&i)==56320&&(r=65536+(r-55296<<10)+(i-56320),a++)),s+=r<128?1:r<2048?2:r<65536?3:4;for(n=new Uint8Array(s),_=0,a=0;_<s;a++)r=e.charCodeAt(a),(64512&r)==55296&&a+1<t&&(i=e.charCodeAt(a+1),(64512&i)==56320&&(r=65536+(r-55296<<10)+(i-56320),a++)),r<128?n[_++]=r:r<2048?(n[_++]=192|r>>>6,n[_++]=128|63&r):r<65536?(n[_++]=224|r>>>12,n[_++]=128|r>>>6&63,n[_++]=128|63&r):(n[_++]=240|r>>>18,n[_++]=128|r>>>12&63,n[_++]=128|r>>>6&63,n[_++]=128|63&r);return n},za=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0};const Xe=Object.prototype.toString,{Z_NO_FLUSH:ka,Z_SYNC_FLUSH:xa,Z_FULL_FLUSH:Aa,Z_FINISH:Ea,Z_OK:se,Z_STREAM_END:Za,Z_DEFAULT_COMPRESSION:Ra,Z_DEFAULT_STRATEGY:Ua,Z_DEFLATED:Sa}=ge;function J(e){this.options=ma({level:Ra,method:Sa,chunkSize:16384,windowBits:15,memLevel:8,strategy:Ua},e||{});let n=this.options;n.raw&&n.windowBits>0?n.windowBits=-n.windowBits:n.gzip&&n.windowBits>0&&n.windowBits<16&&(n.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new za,this.strm.avail_out=0;let r=q.deflateInit2(this.strm,n.level,n.method,n.windowBits,n.memLevel,n.strategy);if(r!==se)throw new Error(ae[r]);if(n.header&&q.deflateSetHeader(this.strm,n.header),n.dictionary){let i;if(i=typeof n.dictionary=="string"?Ge(n.dictionary):Xe.call(n.dictionary)==="[object ArrayBuffer]"?new Uint8Array(n.dictionary):n.dictionary,r=q.deflateSetDictionary(this.strm,i),r!==se)throw new Error(ae[r]);this._dict_set=!0}}function ve(e,n){const r=new J(n);if(r.push(e,!0),r.err)throw r.msg||ae[r.err];return r.result}J.prototype.push=function(e,n){const r=this.strm,i=this.options.chunkSize;let a,_;if(this.ended)return!1;for(_=n===~~n?n:n===!0?Ea:ka,typeof e=="string"?r.input=Ge(e):Xe.call(e)==="[object ArrayBuffer]"?r.input=new Uint8Array(e):r.input=e,r.next_in=0,r.avail_in=r.input.length;;)if(r.avail_out===0&&(r.output=new Uint8Array(i),r.next_out=0,r.avail_out=i),(_===xa||_===Aa)&&r.avail_out<=6)this.onData(r.output.subarray(0,r.next_out)),r.avail_out=0;else{if(a=q.deflate(r,_),a===Za)return r.next_out>0&&this.onData(r.output.subarray(0,r.next_out)),a=q.deflateEnd(this.strm),this.onEnd(a),this.ended=!0,a===se;if(r.avail_out!==0){if(_>0&&r.next_out>0)this.onData(r.output.subarray(0,r.next_out)),r.avail_out=0;else if(r.avail_in===0)break}else this.onData(r.output)}return!0},J.prototype.onData=function(e){this.chunks.push(e)},J.prototype.onEnd=function(e){e===se&&(this.result=va(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg};var We=J,qe=ve,Je=function(e,n){return(n=n||{}).raw=!0,ve(e,n)},Qe=function(e,n){return(n=n||{}).gzip=!0,ve(e,n)},Ve=ge,Ta={Deflate:We,deflate:qe,deflateRaw:Je,gzip:Qe,constants:Ve};y.Deflate=We,y.constants=Ve,y.default=Ta,y.deflate=qe,y.deflateRaw=Je,y.gzip=Qe,Object.defineProperty(y,"__esModule",{value:!0})});
