@font-face {
    font-family: "YouSheBiaoTiHei";
    src: url("~@/assets/css/YouSheBiaoTiHei-2.ttf");
    font-weight: normal;
    font-style: normal;
  }

.left,.right,.card{
    width: 20vw;
    height: calc(95vh - 4.8rem);
    background-color: #00202550;
    border-radius: 4px;
    backdrop-filter: blur(4px);
    padding: 16px;
    box-sizing: border-box;
    position: absolute;top: 6.2rem;
}

.card {
    transition: all 0.3s;
    overflow: hidden;
    box-shadow: 2px 2px 10px #00000050;
    .card_title {
        display: flex;
        align-items: flex-end;
        justify-content: space-between;
        .card_title_content {
            height: 2.6rem; width: 80%;
            span {
                @--font-title();
                padding: 0 40px;
            }
        }
        .card_title_icon img {
            width: 2.1rem;
            height: 2.1rem;
            transform: rotate(180deg);
            transition: transform 0.3s;
            cursor: pointer;
        }
    }
    .card_content { 
        height: calc(100% - 2.6rem);
        .switch{
            >div{
              display: flex; align-items: center; justify-content: space-between;
              padding: 10px;
              :first-child{ color:@--fontColor;font-size: 1.1rem;}
            }
            span{ text-shadow: 2px 2px 4px #777777;}
            .switch_f{ .el-switch{padding: 0 10px;}}
        }
        img{ width: 2rem; margin-right: 5px; }
    }
}

.hsv {filter: hue-rotate(220deg) saturate(130%) brightness(140%);}
