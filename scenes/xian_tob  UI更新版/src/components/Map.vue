<template>
  <div v-loading="loading" element-loading-text="正在加载数据..." element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.8)" style="width: 100%; height: 100%">
    <div id="box"></div>
    <div id="heatMap" style="width: 10000px; height: 10000px; background-color: rgb(0, 175, 255); display: none;"></div>
    <!-- 加载数据loading -->
    <div v-loading="loadingModel" element-loading-text="正在加载数据..." element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(0, 0, 0, 0)" class="loadingModelPromise"></div>
    <!-- <div id="charts"></div> -->
    <!-- <About ref="about" class="map-operate" />
    <Lonlat ref="long" class="buttom_lng" /> -->
    <!-- <div class="legend" v-show="legendShow"></div>
    <Viewer class="daping" ref="xinhaoValue" />
    <div class="leftBack" v-show="largeScreenShow"></div>
    <div class="rightBack" v-show="largeScreenShow"></div>
    <div class="rightBack" v-show="AnalysisCharsShow"></div> -->
  </div>
</template>

<script>
//js
import flyTos from '../js/flyTo.js'
import cesiumMap from '../js/cesiumMap.js'
// import PolylineTrailMaterialProperty from "../js/PolylineTrailMaterialProperty.js"; //线纹理
import circleRippleMaterialProperty from '../js/circleRippleMaterialProperty.js' //点纹理
// import circleDiffuseMaterilaProperty from "../js/circleDiffuseMaterilaProperty.js"; //扩散圆
import '../js/dynamicWallMaterialProperty.js' //墙纹理
import h337 from '../js/heatmaps.js' //热力图
// import "../js/ellipsoidTrailMaterialProperty" //球纹理
import '../js/SkyBoxOnGround' //天空盒
//插件
import * as turf from '@turf/turf'
import * as echarts from 'echarts'
//组件
import Lonlat from './lonlat.vue'
import About from './About.vue'
// import Viewer from './viewer.vue'
//图片
import colorss from '../assets/colors.png'
import lineColor from '../assets/line.png'
import positiveX from '../assets/sky/bk.jpg' //天空盒
import negativeX from '../assets/sky/rt.jpg' //天空盒
import positiveY from '../assets/sky/ft.jpg' //天空盒
import negativeY from '../assets/sky/lf.jpg' //天空盒
import positiveZ from '../assets/sky/up.jpg' //天空盒
import negativeZ from '../assets/sky/dn.jpg' //天空盒
import waterNormals from '../assets/waterNormals.jpg' //水面
import base from '../assets/base2.png' //基站图标
import base1 from '../assets/base3.png' //基站图标
import base2 from '../assets/base4.png' //基站图标
import base3 from '../assets/base5.png' //基站图标

//模型
// import zl from "../models/zlv2.gltf";
// import baogan from "../models/xiaoqu_baogan2.gltf";
// import tianxian from "../models/xiaoqu.gltf";
// import chegltf from "../models/private_car2_dh.gltf";
// import zhjf from "../models/zhjf.gltf"; //综合机房
//json
// import jizhanpont from "../models/json/jizhanpont.json"; // 基站点
// import xiaoqufugai from "../models/json/xiaoqufugai.json"; // 基站覆盖范围
// import rsrp from "../models/json/rsrp.json"; //10m电平信号
// import limiandian from "../models/json/limiandian.json"; //立面点
let fromDegreesArrayHeights = [] //定义数组
let colors = [] //颜色数组
let pnts = [] //轨迹点存入数组
let lines = [] //轨迹线存入数组
let planeEntities = [] //存入切割面实体
let targetY = 0.0 //切割数值
let targetY2 = 0.0 //切割数值
let targetY3 = 0.0 //切割数值
let tileset //体元
let walls = [] //墙集合
let xianModel = [] //西安模型集合
let pickObject = [] //储存点击的模型
let antennaArray = [] //储存天线支架
let mimos = [] //储存波束
let WebsiteArray = [] //储存基站
let azjArray = [] //存储安装架
let txzjArray = [] //存储天线支架
let antennaAry = [] //存储天线
let billboardArray = [] //存储标签
let bycTransmitterArray = [] //储存不夜城天线
let MIMOid = [] //波束id
let jizhanshuju //存储基站数据json
let tianxianshuju //存储天线数据
let canvas2
let wall //墙
let car1 //车
let car2 //车2
let xinhaolines = []
let ripple = null //波纹圆
let rrp = {
  rrp_1: [-120, -110, 0.5],
  rrp_2: [-110, -100, 0.5],
  rrp_3: [-100, -90, 0.5],
  rrp_4: [-90, -80, 0.5],
  rrp_5: [-80, -70, 0.5],
  rrp_6: [-70, -60, 0.5],
  rrp_7: [-60, -50, 0.5],
  rrp_8: [-50, -40, 0.5],
  rrp_9: [-40, -30, 0.5],
  rrp_10: [-30, -20, 0.5],
}
let latMin = 34.18
let latMax = 34.4
let lonMin = 108.8
let lonMax = 109.15
let heatMapjieshou //接收热力图
let heatMap_Received = null
let promise6 //grojson
let azimuthArray = [] //存储方位角参数
let mechanicalArray = [] //存储下倾角
export default {
  components: {
    Lonlat,
    About,
    // Viewer,
  },
  data() {
    return {
      value: false,
      Received_slider: [0, 70],
      Received: [
        {
          label: 'rsrp',
          value: 'rsrp',
        },
        {
          label: 'rsrq',
          value: 'rsrq',
        },
        {
          label: 'rssi',
          value: 'rssi',
        },
      ],
      token:
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJlYTQ2ZjdjNS1jM2E0LTQ1M2EtOWM0My1mODMzNzY3YjYzY2YiLCJpZCI6MjkzMjcsInNjb3BlcyI6WyJhc3IiLCJnYyJdLCJpYXQiOjE1OTE5NDIzNjB9.RzKlVTVDTQ9r7cqCo-PDydgUh8Frgw0Erul_BVxiS9c',
      loading: false,
      icon: {
        icon1: false,
        icon2: false,
        icon3: false,
        icon4: false,
        icon5: false,
        icon6: false,
        icon7: false,
      },
      keyboardLoadCar: true,
      shijiao: '第一人称',
      shijiaoShow: false,
      cartographic: null,
      hpRoll: null,
      showFs: '开启',
      showFs2: '开启',
      switchingStatus: true,
      loadings: true,
      rsrpColor: '#fff',
      rsrpNum: 0,
      value1: 0,
      value2: 0,
      value3: 0,
      value4: 5,
      slider1_min: -6700,
      slider1_max: 6700,
      slider2_min: -8615,
      slider2_max: 8615,
      slider3_min: -40,
      slider3_max: 40,
      slider4_min: 1,
      slider4_max: 20,
      analyse: false,
      modelName: '大唐不夜城',
      largeScreenShow: true,
      azimuth: 0, //下倾角
      azimuth_min: -30, //最小下倾角
      azimuth_max: 0, //最大下倾角
      mechanical: 0, //方位角调整
      mechanical_min: -60, //最小方位角
      mechanical_max: 60, //最大方位角
      azimuthShow: false, //方位角参数调整
      zhandianShow: false, //基站详情
      tableData: [],
      loadingModel: false,
      btnsShow: false,
      loadText: false, //判断业务数据是否加载
      dialogVisible: false, //沉浸式分析弹框
      form: {
        model: '车',
        type: '点',
        speed: 10,
      },
      updateSpeed: 10,
      refractionLine: '显示',
      modelTiaozheng: false,
      showMI: 0, //水平切割位置
      crosswiseMI: 0, //横向切割位置
      lengthwaysMI: 0, //纵向切割位置
      pointsSize: 5,
      qiegemishow: false, //水平切割深度显示
      heatMapShow: true,
      wymbOpen: true,
      RSRPTitleName: 'RSRP',
      guagao: [
        {
          value: '1',
          label: '25米以下',
        },
        {
          value: '2',
          label: '25米-40米',
        },
        {
          value: '3',
          label: '40米-80米',
        },
        {
          value: '4',
          label: '80米以上',
        },
      ],
      txguagao: '1',
      siteArray: [],
      Stationlist: [
        {
          station: '32848',
          alarmName: '驻波比告警',
          time: '2022-11-18',
          Longitude: 108.91141,
          Latitude: 34.2894,
        },
        {
          station: '32783',
          alarmName: 'E1/T1链路远端接受故障告警',
          time: '2022-11-18',
          Longitude: 108.89177,
          Latitude: 34.22122,
        },
        {
          station: '766220',
          alarmName: '基站退服',
          time: '2022-11-18',
          Longitude: 108.87192,
          Latitude: 34.21968,
        },
        {
          station: '766221',
          alarmName: '设备温度高',
          time: '2022-11-18',
          Longitude: 108.86689,
          Latitude: 34.22485,
        },
        {
          station: '766223',
          alarmName: 'SCTP偶联断告警',
          time: '2022-11-18',
          Longitude: 108.866222,
          Latitude: 34.247526,
        },
      ],
      query_StationID: '',
      query_TianxianID: '',
      Received_Data: {},
      mimoSwitch: false,
      legendShow: false, //热力图图例显隐
      AnalysisCharsShow: false, //实时图表
    }
  },
  created() {
    this.$nextTick(() => {
      // 禁用右键
      document.oncontextmenu = new Function('event.returnValue=false')
      // 禁用选择
      document.onselectstart = new Function('event.returnValue=false')
      //禁止f12
      // document.οnkeydοwn = new Function('event.returnValue=false')
    })
  },
  mounted() {
    // this.loadXinhao() //加载业务数据
    this.loadzhandain() //加载基站数据
    this.loadTxzjJSON() //天线数据
    this.common()
    this.$nextTick(() => {
      // this.$refs.long.mouseLonlat(window.viewer)
    })
    this.loadXatiles(window.tileset.XAtileset) //x西安白膜
    //加载天空
    this.skyboxs({
      positiveX: positiveX,
      negativeX: negativeX,
      positiveY: positiveY,
      negativeY: negativeY,
      positiveZ: positiveZ,
      negativeZ: negativeZ,
    })
    // this.loadzhandain() //加载站点
    // this.loadTxzjJSON() //加载天线支架
    // this.loadTxJSON() //加载天线
    this.materialLine() //调用连线shader
    this.xianPolygon({
      url: window.geojson.XACenterBorder,
      name: '西安边界',
    }) //加载西安边界
    this.xianPolygon({
      url: window.geojson.XAGreenLand,
      name: '绿地',
    })
    this.xianPolygon({
      url: window.geojson.XAWaterArea,
      name: '水系',
    })
    this.xianPolygon({
      url: window.geojson.XARoadLines,
      name: '道路',
    })
    // this.draws111('polygon')
  },
  methods: {
    async queryheatMapdata(v){
      console.log(v);
      await this.$http.post('http://*************:8086/zijin_garden-api/dp-data/dispose/distance-calculationAll',v
      ).then((data) => {
        const result = data.data.data
        const mapdata = []
        console.log(result);
        result.forEach((item) => {
          const obj = {
            x: Math.floor(((parseFloat(item.x) - lonMin) / (lonMax - lonMin)) * 10000),
            y: Math.floor((Math.abs(parseFloat(item.y) - latMax) / (latMax - latMin)) * 10000),
            value: parseInt( item.rsrp),
          }
          mapdata.push(obj)
        })
        let max = -1000000
          mapdata.map((item) => {
            if (item.value > max) {
              max = item.value
            }
          })
          if (!heatMap_Received) {
            heatMap_Received = h337.create({
              container: document.querySelector('#heatMap'),
              radius: 20,
              gradient: {
                '.05': '#1F2CE5',
                '.15': '#1F68E5',
                '.30': '#1ECBE6', // the gradient used if not given in the heatmap options object
                '.45': '#1FE580',
                '.60': '#C0F115',
                '.85': '#FF9129',
                '.95': '#FE001B',
              },
            })
          }
          const resultData = { max: max, data: mapdata }
          
          console.log(resultData);
          heatMap_Received.setData(resultData)
          this.stickHot()
      })
    },
    //开启波束
    mimoChange(val) {
      if (val) {
        mimos.forEach(item => {
          item.show = true
        })
      } else {
        mimos.forEach(item => {
          item.show = false
        })
      }
    },
    Received_slider_change(v) {
      if (heatMap_Received == null) {
        // this.queryReceived(this.query_StationID)
      } else {
        viewer.entities.remove(heatMapjieshou)  //  初始化热力图
        heatMapjieshou = null

        let list = []
        this.Received_Data.data.forEach((item) => {
          if (item.height >= v[0] && item.height <= v[1]) {
            list.push(item)
          }
        })
        const data = {
          max: this.Received_Data.max,
          data: list,
        }
        heatMap_Received.setData(data)
        this.stickHot()
      }
    },
    async queryReceived(v) {
      if (v) {
        this.loading = true
        if (!this.zhandianShow && this.azimuthShow) {
          await this.$http
            .get(
              window.queryBase +
              `/igisrest/stgisapi/rest/service/wireless/FeatureServer/4/query?token=&objectIds=&where=1=1&geometry=&inSR=&spatialRel=esriSpatialRelIntersects&returnIdsOnly=false&returnDistinctValues=false&returnGeometry=true&outSR=4326&outFields=*&orderByFields=&f=geojson`,
              {
                // params: {
                //   where:"serving_cell = "+ this.query_TianxianID,
                //   geometry:"",
                //   inSR:"",
                //   spatialRel:"esriSpatialRelIntersects",
                //   returnIdsOnly: false,
                //   returnDistinctValues: false,
                //   returnGeometry: true,
                //   outSR: '4326',
                //   outFields: '*',
                //   orderByFields: "",
                //   f: 'geojson',
                // }
              }
            )
            .then((data) => {
              if (data.status === 200) {
                console.log(data);
                this.loading = false
                this.legendShow = true
                let points = []
                data.data.features.forEach((item) => {
                  const obj = {
                    x: Math.floor(
                      ((item.geometry.coordinates[0] - lonMin) /
                        (lonMax - lonMin)) *
                      10000
                    ),
                    y: Math.floor(
                      (Math.abs(item.geometry.coordinates[1] - latMax) /
                        (latMax - latMin)) *
                      10000
                    ),
                    value: parseInt(
                      item.properties.application_channel_throughput_dl
                    ),
                    height: parseFloat(item.properties.height),
                  }
                  points.push(obj)
                })
                this.Received_Data.data = points //^
                let max = -1000000
                points.map((item) => {
                  if (item.value > max) {
                    max = item.value
                  }
                })
                this.Received_Data.max = max //^
                
                if (!heatMap_Received) {
                  heatMap_Received = h337.create({
                    container: document.querySelector('#heatMap'),
                    // radius: 10,
                    gradient: {
                      '.05': '#1F2CE5',
                      '.15': '#1F68E5',
                      '.30': '#1ECBE6', // the gradient used if not given in the heatmap options object
                      '.45': '#1FE580',
                      '.60': '#C0F115',
                      '.85': '#FF9129',
                      '.95': '#FE001B',
                    },
                  })
                }
                let list = []
                this.Received_Data.data.forEach((item) => {
                  if (  item.height >= this.Received_slider[0] &&  item.height <= this.Received_slider[1]) {
                    list.push(item)
                  }
                })
                const mapdata = { max: max, data: list }
                
                console.log(mapdata);
                heatMap_Received.setData(mapdata)
                this.stickHot()
              }
            })
        } else {
          await this.$http
            .get(
              window.queryBase +
              `/igisrest/stgisapi/rest/service/wireless/FeatureServer/4/query`,
              {
                params: {
                  where: "serving_cell like '%" + this.query_StationID + "_%'",
                  geometry: '',
                  inSR: '',
                  spatialRel: 'esriSpatialRelIntersects',
                  returnIdsOnly: false,
                  returnDistinctValues: false,
                  returnGeometry: true,
                  outSR: '4326',
                  outFields: '*',
                  orderByFields: '',
                  f: 'geojson',
                },
              }
            )
            .then((data) => {
              if (data.status === 200) {
                this.loading = false
                this.legendShow = true
                let points = []
                data.data.features.forEach((item) => {
                  const obj = {
                    x: Math.floor(
                      ((item.geometry.coordinates[0] - lonMin) /
                        (lonMax - lonMin)) *
                      10000
                    ),
                    y: Math.floor(
                      (Math.abs(item.geometry.coordinates[1] - latMax) /
                        (latMax - latMin)) *
                      10000
                    ),
                    value: parseInt(
                      item.properties.application_channel_throughput_dl
                    ),
                    height: parseFloat(item.properties.height),
                  }
                  points.push(obj)
                })
                this.Received_Data.data = points //^
                let max = -1000000
                points.map((item) => {
                  if (item.value > max) {
                    max = item.value
                  }
                })
                this.Received_Data.max = max //^

                if (!heatMap_Received) {
                  heatMap_Received = h337.create({
                    container: document.querySelector('#heatMap'),
                    // radius: 100,
                    gradient: {
                      '.05': '#1F2CE5',
                      '.15': '#1F68E5',
                      '.30': '#1ECBE6', // the gradient used if not given in the heatmap options object
                      '.45': '#1FE580',
                      '.60': '#C0F115',
                      '.85': '#FF9129',
                      0.95: '#FE001B',
                    },
                  })
                }
                let list = []
                this.Received_Data.data.forEach((item) => {
                  if (
                    item.height >= this.Received_slider[0] &&
                    item.height <= this.Received_slider[1]
                  ) {
                    list.push(item)
                  }
                })
                const mapdata = { max: max, data: list }
                console.log(mapdata);
                heatMap_Received.setData(mapdata)
                this.stickHot()
              }
            })
        }
      } else {
        viewer.entities.remove(heatMapjieshou)
        heatMapjieshou = null
        this.legendShow = false
      }
    },
    common() {
      Cesium.Ion.defaultAccessToken = this.token
      Cesium.Camera.DEFAULT_VIEW_RECTANGLE = Cesium.Rectangle.fromDegrees(
        80,
        22,
        130,
        50
      )
      window.viewer = new Cesium.Viewer('box', {
        // sceneMode: Cesium.SceneMode.COLUMBUS_VIEW, //哥伦布视图
        scene3DOnly: true,
        infoBox: false, // 信息框
        shouldAnimate: true,
        vrButton: false, // VR按钮
        geocoder: false,
        homeButton: false, // 初始位置
        sceneModePicker: false,
        baseLayerPicker: false,
        navigationHelpButton: false,
        selectionIndicator: false, //选择框
        animation: true, //时间控件
        timeline: true, //时间线
        fullscreenButton: false,
        shadows: false, // 去掉阴影
        // terrainProvider: Cesium.createWorldTerrain(), // 世界地形
      })
      // 显示帧率
      viewer.scene.debugShowFramesPerSecond = false
      // 开启深度检测，默认是关闭的
      // viewer.scene.globe.depthTestAgainstTerrain = true;
      window.viewer.scene.globe.enableLighting = false // 开启全球光照
      //分辨率调整函数
      window.viewer.scene.fxaa = true
      window.viewer.scene.postProcessStages.fxaa.enabled = true
      window.viewer.animation.container.style.visibility = 'hidden' // 不显示动画控件
      window.viewer.timeline.container.style.visibility = 'hidden' // 不显示动画控件
      var supportsImageRenderingPixelated =
        viewer.cesiumWidget._supportsImageRenderingPixelated
      if (supportsImageRenderingPixelated) {
        var vtxfDpr = window.devicePixelRatio
        while (vtxfDpr >= 2.0) {
          vtxfDpr /= 2.0
        }
        window.viewer.resolutionScale = vtxfDpr
      }
      //深度检测
      window.viewer.scene.globe.depthTestAgainstTerrain = false
      //卫星影像
      window.viewer.imageryLayers.removeAll()
      let guge = new Cesium.ImageryLayer(
        new Cesium.ArcGisMapServerImageryProvider({
          url: 'https://services.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer',
          enablePickFeatures: !1,
        })
      )
      window.viewer.imageryLayers.add(guge)
      // 去除时间原因影响模型颜色
      window.viewer.scene.light = new Cesium.DirectionalLight({
        //   //去除时间原因影响模型颜色
        direction: new Cesium.Cartesian3(
          0.35492591601301104,
          -0.8909182691839401,
          -0.2833588392420772
        ),
      })
      window.viewer.clock.currentTime = new Cesium.JulianDate(
        2458047,
        27399.860215000022
      )
      window.viewer.cesiumWidget.screenSpaceEventHandler.removeInputAction(
        Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK
      )
      // this.eye()
      this.$emit('sendViewer', viewer)
      this.clickFeatures()
      this.doubleClickFeatures()
    },
    positionStation(list) {
      flyTos({
        lon: list[0] + 0.000226,
        lat: list[1] - 0.001832,
        height: 200,
        viewer: window.viewer,
        heading: 353.34,
        pitch: -40.66,
        roll: 0.0,
      })
    },
    //定位
    location(val) {
      {
        this.mimoSwitch = false
        heatMapjieshou ? viewer.entities.remove(heatMapjieshou) : ''
        heatMapjieshou = null
        this.value = false
        this.tableData = []
        if (ripple) {
          viewer.entities.remove(ripple)
        }
        if (mimos.length > 0) {
          mimos.forEach((item) => {
            viewer.scene.primitives.remove(item)
          })
        }
      }   //  初始化

      flyTos({
        lon: val.Longitude || val.lon + 0.000226,
        lat: val.Latitude || val.lat - 0.001832,
        height: 200,
        viewer: viewer,
        heading: 353.34,
        pitch: -40.66,
        roll: 0.0,
      })
      this.loadEllipse({
        lon: val.Longitude || val.lon,
        lat: val.Latitude || val.lat,
        alt: 2,
        semiMinorAxis: 100.0,
        semiMajorAxis: 100.0,
      })
      this.loadMIMO(val.Site, val.Height, 3)
      let SiteantennaNum = []
      antennaArray.forEach((item) => {
        if (item.properties.Site == val.Site) {
          SiteantennaNum.push(item.properties.Transmitte)
        }
      })
      jizhanshuju.forEach((item) => {
        if (val.Site == item.properties.Site) {
          this.query_StationID = item.properties.Site
          this.tableData.push(
            {
              key: '基站ID',
              value: item.properties.Site,
            },
            {
              key: '天线挂高',
              value: item.properties.Height,
            },
            {
              key: '天线数',
              value: SiteantennaNum.length,
            },
            {
              key: '基站类型',
              value: item.properties.安装方,
            }
          )
        }
      })
      this.$emit('setStationInfo', this.query_TianxianID, this.tableData)
      this.zhandianShow = true
      this.azimuthShow = false
    },
    //展开波束
    openMIMO(b) {
      if (b) {
        tianxianshuju.forEach((item) => {
          this.loadMIMO(item.properties.Transmitte, item.properties.Height)
        })
        // this.showFs2 = '关闭'
      } else {
        if (mimos.length > 0) {
          mimos.forEach((item) => {
            viewer.scene.primitives.remove(item)
          })
        }
        // this.showFs2 = '开启'
      }
    },
    //加载西安模型
    loadXatiles(url) {
      const titles = new Cesium.Cesium3DTileset({
        url: url,
        maximumScreenSpaceError: 5, //用于驱动细节细化级别的最大屏幕空间误差。 默认16
        maximumMemoryUsage: 2048 * 2, //瓦片集可以使用的最大内存量（以 MB 为单位）。 默认512
        cullWithChildrenBounds: false, //优化选项。是否使用子边界体积的并集来剔除瓦片。默认true
        cullRequestsWhileMoving: false, //优化选项。不要请求由于相机移动而在返回时可能未使用的图块。这种优化只适用于静止的瓦片集。默认true
        cullRequestsWhileMovingMultiplier: 60.0, //优化选项。移动时用于剔除请求的乘数。较大的是更积极的剔除，较小的较不积极的剔除。 默认值60
        preloadWhenHidden: true, //tileset.show时 预加载瓷砖false。加载图块，就好像图块集可见但不渲染它们。 默认false
        preloadFlightDestinations: true, //优化选项。在相机飞行时在相机的飞行目的地预加载图块。。 默认true
        preferLeaves: true, //优化选项 最好先装载叶子。 默认false
        dynamicScreenSpaceError: true, //优化选项。减少距离相机较远的图块的屏幕空间错误。 默认false
        dynamicScreenSpaceErrorDensity: 0.00278, //用于调整动态屏幕空间误差的密度，类似于雾密度。
        skipLevelOfDetail: true,
        baseScreenSpaceError: 2048 * 2,
        skipScreenSpaceErrorFactor: 1,
        skipLevels: 0,
        immediatelyLoadDesiredLevelOfDetail: true,
        loadSiblings: false,
        // shadows: Cesium.ShadowMode.ENABLED,
        show: true,
      })
      // let shows;
      // this.titlesID.forEach((item) => {
      //   let show = "${id} !==" + `"${item}" && `;
      //   shows += show;
      // });
      // let newString = shows.replace("undefined", "");
      // let newStrings = newString.slice(0, newString.length - 3);
      titles.style = new Cesium.Cesium3DTileStyle({
        color: {
          conditions: [
            // ['${HEIGHT} <= 20', `color("#EDFC2A")`],
            ['true', "color('#ffffff',1)"],
          ],
        },
      })
      viewer.scene.primitives.add(titles)
      viewer.zoomTo(titles)
      // flyTos({
      //   lon: 108.959,
      //   lat: 34.2054,
      //   height: 10,
      //   viewer: viewer,
      //   heading: 0,
      //   pitch: -8,
      //   roll: 0,
      // })
      // viewer.zoomTo(titles)
      titles.readyPromise.then(function (e) {
        this.loading = false
        var heightOffset = 5 //高度
        var boundingSphere = e.boundingSphere
        var cartographic = Cesium.Cartographic.fromCartesian(
          boundingSphere.center
        )
        var surface = Cesium.Cartesian3.fromRadians(
          cartographic.longitude,
          cartographic.latitude,
          0.0
        )
        var lon = Cesium.Math.toDegrees(cartographic.longitude)
        var lat = Cesium.Math.toDegrees(cartographic.latitude)
        var offset = Cesium.Cartesian3.fromDegrees(lon, lat, heightOffset)
        var translation = Cesium.Cartesian3.subtract(
          offset,
          surface,
          new Cesium.Cartesian3()
        )
        e.modelMatrix = Cesium.Matrix4.fromTranslation(translation)
      })
      xianModel.push(titles)
      // titles.tileVisible.addEventListener(function (tile) {
      //   var content = tile.content;
      //   var featuresLength = content.featuresLength;
      //   for (let i = 0; i < featuresLength; i += 2) {
      //     let feature = content.getFeature(i)
      //     let model = feature.content._model

      //     if (model && model._sourcePrograms && model._rendererResources) {
      //       Object.keys(model._sourcePrograms).forEach(key => {
      //         let program = model._sourcePrograms[key]
      //         let fragmentShader = model._rendererResources.sourceShaders[program.fragmentShader];
      //         let v_position = "";
      //         if (fragmentShader.indexOf(" v_positionEC;") != -1) {
      //           v_position = "v_positionEC";
      //         } else if (fragmentShader.indexOf(" v_pos;") != -1) {
      //           v_position = "v_pos";
      //         }
      //         const color = `vec4(${feature.color.toString()})`;

      //         model._rendererResources.sourceShaders[program.fragmentShader] =
      //           `
      //       varying vec3 ${v_position};
      //       void main(void){
      //         vec4 position = czm_inverseModelView * vec4(${v_position},1); // 位置
      //         gl_FragColor = ${color}; // 颜色
      //         gl_FragColor *= vec4(vec3(position.z / 12.0), 1.0); // 渐变
      //         // 动态光环
      //         float time = fract(czm_frameNumber / 60.0);
      //         time = abs(time - 0.5) * 3.0;
      //         float glowRange = 60.0; // 光环的移动范围(高度)
      //         float diff = step(0.03, abs( clamp(position.z / glowRange, 0.0, 1.0) - time));
      //         gl_FragColor.rgb += gl_FragColor.rgb * (1.0 - diff);
      //       }
      //     `
      //       })
      //       model._shouldRegenerateShaders = true
      //     }
      //   }
      // });
    },
    //加载geojson
    xianPolygon(pmaras) {
      let getCustomMaterialLine = (image, color) => {
        return new Cesium.CustomMaterialLine({
          image: image,
          color: color,
          duration: 1000,
        })
      }
      promise6 = Cesium.GeoJsonDataSource.load(pmaras.url, {
        clampToGround: false,
        fill: Cesium.Color.DARGOLDENNROD,
      })
      promise6.then(function (dataSource) {
        viewer.dataSources.add(dataSource)
        var entities = dataSource.entities.values
        if (pmaras.name == '西安边界') {
          entities.forEach((item) => {
            item.polygon.material =
              Cesium.Color.fromCssColorString('#102F73').withAlpha(0.8)
            item.polygon.extrudedHeight = 0.3
            item.polygon.outline = false
            item.polygon.outlineColor = Cesium.Color.BLACK
            item.polygon.outlineWidth = 5
          })
        } else if (pmaras.name == '绿地') {
          entities.forEach((item) => {
            item.polygon.material =
              Cesium.Color.fromCssColorString('#3BD132').withAlpha(0.8)
            item.polygon.extrudedHeight = 0.6
            item.polygon.outline = false
            item.polygon.outlineColor = Cesium.Color.BLACK
            item.polygon.outlineWidth = 8
          })
        } else if (pmaras.name == '水系') {
          entities.forEach((item) => {
            item.polygon.material =
              Cesium.Color.fromCssColorString('#3284D1').withAlpha(0.8)
            item.polygon.extrudedHeight = 0.6
            item.polygon.outline = false
            item.polygon.outlineColor = Cesium.Color.BLACK
            item.polygon.outlineWidth = 8
          })
        } else {
          entities.forEach((item) => {
            // item.polyline.positions._value.forEach(itemSon => {
            //   const cartesian3 = new Cesium.Cartesian3(itemSon.x, itemSon.y, itemSon.z);
            //   const cartographic = viewer.scene.globe.ellipsoid.cartesianToCartographic(cartesian3);
            //   const lat = Cesium.Math.toDegrees(cartographic.latitude);
            //   const lng = Cesium.Math.toDegrees(cartographic.longitude);
            //   const alt = cartographic.height + 15;
            //   itemSon = Cesium.Cartesian3.fromDegrees(lng, lat, alt)
            //   // item.polyline._positions._value.push(Cesium.Cartesian3.fromDegrees(lng, lat, alt))
            // })
            item.polyline.material = getCustomMaterialLine(
              colorss,
              Cesium.Color.fromCssColorString('#F47A1A').withAlpha(0.7)
            )
            item.polyline.width = 2
            item.polyline.glowPower = 1
          })
        }
      })
    },
    removezhengzhouZGD() {
      let removePromise7
      promise6.then((res) => {
        removePromise7 = res
      })
      viewer.dataSources.remove(removePromise7)
    },
    //大唐不夜城边界
    loadWall(wallCanshu) {
      let drawWall = viewer.entities.add({
        name: '立体墙',
        wall: {
          positions: Cesium.Cartesian3.fromDegreesArray(wallCanshu.positions),
          // 设置高度
          maximumHeights: new Array(wallCanshu.positions.length / 2).fill(
            wallCanshu.maxheight
          ),
          minimumHeights: new Array(wallCanshu.positions.length / 2).fill(
            wallCanshu.minheight
          ),
          material: new Cesium.DynamicWallMaterialProperty({
            color: Cesium.Color.fromCssColorString(wallCanshu.color).withAlpha(
              0.8
            ),
            duration: wallCanshu.duration,
          }),
        },
      })
    },
    //加载大唐不夜城水面
    loadWater(pmaras) {
      let polygonMaterial = new Cesium.EllipsoidSurfaceAppearance({
        material: new Cesium.Material({
          fabric: {
            type: 'Water',
            uniforms: {
              // baseWaterColor: new Cesium.Color(173 / 255, 165 / 255, 125 / 255, 0.5),  灰色
              baseWaterColor: new Cesium.Color(
                81 / 255,
                195 / 255,
                225 / 255,
                0.6
              ),
              // specularMap: water,
              normalMap: waterNormals,
              frequency: 100.0, //振幅
              animationSpeed: 0.02, //频率
              amplitude: 10.0,
              specularIntensity: 5, //反光
              time: 20,
            },
          },
        }),
        // fragmentShaderSource: fs,
        fragmentShaderSource:
          'varying vec3 v_positionMC;\n' +
          'varying vec3 v_positionEC;\n' +
          'varying vec2 v_st;\n' +
          'void main()\n' +
          '{\n' +
          'czm_materialInput materialInput;\n' +
          'vec3 normalEC = normalize(czm_normal3D * czm_geodeticSurfaceNormal(v_positionMC, vec3(0.0), vec3(1.0)));\n' +
          '#ifdef FACE_FORWARD\n' +
          'normalEC = faceforward(normalEC, vec3(0.0, 0.0, 1.0), -normalEC);\n' +
          '#endif\n' +
          'materialInput.s = v_st.s;\n' +
          'materialInput.st = v_st;\n' +
          'materialInput.str = vec3(v_st, 0.0);\n' +
          'materialInput.normalEC = normalEC;\n' +
          'materialInput.tangentToEyeMatrix = czm_eastNorthUpToEyeCoordinates(v_positionMC, materialInput.normalEC);\n' +
          'vec3 positionToEyeEC = -v_positionEC;\n' +
          'materialInput.positionToEyeEC = positionToEyeEC;\n' +
          'czm_material material = czm_getMaterial(materialInput);\n' +
          '#ifdef FLAT\n' +
          'gl_FragColor = vec4(material.diffuse + material.emission, material.alpha);\n' +
          '#else\n' +
          'gl_FragColor = czm_phong(normalize(positionToEyeEC), material, czm_lightDirectionEC);\n' +
          'gl_FragColor.a=0.6;\n' +
          '#endif\n' +
          '}\n',
        vertexShader: [
          '#include <common>',
          '#include <fog_pars_vertex>',
          '#include <logdepthbuf_pars_vertex>',

          'uniform mat4 textureMatrix;',

          'varying vec4 vCoord;',
          'varying vec2 vUv;',
          'varying vec3 vToEye;',

          'void main() {',

          'vUv = uv;',
          'vCoord = textureMatrix * vec4( position, 1.0 );',

          'vec4 worldPosition = modelMatrix * vec4( position, 1.0 );',
          'vToEye = cameraPosition - worldPosition.xyz;',

          'vec4 mvPosition =  viewMatrix * worldPosition;', // used in fog_vertex
          'gl_Position = projectionMatrix * mvPosition;',

          '#include <logdepthbuf_vertex>',
          '#include <fog_vertex>',

          '}',
        ].join('\n'),
      })
      viewer.scene.primitives.add(
        new Cesium.Primitive({
          geometryInstances: new Cesium.GeometryInstance({
            geometry: new Cesium.PolygonGeometry({
              polygonHierarchy: new Cesium.PolygonHierarchy(
                Cesium.Cartesian3.fromDegreesArrayHeights(
                  pmaras.polygonHierarchy
                )
              ),
              height: 0.36,
            }),
          }),
          appearance: polygonMaterial, //自定义appearance
          show: true,
        })
      )
    },
    //大屏
    addVideo(pmaras) {
      let videoElement = document.getElementById('daolu')
      videoElement.play()
      viewer.showRenderLoopErrors = false
      viewer.shouldAnimate = true
      let wall = viewer.entities.add({
        name: '立体墙效果',
        wall: {
          positions: Cesium.Cartesian3.fromDegreesArray(pmaras.positions),
          // 设置高度
          maximumHeights: new Array(pmaras.positions.length / 2).fill(
            pmaras.maxheight
          ),
          minimumHeights: new Array(pmaras.positions.length / 2).fill(
            pmaras.minheight
          ),
          material: videoElement,
        },
      })
      walls.push(wall)
    },
    //电平信号覆盖分析
    pntsShow(v) {
      if (!this.analyse && v) {
        this.loadPnts(window.tileset.pointCloud)
        this.icon.icon6 = !this.icon.icon6
        this.analyse = true
        flyTos({
          lon: 108.927148,
          lat: 34.069055,
          height: 16923.95,
          viewer: window.viewer,
          heading: 0.54,
          pitch: -41.02,
          roll: 360,
        })
      } else {
        viewer.scene.primitives.remove(tileset)
        this.analyse = false
        this.icon.icon6 = !this.icon.icon6
        this.qiegemishow = false
        this.RSRPTitleName = 'RSRP'
      }
    },
    bycModelShow() {
      if (this.modelName == '大唐不夜城') {
        if (xianModel.length > 0) {
          xianModel.forEach((item) => {
            viewer.scene.primitives.remove(item)
          })
        }
        this.addVideo({
          positions: [
            108.95895930893968, 34.20779114709436, 108.95895930779104,
            34.20793121528859,
          ],
          maxheight: 17.82,
          minheight: 9.37,
        }) //大屏广告
        this.addVideo({
          positions: [
            108.95913745720488, 34.215752008530345, 108.95913746554546,
            34.216061754395795,
          ],
          maxheight: 14.04,
          minheight: 0,
        })
        this.addVideo({
          positions: [
            108.95835733808336, 34.21815346493692, 108.95835735954324,
            34.218252044515744,
          ],
          maxheight: 17.95,
          minheight: 5.79,
        })
        this.addVideo({
          positions: [
            108.95835753335838, 34.217638899149506, 108.9583573778265,
            34.21773746688779,
          ],
          maxheight: 17.95,
          minheight: 5.79,
        })
        this.addVideo({
          positions: [
            108.95835737634978, 34.218524674465144, 108.95835733069289,
            34.218623277673025,
          ],
          maxheight: 17.95,
          minheight: 5.79,
        })
        this.addVideo({
          positions: [
            108.95835734421509, 34.21882496018736, 108.95835731090727,
            34.21892355233954,
          ],
          maxheight: 17.95,
          minheight: 5.79,
        })
        this.addVideo({
          positions: [
            108.95833542301898, 34.21779433123705, 108.95833544114247,
            34.218034330150225,
          ],
          maxheight: 21.11,
          minheight: 5.59,
        })
        this.loadXatiles(
          'http://*************:59083/models/3dmodels/XIANBYC_Model/tileset.json'
        )
        this.loadXatiles(
          'http://*************:59083/models/3dmodels/Xianrim/tileset.json'
        )
        this.loadXatiles(
          'http://*************:59083/models/3dmodels/XIANBYCTree/tileset.json'
        )
        //加载水面
        // WaterPnts.forEach(item => {
        //   this.loadWater({
        //     polygonHierarchy: item,
        //   })
        // })
        this.modelName = '西安白膜'
      } else {
        if (xianModel.length > 0) {
          xianModel.forEach((item) => {
            viewer.scene.primitives.remove(item)
          })
        }
        if (walls.length > 0) {
          walls.forEach((item) => {
            viewer.entities.remove(item)
          })
        }
        this.loadXatiles(window.tileset.XAtileset)
        this.modelName = '大唐不夜城'
      }
    },
    //加载电平数据
    loadPnts(url) {
      var viewModel = {
        debugBoundingVolumesEnabled: false,
        edgeStylingEnabled: false,
        // exampleTypes: clipObjects,
        // currentExampleType: clipObjects[0],
      }
      this.loading = false
      let that = this
      let selectedPlane
      let clippingPlanes
      clippingPlanes = new Cesium.ClippingPlaneCollection({
        planes: [
          new Cesium.ClippingPlane(new Cesium.Cartesian3(0.0, -1.0, 0.0), 0.0),
          new Cesium.ClippingPlane(new Cesium.Cartesian3(1.0, 0.0, 0.0), 0.0),
          new Cesium.ClippingPlane(new Cesium.Cartesian3(0.0, 0.0, -1.0), 0.0),
          // new Cesium.ClippingPlane(new Cesium.Cartesian3(0.0, 0.0, 1.0), 0.0),
        ],
        edgeWidth: viewModel.edgeStylingEnabled ? 1.0 : 0.0,
      })
      tileset = new Cesium.Cesium3DTileset({
        url: url,
        clippingPlanes: clippingPlanes,
        pointCloudShading: {
          attenuation: true,
          maximumAttenuation: 2,
        },
        show: true,
      })
      tileset.style = new Cesium.Cesium3DTileStyle({
        color: {
          conditions: [
            [
              '${RSRP} >=' +
              `${rrp.rrp_10[0]}` +
              '&& ${RSRP} <=' +
              `${rrp.rrp_10[1]}`,
              "color('#0000FF',0.3)",
            ],
            [
              '${RSRP} >=' +
              `${rrp.rrp_9[0]}` +
              '&& ${RSRP} <=' +
              `${rrp.rrp_9[1]}`,
              "color('#008FFF',0.3)",
            ],
            [
              '${RSRP} >=' +
              `${rrp.rrp_8[0]}` +
              '&& ${RSRP} <=' +
              `${rrp.rrp_8[1]}`,
              "color('#00FFA5',0.3)",
            ],
            [
              '${RSRP} >=' +
              `${rrp.rrp_7[0]}` +
              '&& ${RSRP} <=' +
              `${rrp.rrp_7[1]}`,
              "color('#00FF4B',0.3)",
            ],
            [
              '${RSRP} >=' +
              `${rrp.rrp_6[0]}` +
              '&& ${RSRP} <=' +
              `${rrp.rrp_6[1]}`,
              "color('#75FF00',0.3)",
            ],
            [
              '${RSRP} >=' +
              `${rrp.rrp_5[0]}` +
              '&& ${RSRP} <=' +
              `${rrp.rrp_5[1]}`,
              "color('#D0FF00',0.3)",
            ],
            [
              '${RSRP} >=' +
              `${rrp.rrp_4[0]}` +
              '&& ${RSRP} <=' +
              `${rrp.rrp_4[1]}`,
              "color('#FFF200',0.3)",
            ],
            [
              '${RSRP} >=' +
              `${rrp.rrp_3[0]}` +
              '&& ${RSRP} <=' +
              `${rrp.rrp_3[1]}`,
              "color('#FFAB00',0.3)",
            ],
            [
              '${RSRP} >=' +
              `${rrp.rrp_2[0]}` +
              '&& ${RSRP} <=' +
              `${rrp.rrp_2[1]}`,
              "color('#FF6F00',0.3)",
            ],
            [
              '${RSRP} >=' +
              `${rrp.rrp_1[0]}` +
              '&& ${RSRP} <=' +
              `${rrp.rrp_1[1]}`,
              "color('#FF0000',0.3)",
            ],
            ['true', "color('#FF0000',0.3)"],
          ],
        },
        pointSize: 5,
      })
      viewer.scene.primitives.add(tileset)

      tileset.debugShowBoundingVolume = viewModel.debugBoundingVolumesEnabled
      tileset.readyPromise
        .then((tilesets) => {
          // style.readyPromise.then(() => {
          //   tilesets.style = style
          // })

          this.loading = false
          var boundingSphere = tileset.boundingSphere
          var radius = boundingSphere.radius
          const cartesian3 = new Cesium.Cartesian3(
            boundingSphere.center.x,
            boundingSphere.center.y,
            boundingSphere.center.z
          )
          const cartographic =
            viewer.scene.globe.ellipsoid.cartesianToCartographic(cartesian3)
          const lat = Cesium.Math.toDegrees(cartographic.latitude)
          const lng = Cesium.Math.toDegrees(cartographic.longitude)
          const alt = cartographic.height
          if (
            !Cesium.Matrix4.equals(
              tileset.root.transform,
              Cesium.Matrix4.IDENTITY
            )
          ) {
            // 裁剪平面最初定位在瓷砖的根变换处。
            //应用一个额外的矩阵使裁剪平面在边界球中心居中。
            var transformCenter = Cesium.Matrix4.getTranslation(
              tileset.root.transform,
              new Cesium.Cartesian3()
            )
            var transformCartographic =
              Cesium.Cartographic.fromCartesian(transformCenter)
            var boundingSphereCartographic = Cesium.Cartographic.fromCartesian(
              tileset.boundingSphere.center
            )
            var height =
              boundingSphereCartographic.height - transformCartographic.height
            clippingPlanes.modelMatrix = Cesium.Matrix4.fromTranslation(
              new Cesium.Cartesian3(0.0, 0.0, height)
            )
          }
          var plane = clippingPlanes.get(0)
          var planeEntity = viewer.entities.add({
            position: Cesium.Cartesian3.fromDegrees(lng, lat, alt),
            plane: {
              dimensions: new Cesium.Cartesian2(radius * 1.5, radius * 1.5),
              material: Cesium.Color.WHITE.withAlpha(0),
              plane: new Cesium.CallbackProperty(
                createPlaneUpdateFunction(plane),
                false
              ),
              outline: false,
              outlineColor: Cesium.Color.WHITE.withAlpha,
            },
          })
          planeEntities.push(planeEntity)
          //纵面
          var plane1 = clippingPlanes.get(1)
          var planeEntity1 = viewer.entities.add({
            position: Cesium.Cartesian3.fromDegrees(lng, lat, alt),
            plane: {
              dimensions: new Cesium.Cartesian2(radius * 1.5, radius * 1.5),
              material: Cesium.Color.WHITE.withAlpha(0),
              plane: new Cesium.CallbackProperty(
                createPlaneUpdateFunction2(plane1),
                false
              ),
              outline: false,
              outlineColor: Cesium.Color.WHITE.withAlpha,
            },
          })
          planeEntities.push(planeEntity1)
          //水平面
          var plane2 = clippingPlanes.get(2)
          var planeEntity2 = viewer.entities.add({
            position: Cesium.Cartesian3.fromDegrees(lng, lat, alt),
            plane: {
              dimensions: new Cesium.Cartesian2(radius * 5, radius * 5),
              material: Cesium.Color.WHITE.withAlpha(0),
              plane: new Cesium.CallbackProperty(
                createPlaneUpdateFunction3(plane2),
                false
              ),
              outline: false,
              outlineColor: Cesium.Color.WHITE.withAlpha,
            },
          })
          planeEntities.push(planeEntity2)

          return tileset
        })
        .otherwise(function (error) {
          console.log(error)
        })

      var downHandler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas)
      downHandler.setInputAction(function (movement) {
        var pickedObject = viewer.scene.pick(movement.position)
        if (
          Cesium.defined(pickedObject) &&
          Cesium.defined(pickedObject.id) &&
          Cesium.defined(pickedObject.id.plane)
        ) {
          selectedPlane = pickedObject.id.plane
          selectedPlane.material = Cesium.Color.WHITE.withAlpha(0)
          selectedPlane.outlineColor = Cesium.Color.WHITE
          viewer.scene.screenSpaceCameraController.enableInputs = false
        }
      }, Cesium.ScreenSpaceEventType.LEFT_DOWN)

      // Release plane on mouse up
      var upHandler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas)
      upHandler.setInputAction(function () {
        if (Cesium.defined(selectedPlane)) {
          selectedPlane.material = Cesium.Color.WHITE.withAlpha(0)
          selectedPlane.outlineColor = Cesium.Color.WHITE
          selectedPlane = undefined
        }
        viewer.scene.screenSpaceCameraController.enableInputs = true
      }, Cesium.ScreenSpaceEventType.LEFT_UP)
      // var moveHandler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas)
      // moveHandler.setInputAction(function (movement) {
      //   if (Cesium.defined(selectedPlane)) {
      //     var deltaY = movement.startPosition.x - movement.endPosition.x
      //     targetY += deltaY
      //     console.log(targetY)
      //   }
      // }, Cesium.ScreenSpaceEventType.MOUSE_MOVE)
      function createPlaneUpdateFunction(plane) {
        return function () {
          plane.distance = targetY
          return plane
        }
      }
      function createPlaneUpdateFunction2(plane) {
        return function () {
          plane.distance = targetY2
          return plane
        }
      }
      function createPlaneUpdateFunction3(plane) {
        return function () {
          plane.distance = targetY3
          return plane
        }
      }
    },
    //横向切割
    crosswise(val) {
      var pickedObject = planeEntities[0].plane
      pickedObject.material = Cesium.Color.WHITE.withAlpha(0)
      pickedObject.outlineColor = Cesium.Color.WHITE
      targetY = val
      targetY2 = this.slider2_min
      targetY3 = this.slider3_min
      this.crosswiseMI = val + 6700
      this.qiegemishow = false
      this.$refs.xinhaoValue.crosswise = val
      this.RSRPTitleName = 'RSRP-横向切割' + this.crosswiseMI + 'm'
    },
    //纵向切割
    lengthways(val) {
      var pickedObject = planeEntities[1].plane
      pickedObject.material = Cesium.Color.WHITE.withAlpha(0)
      pickedObject.outlineColor = Cesium.Color.WHITE
      targetY2 = val
      targetY = this.slider1_min
      targetY3 = this.slider3_min
      this.lengthwaysMI = val + 8615
      this.qiegemishow = false
      this.$refs.xinhaoValue.lengthways = val
      this.RSRPTitleName = 'RSRP-纵向切割' + this.lengthwaysMI + 'm'
    },
    //水平切割
    vertical(val) {
      var pickedObject = planeEntities[2].plane
      pickedObject.material = Cesium.Color.WHITE.withAlpha(0)
      pickedObject.outlineColor = Cesium.Color.WHITE
      targetY3 = val
      targetY = this.slider1_min
      targetY2 = this.slider2_min
      this.showMI = val + 40
      this.qiegemishow = true
      this.$refs.xinhaoValue.vertical = val
      this.RSRPTitleName = 'RSRP-水平切割' + this.showMI + 'm'
    },
    pointSize(val) {
      this.pointsSize = val
      this.qiegemishow = false
      // console.log(tile);
      tileset.style = new Cesium.Cesium3DTileStyle({
        color: {
          conditions: [
            [
              '${RSRP} >=' +
              `${rrp.rrp_10[0]}` +
              '&& ${RSRP} <=' +
              `${rrp.rrp_10[1]}`,
              "color('#0000FF',0.3)",
            ],
            [
              '${RSRP} >=' +
              `${rrp.rrp_9[0]}` +
              '&& ${RSRP} <=' +
              `${rrp.rrp_9[1]}`,
              "color('#008FFF',0.3)",
            ],
            [
              '${RSRP} >=' +
              `${rrp.rrp_8[0]}` +
              '&& ${RSRP} <=' +
              `${rrp.rrp_8[1]}`,
              "color('#00FFA5',0.3)",
            ],
            [
              '${RSRP} >=' +
              `${rrp.rrp_7[0]}` +
              '&& ${RSRP} <=' +
              `${rrp.rrp_7[1]}`,
              "color('#00FF4B',0.3)",
            ],
            [
              '${RSRP} >=' +
              `${rrp.rrp_6[0]}` +
              '&& ${RSRP} <=' +
              `${rrp.rrp_6[1]}`,
              "color('#75FF00',0.3)",
            ],
            [
              '${RSRP} >=' +
              `${rrp.rrp_5[0]}` +
              '&& ${RSRP} <=' +
              `${rrp.rrp_5[1]}`,
              "color('#D0FF00',0.3)",
            ],
            [
              '${RSRP} >=' +
              `${rrp.rrp_4[0]}` +
              '&& ${RSRP} <=' +
              `${rrp.rrp_4[1]}`,
              "color('#FFF200',0.3)",
            ],
            [
              '${RSRP} >=' +
              `${rrp.rrp_3[0]}` +
              '&& ${RSRP} <=' +
              `${rrp.rrp_3[1]}`,
              "color('#FFAB00',0.3)",
            ],
            [
              '${RSRP} >=' +
              `${rrp.rrp_2[0]}` +
              '&& ${RSRP} <=' +
              `${rrp.rrp_2[1]}`,
              "color('#FF6F00',0.3)",
            ],
            [
              '${RSRP} >=' +
              `${rrp.rrp_1[0]}` +
              '&& ${RSRP} <=' +
              `${rrp.rrp_1[1]}`,
              "color('#FF0000',0.3)",
            ],
            ['true', "color('#FF0000',0.3)"],
          ],
        },
        pointSize: val,
      })
    },
    //加载仿真数据
    async loadXinhao() {
      if (!this.loadText) {
        this.clickFeatures({
          coverage: await this.getXqCoverage_area(),
          facade_pnts: await this.getFacade_pnts(),
          rsrp: await this.getRsrp(),
          antenna: await this.getTxJSON(),
        }) //点击
      }
    },
    //加载车
    load_car() {
      if (this.icon.icon7 && this.loadingModel == false && !this.icon.icon1) {
        this.dialogVisible = true //放置车辆弹框
      } else {
        this.$message.error('数据加载完成后操作！')
      }
      if (this.icon.icon1) {
        this.modelTiaozheng = false
        viewer.scene.primitives.remove(car1)
        viewer1.scene.primitives.remove(car2)
        lines.forEach((item) => {
          viewer.entities.remove(item)
        })
        pnts.forEach((item) => {
          viewer.entities.remove(item)
        })
        xinhaolines.forEach((item) => {
          viewer.entities.remove(item)
        })
        this.icon.icon1 = !this.icon.icon1
      }
    },
    //请求小区覆盖范围
    getXqCoverage_area() {
      return new Promise((resolve, reject) => {
        this.$http
          .get('http://*************:59083/models/JSON/xiaoqufugai.json')
          .then((res) => {
            if (res) {
              resolve(res.data)
            }
          })
          .catch((error) => {
            resolve('请求小区覆盖范围请求失败')
          })
      })
    },
    //请求天线安装位置
    getTxJSON() {
      return new Promise((resolve, reject) => {
        this.$http
          .get(
            'http://*************:59083/models/JSON/zhandian/transmitter1.json'
          )
          .then((res) => {
            if (res) {
              resolve(res.data.features)
            }
          })
          .catch((error) => {
            resolve('请求天线安装位置失败')
          })
      })
    },
    //请求立面点
    getFacade_pnts() {
      return new Promise((resolve, reject) => {
        this.$http
          .get('http://*************:59083/models/JSON/limiandian.json')
          .then((res) => {
            if (res) {
              resolve(res.data)
            }
          })
          .catch((error) => {
            resolve('立面点请求失败')
          })
      })
    },
    //请求rsrp
    getRsrp() {
      return new Promise((resolve, reject) => {
        this.$http
          .get('http://*************:59083/models/JSON/rsrp.json')
          .then((res) => {
            if (res) {
              resolve(res.data)
            }
          })
          .catch((error) => {
            resolve('rsrp请求失败')
          })
      })
    },
    //绘制线
    drawLine(pmaras) {
      // perVertexPolyline = new Cesium.GeometryInstance({
      //   geometry: new Cesium.SimplePolylineGeometry({
      //     positions: Cesium.Cartesian3.fromDegreesArrayHeights(fromDegreesArrayHeights),
      //     colors: colors,
      //     colorsPerVertex: true,
      //   }),
      // })
      // viewer.scene.primitives.add(
      //   new Cesium.Primitive({
      //     geometryInstances: [perVertexPolyline],
      //     appearance: new Cesium.PerInstanceColorAppearance({
      //       flat: true,
      //       renderState: {
      //         lineWidth: Math.min(10.0, viewer.scene.maximumAliasedLineWidth),
      //       },
      //     }),
      //   })
      // )
      perVertexPolyline = viewer.scene.primitives.add(
        new Cesium.Primitive({
          geometryInstances: new Cesium.GeometryInstance({
            geometry: new Cesium.PolylineGeometry({
              positions: Cesium.Cartesian3.fromDegreesArrayHeights(
                pmaras.fromDegreesArrayHeights
              ),
              width: 10.0,
              vertexFormat: Cesium.PolylineColorAppearance.VERTEX_FORMAT,
              colors: pmaras.colors,
              colorsPerVertex: true,
            }),
          }),
          appearance: new Cesium.PolylineColorAppearance(),
        })
      )
    },
    //放置车
    placeModel() {
      this.$message('请在地图上指定模型位置')
      this.dialogVisible = false
      this.modelTiaozheng = true
      this.icon.icon1 = !this.icon.icon1
      if (this.form.type == '线') {
        this.switchingStatus = false
      } else {
        this.switchingStatus = true
      }
    },
    //键盘控制车
    keyboard_car(params) {
      let that = this
      //控制车的初始连线
      this.loadLine([
        {
          data: [
            params.lng,
            params.lat,
            params.height,
            params.lng,
            params.lat,
            params.height,
          ],
          color: '#FC6B09',
        },
        {
          data: [
            params.lng,
            params.lat,
            params.height,
            params.lng,
            params.lat,
            params.height,
          ],
          color: '#15BBDC',
        },
        {
          data: [
            params.lng,
            params.lat,
            params.height,
            params.lng,
            params.lat,
            params.height,
          ],
          color: '#EF2323',
        },
        {
          data: [
            params.lng,
            params.lat,
            params.height,
            params.lng,
            params.lat,
            params.height,
          ],
          color: '#E8EF23',
        },
        {
          data: [
            params.lng,
            params.lat,
            params.height,
            params.lng,
            params.lat,
            params.height,
          ],
          color: '#6FEF23',
        },
      ])
      let newStr = [params.lng, params.lat, params.height]
      // 小车旋转角度
      let radian = Cesium.Math.toRadians(2)
      // 小车的速度
      let speed = parseFloat(this.form.speed)
      // 速度矢量
      let speedVector = new Cesium.Cartesian3()
      let scene = viewer.scene
      // 起始位置
      let position = Cesium.Cartesian3.fromDegrees(
        newStr[0],
        newStr[1],
        newStr[2]
      )
      // 用于设置小车方向
      let heading = Cesium.Math.toRadians(0)
      let pitch = 0
      let roll = 0
      let hpRoll = new Cesium.HeadingPitchRoll(heading, pitch, roll)
      let fixedFrameTransforms =
        Cesium.Transforms.localFrameToFixedFrameGenerator('north', 'west')
      // Cesium.Transforms.localFrameToFixedFrameGenerator("south", "east");
      var carPrimitive = scene.primitives.add(
        Cesium.Model.fromGltf({
          id: 'mycar',
          url:
            this.form.model == '车'
              ? 'http://*************:59083/models/monomer_model/GLTF/3Dmodel/tesla_2018_model_3/tesla_2018_model.gltf'
              : 'http://*************:59083/models/gltf/workman.gltf',
          modelMatrix: Cesium.Transforms.headingPitchRollToFixedFrame(
            position,
            hpRoll,
            Cesium.Ellipsoid.WGS84,
            fixedFrameTransforms
          ),
          scale: this.form.model == '车' ? 0.8 : 0.0003,
        })
      )
      // console.log(carPrimitive)

      carPrimitive.readyPromise.then((model) => {
        this.loadingModel = false
        this.$message.success('模型加载完成')
        model.activeAnimations.addAll({
          loop: Cesium.ModelAnimationLoop.REPEAT,
          speedup: 1, //速度
          reverse: false, //false顺时针  true逆时针
        })
        car1 = model
      })

      let carPrimitive1 = viewer1.scene.primitives.add(
        Cesium.Model.fromGltf({
          id: 'mycar',
          url: 'http://*************:59083/models/monomer_model/GLTF/3Dmodel/tsl_model3jh/tesla_2018_model.gltf',
          modelMatrix: Cesium.Transforms.headingPitchRollToFixedFrame(
            position,
            hpRoll,
            Cesium.Ellipsoid.WGS84,
            fixedFrameTransforms
          ),
          scale: 0.8,
          minimumPixelSize: 40,
          maximumPixelSize: 40,
        })
      )

      car2 = carPrimitive1

      // 小车状态标志
      let flag = {
        moveUp: false,
        moveDown: false,
        moveLeft: false,
        moveRight: false,
      }
      // 根据键盘按键返回标志
      function setFlagStatus(key, value) {
        switch (key.keyCode) {
          case 37:
            // 左
            flag.moveLeft = value
            break
          case 65:
            // 左
            flag.moveLeft = value
            break
          case 38:
            // 上
            flag.moveUp = value
            break
          case 87:
            flag.moveUp = value
            // 上
            break
          case 39:
            // 右
            flag.moveRight = value
            break
          case 68:
            // 右
            flag.moveRight = value
            break
          case 40:
            flag.moveDown = value
            // 下
            break
          case 83:
            flag.moveDown = value
            // 下
            break
        }
      }

      function setFlagStatus2(key) {
        switch (key.keyCode) {
          case 32:
            document.getElementById('manipulate').click()
            //空格
            break
        }
      }

      document.addEventListener('keyup', (e) => {
        setFlagStatus2(e)
      })

      document.addEventListener('keydown', (e) => {
        setFlagStatus(e, true)
      })

      document.addEventListener('keyup', (e) => {
        setFlagStatus(e, false)
      })
      // moveCar(true);
      var count = 0
      viewer.clock.onTick.addEventListener((clock) => {
        if (flag.moveUp) {
          if (flag.moveLeft) {
            hpRoll.heading -= radian
            count += 2
          }
          if (flag.moveRight) {
            hpRoll.heading += radian
            count -= 2
          }
          moveCar(1)
        } else if (flag.moveDown) {
          if (flag.moveLeft) {
            hpRoll.heading -= radian
            count += 2
          }
          if (flag.moveRight) {
            hpRoll.heading += radian
            count -= 2
          }
          moveCar(-1)
        } else {
          if (flag.moveLeft) {
            hpRoll.heading -= radian
            count += 2
            moveCar(0)
          }
          if (flag.moveRight) {
            hpRoll.heading += radian
            count -= 2
            moveCar(0)
          }
        }
      })
      that.hpRoll = hpRoll
      function moveCar(isUP) {
        // 计算速度矩阵
        if (isUP === 1) {
          speedVector = Cesium.Cartesian3.multiplyByScalar(
            Cesium.Cartesian3.UNIT_X,
            speed,
            speedVector
          )
        } else if (isUP === -1) {
          speedVector = Cesium.Cartesian3.multiplyByScalar(
            Cesium.Cartesian3.UNIT_X,
            -speed,
            speedVector
          )
        } else {
          speedVector = Cesium.Cartesian3.multiplyByScalar(
            Cesium.Cartesian3.UNIT_X,
            0,
            speedVector
          )
        }

        Cesium.Matrix4.multiplyByPoint(
          carPrimitive1.modelMatrix,
          speedVector,
          position
        )

        // 根据速度计算出下一个位置的坐标
        position = Cesium.Matrix4.multiplyByPoint(
          carPrimitive.modelMatrix,
          speedVector,
          position
        )

        // carPrimitive._cachedGltf._gltf.nodes.forEach(item => {
        //   if (item.name == 'movsteer_1.0_movsteer_1.0.0_0' || item.name == 'movsteer_1.0_dvorright.0_0') {
        //     console.log(carPrimitive);
        //     let oldMatrix = item.matrix
        //     let oldCenter = new Cesium.Cartesian3(oldMatrix[12], oldMatrix[13], oldMatrix[14]);
        //     const m1 = Cesium.Transforms.eastNorthUpToFixedFrame(Cesium.Matrix4.getTranslation(oldMatrix, new Cesium.Cartesian3()), Cesium.Ellipsoid.WGS84, new Cesium.Matrix4());
        //     const m3 = Cesium.Matrix4.multiply(Cesium.Matrix4.inverse(m1, new Cesium.Matrix4()), oldMatrix, new Cesium.Matrix4());
        //     const mat3 = Cesium.Matrix4.getMatrix3(m3, new Cesium.Matrix3());
        //     const q = Cesium.Quaternion.fromRotationMatrix(mat3);
        //     const hpr = Cesium.HeadingPitchRoll.fromQuaternion(q);
        //     console.log(hpr);
        //     let headingPitchRoll = new Cesium.HeadingPitchRoll(Cesium.Math.toRadians(hpr.heading += radian), Cesium.Math.toRadians(hpr.patch), Cesium.Math.toRadians(hpr.roll));
        //     let m = Cesium.Transforms.headingPitchRollToFixedFrame(oldCenter, headingPitchRoll, Cesium.Ellipsoid.WGS84, Cesium.Transforms.eastNorthUpToFixedFrame, new Cesium.Matrix4());
        //     console.log(m);
        //     item.matrix = m;
        //   }
        // })

        //连线
        let pont_xq = {}
        let llh_old
        let llh1, llh2, llh3, llh4
        let lengArrey = [] //储存车到墙的距离
        var ellipsoid = viewer.scene.globe.ellipsoid
        var cartesian3 = new Cesium.Cartesian3(
          position.x,
          position.y,
          position.z
        )
        var cartographic = ellipsoid.cartesianToCartographic(cartesian3)
        let lon = Cesium.Math.toDegrees(cartographic.longitude).toFixed(5)
        let lats = Cesium.Math.toDegrees(cartographic.latitude).toFixed(5)
        let llh = [
          parseFloat(Cesium.Math.toDegrees(cartographic.longitude).toFixed(5)),
          parseFloat(Cesium.Math.toDegrees(cartographic.longitude).toFixed(5)),
          parseFloat(59),
          parseFloat(lon),
          parseFloat(lats),
          parseFloat(cartographic.height.toFixed(0)),
        ]
        llh_old = llh
        //计算车辆是否进入小区覆盖区域
        params.xiaoqufugai.features.forEach((item) => {
          var pt = turf.point([lon, lats])
          let poly = turf.polygon(item.geometry.coordinates)
          if (turf.booleanPointInPolygon(pt, poly)) {
            if (that.largeScreenShow) {
              params.antenna.forEach((antennaItem) => {
                if (
                  item.properties.Transmitte ==
                  antennaItem.properties.Transmitte
                ) {
                  switch (item.properties.安装方式) {
                    case '楼顶抱杆':
                      if (parseFloat(antennaItem.properties.minheight)) {
                        pont_xq.lon = antennaItem.geometry.coordinates[0]
                        pont_xq.lat = antennaItem.geometry.coordinates[1]
                        pont_xq.height =
                          parseFloat(antennaItem.properties.minheight) +
                          2 +
                          0.68
                      } else {
                        pont_xq.lon = antennaItem.geometry.coordinates[0]
                        pont_xq.lat = antennaItem.geometry.coordinates[1]
                        pont_xq.height = 0 + 2 + 0.68
                      }
                      break
                    case '楼顶三角塔':
                      pont_xq.lon = antennaItem.geometry.coordinates[0]
                      pont_xq.lat = antennaItem.geometry.coordinates[1]
                      pont_xq.height = antennaItem.properties.Height + 0.68
                      break
                    case '单管塔':
                      pont_xq.lon =
                        Math.cos(
                          Cesium.Math.toRadians(
                            (antennaItem.properties.Azimuth - 90) * -1
                          )
                        ) *
                        0.00000398 +
                        antennaItem.geometry.coordinates[0]
                      pont_xq.lat =
                        Math.sin(
                          Cesium.Math.toRadians(
                            (antennaItem.properties.Azimuth - 90) * -1
                          )
                        ) *
                        0.00000398 +
                        antennaItem.geometry.coordinates[1]
                      pont_xq.height = antennaItem.properties.Height + 0.68 * 2
                      break
                    case '地面铁塔':
                      pont_xq.lon = antennaItem.geometry.coordinates[0]
                      pont_xq.lat = antennaItem.geometry.coordinates[1]
                      pont_xq.height = antennaItem.properties.Height + 0.68
                      break
                    default:
                      break
                  }
                }
              })
            } else {
              bycTransmitterArray.forEach((antennaItem) => {
                if (
                  item.properties.Transmitte ==
                  antennaItem.properties.Transmitte
                ) {
                  pont_xq.lon = antennaItem.geometry.coordinates[0]
                  pont_xq.lat = antennaItem.geometry.coordinates[1]
                  pont_xq.height = 20 + 0.68 * 2
                  MIMOid.push(antennaItem.properties.Transmitte)
                  if (MIMOid[MIMOid.length - 2] != MIMOid[MIMOid.length - 1]) {
                    that.loadMIMO(antennaItem.properties.Transmitte, 20)
                  }
                }
              })
            }
            // pont_xq.lon =
            //   Math.cos(
            //     Cesium.Math.toRadians((item.properties.Azimuth - 90) * -1)
            //   ) *
            //   0.000048 +
            //   item.properties.Longitude +
            //   0.000021
            // pont_xq.lat =
            //   Math.sin(
            //     Cesium.Math.toRadians((item.properties.Azimuth - 90) * -1)
            //   ) *
            //   0.000048 + item.properties.Latitude
          }
        })

        //汽车外扩
        let minlon_wk = parseFloat(lon) - 0.003
        let maxlon_wk = parseFloat(lon) + 0.003
        let minlat_wk = parseFloat(lats) - 0.003
        let maxlat_wk = parseFloat(lats) + 0.003

        params.limiandian.forEach((item) => {
          if (
            item.x > minlon_wk &&
            item.x < maxlon_wk &&
            item.y > minlat_wk &&
            item.y < maxlat_wk
          ) {
            //汽车到墙的距离
            let from = turf.point([parseFloat(lon), parseFloat(lats)]) //车
            let to = turf.point([item.x, item.y]) //墙
            let options = { units: 'kilometers' }
            let distance = turf.distance(from, to, options)
            if (distance * 1000 <= 300) {
              //基站到墙的距离
              let from1 = turf.point([
                parseFloat(pont_xq.lon),
                parseFloat(pont_xq.lat),
              ]) //基站
              let to1 = turf.point([parseFloat(item.x), parseFloat(item.y)]) //墙
              let options1 = { units: 'kilometers' }
              let distance1 = turf.distance(from1, to1, options1)
              //基站到车的距离
              let from2 = turf.point([
                parseFloat(pont_xq.lon),
                parseFloat(pont_xq.lat),
              ]) //基站
              let to2 = turf.point([parseFloat(lon), parseFloat(lats)]) //墙
              let options2 = { units: 'kilometers' }
              let distance2 = turf.distance(from2, to2, options2)
              //判断基站到墙大于基站到车
              if (distance1 > distance2) {
                //汽车到墙的距离
                let from = turf.point([parseFloat(lon), parseFloat(lats)]) //车
                let to = turf.point([item.x, item.y]) //墙
                let options = { units: 'kilometers' }
                let distance = turf.distance(from, to, options)
                lengArrey.push({
                  x: item.x,
                  y: item.y,
                  z: item.z,
                  uuid: item.uuid,
                  distance: distance,
                })
              }
            }
          }
        })

        let distance_duan = Math.min.apply(
          Math,
          lengArrey.map((item) => {
            return item.distance
          })
        ) //最短距离

        let distance_duan1 = [] //存入除了最短的其他长度
        let distance_duan1_uuid //最短距离uuid

        //墙距离车最短距离 与 基站和车连线
        lengArrey.forEach((item) => {
          if (item.distance == distance_duan) {
            distance_duan1_uuid = item.uuid
            llh1 = [
              item.x,
              item.y,
              item.z,
              parseFloat(lon),
              parseFloat(lats),
              parseFloat(cartographic.height.toFixed(0)),
            ]
            viewer.entities.getById('purpleArrows2').polyline.positions =
              new Cesium.CallbackProperty(function () {
                return Cesium.Cartesian3.fromDegreesArrayHeights(llh1)
              }, false)
            llh2 = [
              parseFloat(pont_xq.lon),
              parseFloat(pont_xq.lat),
              parseFloat(pont_xq.height),
              item.x,
              item.y,
              item.z,
            ]
            viewer.entities.getById('purpleArrows3').polyline.positions =
              new Cesium.CallbackProperty(function () {
                return Cesium.Cartesian3.fromDegreesArrayHeights(llh2)
              }, false)
          } else {
            distance_duan1.push(item)
          }
        })

        let distance_duan2 = Math.min.apply(
          Math,
          distance_duan1.map((item) => {
            return item.distance
          })
        ) //第二最短距离
        // console.log(distance_duan2)

        //墙距离车第二最短距离 与 基站和车连线
        lengArrey.forEach((item) => {
          if (
            item.distance == distance_duan2 &&
            item.uuid !== distance_duan1_uuid
          ) {
            llh3 = [
              item.x,
              item.y,
              item.z,
              parseFloat(lon),
              parseFloat(lats),
              parseFloat(cartographic.height.toFixed(0)),
            ]
            viewer.entities.getById('purpleArrows4').polyline.positions =
              new Cesium.CallbackProperty(function () {
                return Cesium.Cartesian3.fromDegreesArrayHeights(llh3)
              }, false)
            llh4 = [
              parseFloat(pont_xq.lon),
              parseFloat(pont_xq.lat),
              parseFloat(pont_xq.height),
              item.x,
              item.y,
              item.z,
            ]
            viewer.entities.getById('purpleArrows5').polyline.positions =
              new Cesium.CallbackProperty(function () {
                return Cesium.Cartesian3.fromDegreesArrayHeights(llh4)
              }, false)
          }
        })

        llh = [
          parseFloat(pont_xq.lon),
          parseFloat(pont_xq.lat),
          parseFloat(pont_xq.height),
          parseFloat(lon),
          parseFloat(lats),
          parseFloat(cartographic.height.toFixed(0)),
        ]

        if (llh.toString() != llh_old.toString()) {
          viewer.entities.getById('purpleArrows1').polyline.positions =
            new Cesium.CallbackProperty(function () {
              return Cesium.Cartesian3.fromDegreesArrayHeights(llh)
            }, false)
          llh_old = llh
        }

        params.rsrp.forEach((item) => {
          if (
            parseFloat(lon) > item.xmin &&
            parseFloat(lon) < item.xmax &&
            parseFloat(lats) > item.ymin &&
            parseFloat(lats) < item.ymax
          ) {
            // console.log(parseFloat(item.rsrp).toFixed(2))
            that.rsrpColor = that.rountColor(item.rsrp) //rsrp设置颜色
            that.rsrpNum = parseFloat(item.rsrp).toFixed(2) //设置值
            //绘制信号点
            that.loadingPoint({
              lon: lon,
              lats: lats,
              rsrp: item.rsrp,
              cartographic: cartographic,
              show: that.switchingStatus,
            })
            // let gjpnt = viewer.entities.add({
            //   position: Cesium.Cartesian3.fromDegrees(
            //     parseFloat(lon),
            //     parseFloat(lats),
            //     parseFloat(cartographic.height.toFixed(0)) + 2
            //   ),
            //   point: {
            //     pixelSize: 10, //点的大小
            //     color: Cesium.Color.fromCssColorString(
            //       that.rountColor(item.rsrp)
            //     ), //颜色
            //     // distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
            //     //   0,
            //     //   6000
            //     // ),
            //     show: true
            //   },
            // });
            // pnts.push(gjpnt)
            //绘制线
            fromDegreesArrayHeights.push(
              parseFloat(lon),
              parseFloat(lats),
              parseFloat(cartographic.height.toFixed(0)) + 2
            )
            colors.push(
              Cesium.Color.fromCssColorString(
                that.rountColor(item.rsrp)
              ).withAlpha(0.8)
            )
            if (colors.length > 2) {
              that.loadingLine({
                fromDegreesArrayHeights: fromDegreesArrayHeights,
                colors: colors,
                show: !that.switchingStatus,
              })
              // let perVertexPolyline = viewer.scene.primitives.add(new Cesium.Primitive({
              //   geometryInstances: new Cesium.GeometryInstance({
              //     geometry: new Cesium.PolylineGeometry({
              //       positions: Cesium.Cartesian3.fromDegreesArrayHeights(fromDegreesArrayHeights),
              //       width: 5.0,
              //       vertexFormat: Cesium.PolylineColorAppearance.VERTEX_FORMAT,
              //       colors: colors,
              //       colorsPerVertex: true
              //     })
              //   }),
              //   appearance: new Cesium.PolylineColorAppearance(),
              //   show: false
              // }))
              // lines.push(perVertexPolyline)
            }
          }
        })

        // 小车移动
        Cesium.Transforms.headingPitchRollToFixedFrame(
          position,
          hpRoll,
          Cesium.Ellipsoid.WGS84,
          fixedFrameTransforms,
          carPrimitive.modelMatrix
        )
        // 小车移动
        Cesium.Transforms.headingPitchRollToFixedFrame(
          position,
          hpRoll,
          Cesium.Ellipsoid.WGS84,
          fixedFrameTransforms,
          carPrimitive1.modelMatrix
        )
        //计算相机位置
        var cartesian3 = new Cesium.Cartesian3(
          position.x,
          position.y,
          position.z
        )
        var cartographic =
          scene.globe.ellipsoid.cartesianToCartographic(cartesian3)
        that.cartographic = cartographic
        //控制视角切换
        that.firstPerson({
          cartographic: cartographic,
          hpRoll: hpRoll,
          count: count,
        })
        // var lng =
        //   Cesium.Math.toDegrees(cartographic.longitude) +
        //   0.00001852398509 *
        //   200 *
        //   Math.cos(((270 + count) * 2 * Math.PI) / 360);
        // var lat =
        //   Cesium.Math.toDegrees(cartographic.latitude) +
        //   0.00001852398509 *
        //   200 *
        //   Math.sin(((270 + count) * 2 * Math.PI) / 360);
        // var alt = cartographic.height + 400;
        // viewer.camera.setView({
        //   destination: Cesium.Cartesian3.fromDegrees(lng, lat, alt),
        //   orientation: {
        //     // 指向  镜头随小车变化角度
        //     heading: hpRoll.heading,
        //     // 视角固定
        //     pitch: Cesium.Math.toRadians(-45.0),
        //     roll: 0.0,
        //   },
        // });
      }
    },
    //反射径显示隐藏
    refractionLineShow() {
      if (this.refractionLine == '显示') {
        this.refractionLine = '隐藏'
        viewer.entities.getById('purpleArrows2').polyline.show = false
        viewer.entities.getById('purpleArrows3').polyline.show = false
        viewer.entities.getById('purpleArrows4').polyline.show = false
        viewer.entities.getById('purpleArrows5').polyline.show = false
      } else {
        this.refractionLine = '显示'
        viewer.entities.getById('purpleArrows2').polyline.show = true
        viewer.entities.getById('purpleArrows3').polyline.show = true
        viewer.entities.getById('purpleArrows4').polyline.show = true
        viewer.entities.getById('purpleArrows5').polyline.show = true
      }
    },
    //加载点方法
    loadingPoint(pmaras) {
      let gjpnt = viewer.entities.add({
        position: Cesium.Cartesian3.fromDegrees(
          parseFloat(pmaras.lon),
          parseFloat(pmaras.lats),
          parseFloat(pmaras.cartographic.height.toFixed(0)) + 2
        ),
        point: {
          pixelSize: 10, //点的大小
          color: Cesium.Color.fromCssColorString(this.rountColor(pmaras.rsrp)), //颜色
          // distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
          //   0,
          //   6000
          // ),
          show: pmaras.show,
        },
      })
      pnts.push(gjpnt)
    },
    //加载线方法
    loadingLine(pmaras) {
      let perVertexPolyline = viewer.scene.primitives.add(
        new Cesium.Primitive({
          geometryInstances: new Cesium.GeometryInstance({
            geometry: new Cesium.PolylineGeometry({
              positions: Cesium.Cartesian3.fromDegreesArrayHeights(
                pmaras.fromDegreesArrayHeights
              ),
              width: 8.0,
              vertexFormat: Cesium.PolylineColorAppearance.VERTEX_FORMAT,
              colors: pmaras.colors,
              colorsPerVertex: true,
            }),
          }),
          appearance: new Cesium.PolylineColorAppearance(),
          show: pmaras.show,
        })
      )
      lines.push(perVertexPolyline)
    },
    //切换显示方式
    displayUsage() {
      if (this.showFs === '轨迹线') {
        this.switchingStatus = false
        lines.forEach((item) => {
          item.show = true
        })
        pnts.forEach((item) => {
          item.point.show._value = false
        })
        this.showFs = '轨迹点'
      } else {
        this.switchingStatus = true
        lines.forEach((item) => {
          item.show = false
        })
        pnts.forEach((item) => {
          item.point.show._value = true
        })
        this.showFs = '轨迹线'
      }
    },
    //判断视角高度
    monitorCamera() {
      let cartesian3 = new Cesium.Cartesian3(
        viewer.camera.position.x,
        viewer.camera.position.y,
        viewer.camera.position.z
      )
      let cartographic =
        viewer.scene.globe.ellipsoid.cartesianToCartographic(cartesian3)
      let alt = cartographic.height
      if (alt < 5) {
        this.shijiaoShow = true
      } else {
        this.shijiaoShow = false
      }
    },
    //第一人称
    firstPerson(params) {
      this.monitorCamera()
      if (this.shijiao === '第三人称') {
        var lng =
          Cesium.Math.toDegrees(params.cartographic.longitude) -
          0 * 5 * Math.cos(((0 + params.count) * 2 * Math.PI) / 360)
        var lat =
          Cesium.Math.toDegrees(params.cartographic.latitude) -
          0 * 5 * Math.sin(((0 + params.count) * 2 * Math.PI) / 360)
        var point = turf.point([lng, lat])
        var distance = -1 / 1000
        var distance1 = -0.3
        var bearing = (params.hpRoll.heading * 180) / Math.PI
        var options = { units: 'kilometers' }
        var destination = turf.destination(point, distance, bearing, options)
        var destination1 = turf.destination(point, distance1, bearing, options)
        var alt = params.cartographic.height + 1.9
        var alt1 = params.cartographic.height + 100
        viewer.camera.setView({
          destination: Cesium.Cartesian3.fromDegrees(
            destination.geometry.coordinates[0],
            destination.geometry.coordinates[1],
            alt
          ),
          orientation: {
            // 指向  镜头随小车变化角度
            heading: params.hpRoll.heading,
            // 视角固定
            pitch: Cesium.Math.toRadians(-4.0),
            roll: 0.0,
          },
        })
        viewer1.camera.setView({
          destination: Cesium.Cartesian3.fromDegrees(
            destination1.geometry.coordinates[0],
            destination1.geometry.coordinates[1],
            alt1
          ),
          orientation: {
            // 指向  镜头随小车变化角度
            heading: params.hpRoll.heading,
            // 视角固定
            pitch: Cesium.Math.toRadians(-15.0),
            roll: 0.0,
          },
        })
        viewer.scene.screenSpaceCameraController.enableRotate = false
        viewer.scene.screenSpaceCameraController.enableZoom = false
        viewer.scene.screenSpaceCameraController.enableTilt = false
      } else {
        var lng =
          Cesium.Math.toDegrees(params.cartographic.longitude) +
          0.00001852398509 *
          200 *
          Math.cos(((270 + params.count) * 2 * Math.PI) / 360)
        var lat =
          Cesium.Math.toDegrees(params.cartographic.latitude) +
          0.00001852398509 *
          200 *
          Math.sin(((270 + params.count) * 2 * Math.PI) / 360)
        var alt = params.cartographic.height + 400
        viewer.camera.setView({
          destination: Cesium.Cartesian3.fromDegrees(lng, lat, alt),
          orientation: {
            // 指向  镜头随小车变化角度
            heading: params.hpRoll.heading,
            // 视角固定
            pitch: Cesium.Math.toRadians(-45.0),
            roll: 0.0,
          },
        })
        viewer.scene.screenSpaceCameraController.enableRotate = true
        viewer.scene.screenSpaceCameraController.enableZoom = true
        viewer.scene.screenSpaceCameraController.enableTilt = true
      }
    },
    //控制车的初始连线
    loadLine(data) {
      let getCustomMaterialLine = (image, color) => {
        return new Cesium.CustomMaterialLine({
          image: image,
          color: color,
          duration: 1000,
        })
      }
      data.forEach((item, index) => {
        let lianxian = viewer.entities.add({
          id: 'purpleArrows' + (index + 1),
          polyline: {
            positions: Cesium.Cartesian3.fromDegreesArrayHeights(item.data),
            width: 2,
            arcType: 1,
            material: getCustomMaterialLine(
              colorss,
              Cesium.Color.fromCssColorString(item.color).withAlpha(0.7)
            ),
          },
        })
        xinhaolines.push(lianxian)
      })
    },
    //加载网元
    loadwangyuan() {
      if (!this.icon.icon5) {
        this.loadBase(jizhanshuju)
        this.loadTxzj(tianxianshuju)
        this.loadTxJSON()
        this.modelTiaozheng = !this.modelTiaozheng
        this.largeScreenShow = !this.largeScreenShow
        flyTos({
          lon: 108.959,
          lat: 34.2054,
          height: 25000,
          viewer: viewer,
          heading: 0,
          pitch: -75,
          roll: 0,
        })
      } else {
        if (WebsiteArray.length > 0) {
          WebsiteArray.forEach((item) => {
            viewer.scene.primitives.remove(item)
          })
          azjArray.forEach((item) => {
            viewer.scene.primitives.remove(item)
          })
          txzjArray.forEach((item) => {
            viewer.scene.primitives.remove(item)
          })
          antennaAry.forEach((item) => {
            viewer.scene.primitives.remove(item)
          })
          billboardArray.forEach((item) => {
            viewer.entities.remove(item)
          })
        }
        this.modelTiaozheng = !this.modelTiaozheng
        this.largeScreenShow = !this.largeScreenShow
      }
    },
    //获取站点数据
    async loadzhandain() {
      await this.$http.get(window.geojson.siteJson).then((res) => {
        if (res) {
          jizhanshuju = res.data.features
          jizhanshuju.forEach((item) => {
            this.siteArray.push({
              lon: item.geometry.coordinates[0],
              lat: item.geometry.coordinates[1],
              Site: item.properties.Site,
              InstallationMod: item.properties.安装方,
              Height: item.properties.Height,
            })
          })
          this.$emit('sendStation', this.siteArray)
        }
      })
    },
    //按照基站类型加载基站
    loadBase(pmaras) {
      pmaras.forEach((item) => {
        switch (item.properties.安装方) {
          case '楼顶抱杆':
            this.loadWebsite({
              lon: item.geometry.coordinates[0],
              lat: item.geometry.coordinates[1],
              height: parseFloat(item.properties.minheight) || 0,
              url: window.models.bgModelUrl,
              scale: 1,
              color: '#42FF73',
            })
            if (parseFloat(item.properties.minheight)) {
              this.addMultipleBillboards({
                id: item.properties.Site,
                lon: item.geometry.coordinates[0],
                lat: item.geometry.coordinates[1],
                height: parseFloat(item.properties.minheight) + 10,
                image: base3,
                width: 32,
                height2: 32,
              })
            } else {
              this.addMultipleBillboards({
                id: item.properties.Site,
                lon: item.geometry.coordinates[0],
                lat: item.geometry.coordinates[1],
                height: 10,
                image: base3,
                width: 32,
                height2: 32,
              })
            }
            break
          case '楼顶三角塔':
            this.loadWebsite({
              lon: item.geometry.coordinates[0],
              lat: item.geometry.coordinates[1],
              height: parseFloat(item.properties.minheight) || 0,
              url: window.models.sjtModelUrl,
              scale: 1,
              color: '#FB9526',
            })
            //安装架
            this.loadAzj({
              lon: item.geometry.coordinates[0],
              lat: item.geometry.coordinates[1],
              height: item.properties.Height,
              url: window.models.ldsjtazjModelUrl,
              scale: 1,
            })
            this.addMultipleBillboards({
              id: item.properties.Site,
              lon: item.geometry.coordinates[0],
              lat: item.geometry.coordinates[1],
              height: item.properties.Height + 35,
              image: base2,
              width: 32,
              height2: 32,
            })
            break
          case '单管塔':
            this.loadWebsite({
              lon: item.geometry.coordinates[0],
              lat: item.geometry.coordinates[1],
              height: item.properties.Height - 80 + 3,
              url: window.models.dgtModelUrl,
              scale: 1,
              color: '#E726FB',
              size: 1,
            })
            this.addMultipleBillboards({
              id: item.properties.Site,
              lon: item.geometry.coordinates[0],
              lat: item.geometry.coordinates[1],
              height: item.properties.Height + 25,
              image: base,
              width: 32,
              height2: 32,
            })
            break
          case '地面铁塔':
            this.loadWebsite({
              lon: item.geometry.coordinates[0],
              lat: item.geometry.coordinates[1],
              height: item.properties.Height - 110 + 3,
              url: window.models.swttModelUrl,
              scale: 1,
              color: '#FB264D',
            })
            this.loadAzj({
              lon: item.geometry.coordinates[0],
              lat: item.geometry.coordinates[1],
              height: item.properties.Height,
              url: window.models.ldsjtazjModelUrl,
              scale: 1,
            })
            // this.loadSlz({
            //   lon: item.geometry.coordinates[0],
            //   lat: item.geometry.coordinates[1],
            //   height: item.properties.Height + 10,
            // })
            this.addMultipleBillboards({
              id: item.properties.Site,
              lon: item.geometry.coordinates[0],
              lat: item.geometry.coordinates[1],
              height: item.properties.Height + 35,
              image: base1,
              width: 32,
              height2: 32,
            })
            break
          default:
            break
        }
      })
    },
    //加载基站标注
    addMultipleBillboards(pmaras) {
      // var center = Cesium.Cartesian3.fromDegrees(
      //   pmaras.lon,
      //   pmaras.lat,
      //   pmaras.height
      // )
      // var billboards = viewer.scene.primitives.add(
      //   new Cesium.BillboardCollection()
      // )
      // billboards.add({
      //   id: pmaras.id,
      //   image: pmaras.image,
      //   // sizeInMeters: true, //图像的尺寸被指定成图像实际的尺寸
      //   position: center,
      //   horizontalOrigin: Cesium.HorizontalOrigin.center, // default
      //   verticalOrigin: Cesium.VerticalOrigin.BOTTOM, // default: CENTER
      //   // 按距离缩放
      //   scaleByDistance: new Cesium.NearFarScalar(1.5e2, 1, 60000, 0.0),
      //   scale: 1,
      //   //   new Cesium.CallbackProperty(function () {
      //   //   let scale = 1;
      //   //   let ha = true;
      //   //   if (ha) {
      //   //     scale += 0.1
      //   //     if (scale >= 1.5) {
      //   //       ha = false
      //   //     }
      //   //   } else {
      //   //     scale -= 0.1
      //   //     if (scale <= 1) {
      //   //       ha = true
      //   //     }
      //   //   }
      //   //   return scale
      //   // }, false),
      //   color: Cesium.Color.fromCssColorString('#ffffff'), // default: WHITE
      //   // rotation: Cesium.Math.PI_OVER_FOUR, // default: 0.0
      //   // alignedAxis: Cesium.Cartesian3.ZERO, // default
      //   width: pmaras.width, // default: undefined
      //   height: pmaras.height2, // default: undefined
      // })
      let scale = 0.7
      let ha = true
      let billboards = viewer.entities.add({
        id: pmaras.id,
        position: Cesium.Cartesian3.fromDegrees(
          pmaras.lon,
          pmaras.lat,
          pmaras.height
        ),
        billboard: {
          //图标
          image: pmaras.image,
          width: pmaras.width,
          height: pmaras.height2,
          scale: new Cesium.CallbackProperty(function () {
            if (ha) {
              scale += 0.05
              if (scale >= 1) {
                ha = false
              }
            } else {
              scale -= 0.05
              if (scale <= 0.7) {
                ha = true
              }
            }
            return scale
          }, false),
          scaleByDistance: new Cesium.NearFarScalar(1.5e2, 1, 60000, 0.0),
          // pixelOffset: new this.Cesium.Cartesian2(100, -35),   //偏移量
        },
      })
      billboardArray.push(billboards)
    },
    //加载标注
    loadSlz(pmaras) {
      let position = Cesium.Cartesian3.fromDegrees(
        pmaras.lon,
        pmaras.lat,
        pmaras.height
      )
      let heading = 0
      function diaoyong() {
        heading = heading + Cesium.Math.toRadians(10)
        var hpr = new Cesium.HeadingPitchRoll(heading, 0, 0)
        var orientation = Cesium.Transforms.headingPitchRollQuaternion(
          position,
          hpr
        )
        return orientation
      }
      viewer.entities.add({
        position: position, //椎体位置
        //通过CallbackProperty延迟回调函数一直调用封装的偏航角方法
        //false，返回的值如果改变则一直调用自身，diaoyong()返回的值是orientation，而orientation会根据每次heading 的不同而发生改变
        orientation: new Cesium.CallbackProperty(diaoyong, false),
        model: {
          show: true,
          uri: 'http://*************:59083/models/gltf/sileizhui1.gltf',
          scale: 5,
          minimumPixelSize: 10,
          maximumScale: 10,
        },
      })
    },
    //请求天线支架服务
    async loadTxzjJSON() {
      await this.$http.get(window.geojson.txzjJson).then((res) => {
        if (res) {
          tianxianshuju = res.data.features
        }
      })
    },
    //加载天线支架
    loadTxzj(pmaras) {
      pmaras.forEach((item) => {
        switch (item.properties.安装方) {
          case '楼顶抱杆':
            this.xiaoqubaogan_loding({
              lon: item.geometry.coordinates[0],
              lat: item.geometry.coordinates[1],
              height: parseFloat(item.properties.minheight) + 2 || 0 + 2,
              Azimuth: (item.properties.Azimuth - 90) * -1,
              url: window.models.txzjModelUrl,
              id: item.properties,
              scale: 1,
            })
            break
          case '楼顶三角塔':
            this.xiaoqubaogan_loding({
              lon: item.geometry.coordinates[0],
              lat: item.geometry.coordinates[1],
              height: item.properties.Height,
              Azimuth: (item.properties.Azimuth - 90) * -1,
              url: window.models.txzjModelUrl,
              id: item.properties,
              scale: 1,
            })
            break
          case '单管塔':
            this.xiaoqubaogan_loding({
              lon: item.geometry.coordinates[0],
              lat: item.geometry.coordinates[1],
              height: item.properties.Height,
              Azimuth: (item.properties.Azimuth - 90) * -1,
              url: window.models.txzjModelUrl,
              id: item.properties,
              scale: 2,
            })
            break
          case '地面铁塔':
            this.xiaoqubaogan_loding({
              lon: item.geometry.coordinates[0],
              lat: item.geometry.coordinates[1],
              height: item.properties.Height,
              Azimuth: (item.properties.Azimuth - 90) * -1,
              url: window.models.txzjModelUrl,
              id: item.properties,
              scale: 1,
            })
            break
          default:
            break
        }
      })
    },
    //请求天线服务
    async loadTxJSON() {
      await this.$http.get(window.geojson.antennaPositionJson).then((res) => {
        if (res) {
          antennaArray = res.data.features
          this.loadTx(res.data.features)
        }
      })
    },
    //加载天线
    loadTx(pmaras) {
      pmaras.forEach((item) => {
        switch (item.properties.安装方) {
          case '楼顶抱杆':
            if (parseFloat(item.properties.minheight)) {
              this.loadXiaoqu({
                id: item.properties.Transmitte,
                lon: item.geometry.coordinates[0],
                lat: item.geometry.coordinates[1],
                height: parseFloat(item.properties.minheight) + 2 + 0.68,
                Azimuth: (item.properties.Azimuth - 90) * -1,
                url: window.models.transmitterModelUrl,
                scale: 1,
              })
            } else {
              this.loadXiaoqu({
                id: item.properties.Transmitte,
                lon: item.geometry.coordinates[0],
                lat: item.geometry.coordinates[1],
                height: 0 + 2 + 0.68,
                Azimuth: (item.properties.Azimuth - 90) * -1,
                url: window.models.transmitterModelUrl,
                scale: 1,
              })
            }
            break
          case '楼顶三角塔':
            this.loadXiaoqu({
              id: item.properties.Transmitte,
              lon: item.geometry.coordinates[0],
              lat: item.geometry.coordinates[1],
              height: item.properties.Height + 0.68,
              Azimuth: (item.properties.Azimuth - 90) * -1,
              url: window.models.transmitterModelUrl,
              scale: 1,
            })
            break
          case '单管塔':
            this.loadXiaoqu({
              id: item.properties.Transmitte,
              lon:
                Math.cos(
                  Cesium.Math.toRadians((item.properties.Azimuth - 90) * -1)
                ) *
                0.00000398 +
                item.geometry.coordinates[0],
              lat:
                Math.sin(
                  Cesium.Math.toRadians((item.properties.Azimuth - 90) * -1)
                ) *
                0.00000398 +
                item.geometry.coordinates[1],
              height: item.properties.Height + 0.68 * 2,
              Azimuth: (item.properties.Azimuth - 90) * -1,
              url: window.models.transmitterModelUrl,
              scale: 2,
            })
            break
          case '地面铁塔':
            this.loadXiaoqu({
              id: item.properties.Transmitte,
              lon: item.geometry.coordinates[0],
              lat: item.geometry.coordinates[1],
              height: item.properties.Height + 0.68,
              Azimuth: (item.properties.Azimuth - 90) * -1,
              url: window.models.transmitterModelUrl,
              scale: 1,
            })
            break
          default:
            break
        }
      })
    },
    //加载波束
    loadMIMO(Transmitte, height, num) {
      var scales = [10, 7, 7, 4, 4, 2, 2]
      var jiaodu = [0, -17, 17, -40, 40, -70, 70]
      var models = window.models.mimoModels
      if (!this.largeScreenShow) {
        if (num == 3) {
          antennaArray.forEach((item) => {
            if (item.properties.Transmitte.indexOf(Transmitte) != -1) {
              scales.forEach((itemSon, index) => {
                let cx =
                  Math.cos(
                    Cesium.Math.toRadians((item.properties.Azimuth - 90) * -1)
                  ) *
                  0.00000398 +
                  item.geometry.coordinates[0]
                let cy =
                  Math.sin(
                    Cesium.Math.toRadians((item.properties.Azimuth - 90) * -1)
                  ) *
                  0.00000398 +
                  item.geometry.coordinates[1]
                let position = Cesium.Cartesian3.fromDegrees(
                  cx,
                  cy,
                  height + 0.68 * 2 || item.properties.Height + 0.68 * 2
                )
                let heading = Cesium.Math.toRadians(
                  item.properties.Azimuth + 190 * -1
                )
                let pitch = Cesium.Math.toRadians(jiaodu[index])
                let roll = Cesium.Math.toRadians(0)
                let hpRolls = new Cesium.HeadingPitchRoll(heading, pitch, roll)
                let fixedFrameTransforms =
                  Cesium.Transforms.localFrameToFixedFrameGenerator(
                    'south',
                    'east'
                  )
                var models2 = viewer.scene.primitives.add(
                  Cesium.Model.fromGltf({
                    position: position,
                    url: models[index],
                    modelMatrix: Cesium.Transforms.headingPitchRollToFixedFrame(
                      position,
                      hpRolls,
                      Cesium.Ellipsoid.WGS84,
                      fixedFrameTransforms
                    ),
                    scale: itemSon,
                    lightColor: new Cesium.Cartesian3(100, 100, 100),
                    color: new Cesium.Color(1, 1, 1, 0.4),
                    show: false
                  })
                )
                mimos.push(models2)
              })
            }
          })
        } else {
          antennaArray.forEach((item) => {
            if (item.properties.Transmitte == Transmitte) {
              scales.forEach((itemSon, index) => {
                let cx =
                  Math.cos(
                    Cesium.Math.toRadians((item.properties.Azimuth - 90) * -1)
                  ) *
                  0.00000398 +
                  item.geometry.coordinates[0]
                let cy =
                  Math.sin(
                    Cesium.Math.toRadians((item.properties.Azimuth - 90) * -1)
                  ) *
                  0.00000398 +
                  item.geometry.coordinates[1]
                let position = Cesium.Cartesian3.fromDegrees(
                  cx,
                  cy,
                  height + 0.68 * 2 || item.properties.Height + 0.68 * 2
                )
                let heading = Cesium.Math.toRadians(
                  item.properties.Azimuth + 190 * -1
                )
                let pitch = Cesium.Math.toRadians(jiaodu[index])
                let roll = Cesium.Math.toRadians(0)
                let hpRolls = new Cesium.HeadingPitchRoll(heading, pitch, roll)
                let fixedFrameTransforms =
                  Cesium.Transforms.localFrameToFixedFrameGenerator(
                    'south',
                    'east'
                  )
                var models2 = viewer.scene.primitives.add(
                  Cesium.Model.fromGltf({
                    position: position,
                    url: models[index],
                    modelMatrix: Cesium.Transforms.headingPitchRollToFixedFrame(
                      position,
                      hpRolls,
                      Cesium.Ellipsoid.WGS84,
                      fixedFrameTransforms
                    ),
                    scale: itemSon,
                    lightColor: new Cesium.Cartesian3(100, 100, 100),
                    color: new Cesium.Color(1, 1, 1, 0.4),
                    show: false
                  })
                )
                mimos.push(models2)
              })
            }
          })
        }
      } else {
        if (mimos.length > 0) {
          mimos.forEach((item) => {
            viewer.scene.primitives.remove(item)
          })
        }
        bycTransmitterArray.forEach((item) => {
          if (item.properties.Transmitte == Transmitte) {
            scales.forEach((itemSon, index) => {
              let cx =
                Math.cos(
                  Cesium.Math.toRadians((item.properties.Azimuth - 90) * -1)
                ) *
                0.00000398 +
                item.geometry.coordinates[0]
              let cy =
                Math.sin(
                  Cesium.Math.toRadians((item.properties.Azimuth - 90) * -1)
                ) *
                0.00000398 +
                item.geometry.coordinates[1]
              let position = Cesium.Cartesian3.fromDegrees(
                cx,
                cy,
                height + 0.68 * 2
              )
              let heading = Cesium.Math.toRadians(
                item.properties.Azimuth + 190 * -1
              )
              let pitch = Cesium.Math.toRadians(jiaodu[index])
              let roll = Cesium.Math.toRadians(-3)
              let hpRolls = new Cesium.HeadingPitchRoll(heading, pitch, roll)
              let fixedFrameTransforms =
                Cesium.Transforms.localFrameToFixedFrameGenerator(
                  'south',
                  'east'
                )
              var models2 = viewer.scene.primitives.add(
                Cesium.Model.fromGltf({
                  position: position,
                  url: models[index],
                  modelMatrix: Cesium.Transforms.headingPitchRollToFixedFrame(
                    position,
                    hpRolls,
                    Cesium.Ellipsoid.WGS84,
                    fixedFrameTransforms
                  ),
                  scale: itemSon,
                  lightColor: new Cesium.Cartesian3(100, 100, 100),
                  color: new Cesium.Color(1, 1, 1, 0.4),
                })
              )
              mimos.push(models2)
            })
          }
        })
      }
      // setInterval(() => {
      //   mimos.forEach((item) => {
      //     if (item.scale <= 20) {
      //       item.scale += 0.02
      //     } else {
      //       item.scale = 1
      //     }
      //   })
      // }, 100)
    },
    //加载单管塔
    loadJjzhan() {
      const jizhan = new Cesium.Cesium3DTileset({
        url: 'http://*************:59083/models/3dmodels/jizhan/tileset.json',
        maximumScreenSpaceError: 30, //用于驱动细节细化级别的最大屏幕空间误差。 默认16
        maximumMemoryUsage: 2048 * 2, //瓦片集可以使用的最大内存量（以 MB 为单位）。 默认512
        cullWithChildrenBounds: false, //优化选项。是否使用子边界体积的并集来剔除瓦片。默认true
        cullRequestsWhileMoving: false, //优化选项。不要请求由于相机移动而在返回时可能未使用的图块。这种优化只适用于静止的瓦片集。默认true
        cullRequestsWhileMovingMultiplier: 30.0, //优化选项。移动时用于剔除请求的乘数。较大的是更积极的剔除，较小的较不积极的剔除。 默认值60
        preloadWhenHidden: true, //tileset.show时 预加载瓷砖false。加载图块，就好像图块集可见但不渲染它们。 默认false
        preloadFlightDestinations: true, //优化选项。在相机飞行时在相机的飞行目的地预加载图块。。 默认true
        preferLeaves: true, //优化选项 最好先装载叶子。 默认false
        dynamicScreenSpaceError: true, //优化选项。减少距离相机较远的图块的屏幕空间错误。 默认false
        dynamicScreenSpaceErrorDensity: 0.00278, //用于调整动态屏幕空间误差的密度，类似于雾密度。
        skipLevelOfDetail: true,
        baseScreenSpaceError: 2048,
        skipScreenSpaceErrorFactor: 1,
        skipLevels: 0,
        immediatelyLoadDesiredLevelOfDetail: true,
        loadSiblings: false,
        // shadows: Cesium.ShadowMode.ENABLED,
        show: true,
      })
      viewer.scene.primitives.add(jizhan)
    },
    //基站
    loadWebsite(data) {
      let lon = data.lon
      let lat = data.lat
      let alt = data.height
      let position = Cesium.Cartesian3.fromDegrees(lon, lat, alt)
      let heading = Cesium.Math.toRadians(data.heading || 0)
      let pitch = Cesium.Math.toRadians(0)
      let roll = Cesium.Math.toRadians(0)
      let hpRolls = new Cesium.HeadingPitchRoll(heading, pitch, roll)
      let fixedFrameTransforms =
        Cesium.Transforms.localFrameToFixedFrameGenerator('south', 'east')
      let models3 = viewer.scene.primitives.add(
        Cesium.Model.fromGltf({
          position: position,
          url: data.url,
          modelMatrix: Cesium.Transforms.headingPitchRollToFixedFrame(
            position,
            hpRolls,
            Cesium.Ellipsoid.WGS84,
            fixedFrameTransforms
          ),
          scale: data.scale,
          lightColor: data.lightColor || new Cesium.Cartesian3(20, 20, 20),
          color: new Cesium.Color(1, 1, 1, 1),
          // distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
          //   0,
          //   5000
          // ),
        })
      )
      WebsiteArray.push(models3)
      // viewer.entities.add({
      //   position: position,
      //   point: {
      //     pixelSize: 10, //点的大小
      //     color: Cesium.Color.fromCssColorString(data.color), //颜色
      //     show: true,
      //   },
      // })
    },
    //加载安装架
    loadAzj(data) {
      let lon = data.lon
      let lat = data.lat
      let alt = data.height
      let position = Cesium.Cartesian3.fromDegrees(lon, lat, alt)
      let heading = Cesium.Math.toRadians(0)
      let pitch = Cesium.Math.toRadians(0)
      let roll = Cesium.Math.toRadians(0)
      let hpRolls = new Cesium.HeadingPitchRoll(heading, pitch, roll)
      let fixedFrameTransforms =
        Cesium.Transforms.localFrameToFixedFrameGenerator('south', 'east')
      let model = viewer.scene.primitives.add(
        Cesium.Model.fromGltf({
          position: position,
          url: data.url,
          modelMatrix: Cesium.Transforms.headingPitchRollToFixedFrame(
            position,
            hpRolls,
            Cesium.Ellipsoid.WGS84,
            fixedFrameTransforms
          ),
          scale: data.scale,
          lightColor: new Cesium.Cartesian3(20, 20, 20),
          color: new Cesium.Color(1, 1, 1, 1),
          // distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
          //   0,
          //   5000
          // ),
        })
      )
      azjArray.push(model)
    },
    //基站扇形
    drawBasePoint(data) {
      var cx = Math.cos(Cesium.Math.toRadians(data.aoa)) * data.juli + data.lng
      var cy = Math.sin(Cesium.Math.toRadians(data.aoa)) * data.juli + data.lat
      //Cesium.Math.toRadians(data.aoa) data.aoa是扇形的朝向
      let headingd = Cesium.Math.toRadians(360 - data.aoa)
      let pitchd = Cesium.Math.toRadians(data.mechanical * -1)
      let rolld = Cesium.Math.toRadians(0)
      let hpRolls2 = new Cesium.HeadingPitchRoll(headingd, pitchd, rolld)
      let shanxin = viewer.entities.add({
        id: data.id,
        name: '扇形' + data.aoa,
        position: Cesium.Cartesian3.fromDegrees(cx, cy, 59),
        orientation: Cesium.Transforms.headingPitchRollQuaternion(
          Cesium.Cartesian3.fromDegrees(cx, cy, 59),
          hpRolls2
        ),
        ellipsoid: {
          radii: new Cesium.Cartesian3(130.0, 130.0, 130.0), // 扇形半径
          innerRadii: new Cesium.Cartesian3(1.0, 1.0, 1.0), // 内半径
          minimumClock: Cesium.Math.toRadians(-25), // 左右偏角
          maximumClock: Cesium.Math.toRadians(25),
          minimumCone: Cesium.Math.toRadians(90), // 上下偏角  可以都设置为90
          maximumCone: Cesium.Math.toRadians(75),
          material: new Cesium.CircleRippleMaterialProperty({
            color: Cesium.Color.fromCssColorString(data.color).withAlpha(0.6),
            speed: 6.0,
            count: 4,
            gradient: 0.2,
          }),
          // material: new Cesium.CircleSpiralMaterialProperty({
          //   color: Cesium.Color.fromCssColorString(
          //     data.color
          //   ).withAlpha(0.6),
          //   speed: 10.0
          // }),
          outline: false,
          outlineColor: Cesium.Color.fromCssColorString(data.color),
        },
      })
    },
    //实例化抱杆
    xiaoqubaogan_loding(pmaras) {
      let position3 = new Cesium.Cartesian3.fromDegrees(
        pmaras.lon,
        pmaras.lat,
        pmaras.height
      )
      var headingd = Cesium.Math.toRadians(pmaras.Azimuth)
      var pitchd = Cesium.Math.toRadians(180)
      var rolld = Cesium.Math.toRadians(0)
      let hpRolls2 = new Cesium.HeadingPitchRoll(headingd, pitchd, rolld)
      let fixedFrameTransforms2 =
        Cesium.Transforms.localFrameToFixedFrameGenerator('east', 'south')
      var models3 = viewer.scene.primitives.add(
        Cesium.Model.fromGltf({
          position: position3,
          url: pmaras.url,
          modelMatrix: Cesium.Transforms.headingPitchRollToFixedFrame(
            position3,
            hpRolls2,
            Cesium.Ellipsoid.WGS84,
            fixedFrameTransforms2
          ),
          scale: pmaras.scale,
          lightColor: new Cesium.Cartesian3(20, 20, 20),
          color: new Cesium.Color(1, 1, 1, 1),
          distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
            0,
            5000
          ),
        })
      )
      txzjArray.push(models3)
    },
    //加载小区
    loadXiaoqu(pmaras) {
      var cx = pmaras.lon
      var cy = pmaras.lat
      let position3 = Cesium.Cartesian3.fromDegrees(cx, cy, pmaras.height)
      var headingd = Cesium.Math.toRadians(pmaras.Azimuth)
      var pitchd = Cesium.Math.toRadians(0)
      var rolld = Cesium.Math.toRadians(0)
      let hpRolls2 = new Cesium.HeadingPitchRoll(headingd, pitchd, rolld)
      let fixedFrameTransforms2 =
        Cesium.Transforms.localFrameToFixedFrameGenerator('east', 'south')
      var xiaoqu = viewer.scene.primitives.add(
        Cesium.Model.fromGltf({
          id: pmaras.id,
          position: position3,
          url: pmaras.url,
          modelMatrix: Cesium.Transforms.headingPitchRollToFixedFrame(
            position3,
            hpRolls2,
            Cesium.Ellipsoid.WGS84,
            fixedFrameTransforms2
          ),
          scale: pmaras.scale,
          lightColor: new Cesium.Cartesian3(20, 20, 20),
          color: new Cesium.Color(1, 1, 1, 1),
          distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
            0,
            5000
          ),
        })
      )
      antennaAry.push(xiaoqu)
    },
    azimuthAdjustment(val) {
      azimuthArray.push(val)
      if (pickObject.length > 0) {
        let oldMatrix = pickObject[0].primitive.modelMatrix
        let oldCenter = new Cesium.Cartesian3(
          oldMatrix[12],
          oldMatrix[13],
          oldMatrix[14]
        )
        let m1 = Cesium.Transforms.eastNorthUpToFixedFrame(
          Cesium.Matrix4.getTranslation(oldMatrix, new Cesium.Cartesian3()),
          Cesium.Ellipsoid.WGS84,
          new Cesium.Matrix4()
        )
        let m3 = Cesium.Matrix4.multiply(
          Cesium.Matrix4.inverse(m1, new Cesium.Matrix4()),
          oldMatrix,
          new Cesium.Matrix4()
        )
        let mat3 = Cesium.Matrix4.getMatrix3(m3, new Cesium.Matrix3())
        let q = Cesium.Quaternion.fromRotationMatrix(mat3)
        let hpr = Cesium.HeadingPitchRoll.fromQuaternion(q)
        let headingPitchRoll, num
        if (azimuthArray.length > 1) {
          if (
            azimuthArray[azimuthArray.length - 1] >
            azimuthArray[azimuthArray.length - 2]
          ) {
            num = azimuthArray[azimuthArray.length - 1] - azimuthArray[azimuthArray.length - 2]
            headingPitchRoll = new Cesium.HeadingPitchRoll(
              hpr.heading,
              (hpr.pitch += Cesium.Math.toRadians(num)),
              hpr.roll
            )
            this.tableData[6].value += num
          } else {
            num = azimuthArray[azimuthArray.length - 2] - azimuthArray[azimuthArray.length - 1]
            headingPitchRoll = new Cesium.HeadingPitchRoll(
              hpr.heading,
              (hpr.pitch -= Cesium.Math.toRadians(num)),
              hpr.roll
            )
            this.tableData[6].value -= num
          }
        }
        let m = Cesium.Transforms.headingPitchRollToFixedFrame(
          oldCenter,
          headingPitchRoll,
          Cesium.Ellipsoid.WGS84,
          Cesium.Transforms.eastNorthUpToFixedFrame,
          new Cesium.Matrix4()
        )
        pickObject[0].primitive.modelMatrix = m
      }
      if (mimos.length > 0) {
        mimos.forEach((item) => {
          let oldMatrix = item.modelMatrix
          let oldCenter = new Cesium.Cartesian3(
            oldMatrix[12],
            oldMatrix[13],
            oldMatrix[14]
          )
          let m1 = Cesium.Transforms.eastNorthUpToFixedFrame(
            Cesium.Matrix4.getTranslation(oldMatrix, new Cesium.Cartesian3()),
            Cesium.Ellipsoid.WGS84,
            new Cesium.Matrix4()
          )
          let m3 = Cesium.Matrix4.multiply(
            Cesium.Matrix4.inverse(m1, new Cesium.Matrix4()),
            oldMatrix,
            new Cesium.Matrix4()
          )
          let mat3 = Cesium.Matrix4.getMatrix3(m3, new Cesium.Matrix3())
          let q = Cesium.Quaternion.fromRotationMatrix(mat3)
          let hpr = Cesium.HeadingPitchRoll.fromQuaternion(q)
          let headingPitchRoll, num
          if (azimuthArray.length > 1) {
            if (
              azimuthArray[azimuthArray.length - 1] >
              azimuthArray[azimuthArray.length - 2]
            ) {
              num = azimuthArray[azimuthArray.length - 1] - azimuthArray[azimuthArray.length - 2]
              headingPitchRoll = new Cesium.HeadingPitchRoll(
                hpr.heading,
                (hpr.pitch += Cesium.Math.toRadians(num)),
                hpr.roll
              )
            } else {
              num = azimuthArray[azimuthArray.length - 2] - azimuthArray[azimuthArray.length - 1]
              headingPitchRoll = new Cesium.HeadingPitchRoll(
                hpr.heading,
                (hpr.pitch -= Cesium.Math.toRadians(num)),
                hpr.roll
              )
            }
          }
          let m = Cesium.Transforms.headingPitchRollToFixedFrame(
            oldCenter,
            headingPitchRoll,
            Cesium.Ellipsoid.WGS84,
            Cesium.Transforms.eastNorthUpToFixedFrame,
            new Cesium.Matrix4()
          )
          item.modelMatrix = m
        })
      }
    },
    //方位角调整
    mechanicalAdjustment(val) {
      mechanicalArray.push(val)
      if (pickObject.length > 0) {
        let oldMatrix = pickObject[0].primitive.modelMatrix
        let oldCenter = new Cesium.Cartesian3(
          oldMatrix[12],
          oldMatrix[13],
          oldMatrix[14]
        )
        let m1 = Cesium.Transforms.eastNorthUpToFixedFrame(
          Cesium.Matrix4.getTranslation(oldMatrix, new Cesium.Cartesian3()),
          Cesium.Ellipsoid.WGS84,
          new Cesium.Matrix4()
        )
        let m3 = Cesium.Matrix4.multiply(
          Cesium.Matrix4.inverse(m1, new Cesium.Matrix4()),
          oldMatrix,
          new Cesium.Matrix4()
        )
        let mat3 = Cesium.Matrix4.getMatrix3(m3, new Cesium.Matrix3())
        let q = Cesium.Quaternion.fromRotationMatrix(mat3)
        let hpr = Cesium.HeadingPitchRoll.fromQuaternion(q)
        let headingPitchRoll, num
        if (mechanicalArray.length > 1) {
          if (
            mechanicalArray[mechanicalArray.length - 1] >
            mechanicalArray[mechanicalArray.length - 2]
          ) {
            num = mechanicalArray[mechanicalArray.length - 1] - mechanicalArray[mechanicalArray.length - 2]
            headingPitchRoll = new Cesium.HeadingPitchRoll(
              (hpr.heading += Cesium.Math.toRadians(num)),
              hpr.pitch,
              hpr.roll
            )
            this.tableData[3].value += num
          } else {
            num = mechanicalArray[mechanicalArray.length - 2] - mechanicalArray[mechanicalArray.length - 1]
            headingPitchRoll = new Cesium.HeadingPitchRoll(
              (hpr.heading -= Cesium.Math.toRadians(num)),
              hpr.pitch,
              hpr.roll
            )
            this.tableData[3].value -= num
          }
        }
        let m = Cesium.Transforms.headingPitchRollToFixedFrame(
          oldCenter,
          headingPitchRoll,
          Cesium.Ellipsoid.WGS84,
          Cesium.Transforms.eastNorthUpToFixedFrame,
          new Cesium.Matrix4()
        )
        pickObject[0].primitive.modelMatrix = m
      }
      if (mimos.length > 0) {
        mimos.forEach((item) => {
          let oldMatrix = item.modelMatrix
          let oldCenter = new Cesium.Cartesian3(
            oldMatrix[12],
            oldMatrix[13],
            oldMatrix[14]
          )
          let m1 = Cesium.Transforms.eastNorthUpToFixedFrame(
            Cesium.Matrix4.getTranslation(oldMatrix, new Cesium.Cartesian3()),
            Cesium.Ellipsoid.WGS84,
            new Cesium.Matrix4()
          )
          let m3 = Cesium.Matrix4.multiply(
            Cesium.Matrix4.inverse(m1, new Cesium.Matrix4()),
            oldMatrix,
            new Cesium.Matrix4()
          )
          let mat3 = Cesium.Matrix4.getMatrix3(m3, new Cesium.Matrix3())
          let q = Cesium.Quaternion.fromRotationMatrix(mat3)
          let hpr = Cesium.HeadingPitchRoll.fromQuaternion(q)
          let headingPitchRoll, num
          if (mechanicalArray.length > 1) {
            if (
              mechanicalArray[mechanicalArray.length - 1] >
              mechanicalArray[mechanicalArray.length - 2]
            ) {
              num = mechanicalArray[mechanicalArray.length - 1] - mechanicalArray[mechanicalArray.length - 2]
              headingPitchRoll = new Cesium.HeadingPitchRoll(
                (hpr.heading += Cesium.Math.toRadians(num)),
                hpr.pitch,
                hpr.roll
              )
            } else {
              num = mechanicalArray[mechanicalArray.length - 2] - mechanicalArray[mechanicalArray.length - 1]
              headingPitchRoll = new Cesium.HeadingPitchRoll(
                (hpr.heading -= Cesium.Math.toRadians(num)),
                hpr.pitch,
                hpr.roll
              )
            }
          }
          let m = Cesium.Transforms.headingPitchRollToFixedFrame(
            oldCenter,
            headingPitchRoll,
            Cesium.Ellipsoid.WGS84,
            Cesium.Transforms.eastNorthUpToFixedFrame,
            new Cesium.Matrix4()
          )
          item.modelMatrix = m
        })
      }
    },
    //随机颜色方法
    randomColor() {
      let col = '#'
      for (let i = 0; i < 6; i++)
        col += parseInt(Math.random() * 16).toString(16)
      return col
    },
    //天空
    skyboxs(skyImg) {
      let groundSkybox = new Cesium.GroundSkyBox({
        sources: {
          positiveX: skyImg.positiveX,
          negativeX: skyImg.negativeX,
          positiveY: skyImg.positiveY,
          negativeY: skyImg.negativeY,
          positiveZ: skyImg.positiveZ,
          negativeZ: skyImg.negativeZ,
        },
      })
      let defaultSkybox = viewer.scene.skyBox
      // // 渲染前监听并判断相机位置
      viewer.scene.preUpdate.addEventListener(() => {
        let position = viewer.scene.camera.position
        let cameraHeight = Cesium.Cartographic.fromCartesian(position).height
        if (cameraHeight < 240000) {
          viewer.scene.skyBox = groundSkybox
          viewer.scene.skyAtmosphere.show = false
        } else {
          viewer.scene.skyBox = defaultSkybox
          viewer.scene.skyAtmosphere.show = true
        }
      })
    },
    //标注
    poiIconLabelAdd(labelCanshu) {
      viewer.entities.add({
        position: Cesium.Cartesian3.fromDegrees(
          labelCanshu.lon,
          labelCanshu.lat,
          420
        ),
        label: {
          // 文本。支持显式换行符“ \ n”
          text:
            '联通西南数据中心1号楼数据机房' +
            '\n' +
            ' ' +
            '\n' +
            '              机架数：' +
            parseInt(window.g.jiguiNum) +
            '个',
          color: Cesium.Color.fromCssColorString('#fff'),
          // 字体样式，以CSS语法指定字体
          font: '10pt Helvetica',
          // 字体颜色
          fillColor: Cesium.Color.WHITE,
          // 背景颜色
          backgroundColor: Cesium.Color.fromCssColorString(
            'rgba(254, 129, 6, 0.2)'
          ),
          // 是否显示背景颜色
          showBackground: false,
          // 字体边框
          outline: true,
          // 字体边框颜色
          outlineColor: Cesium.Color.WHITE,
          // 字体边框尺寸
          outlineWidth: 1,
          // 应用于图像的统一比例。比例大于会1.0放大标签，而比例小于会1.0缩小标签。
          scale: 1,
          // 设置样式：FILL：填写标签的文本，但不要勾勒轮廓；OUTLINE：概述标签的文本，但不要填写；FILL_AND_OUTLINE：填写并概述标签文本。
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          // 相对于坐标的水平位置
          verticalOrigin: Cesium.VerticalOrigin.CENTER,
          // 相对于坐标的水平位置
          horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
          // 该属性指定标签在屏幕空间中距此标签原点的像素偏移量
          pixelOffset: new Cesium.Cartesian2(-95, 30),
          // 是否显示
          show: true,
          distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
            0,
            2000
          ),
          eyeOffset: new Cesium.Cartesian3(0, 0, -1),
          // translucencyByDistance: new Cesium.NearFarScalar(0, 1, 3000, 1)//随着距离改变透明度
        },
        billboard: {
          // 图像地址，URI或Canvas的属性
          image: label,
          // 设置颜色和透明度
          color: Cesium.Color.WHITE.withAlpha(1),
          // 高度（以像素为单位）
          height: 160.06,
          // 宽度（以像素为单位）
          width: 270.17,
          // 逆时针旋转
          // rotation: 20,
          // 大小是否以米为单位
          sizeInMeters: false,
          // 相对于坐标的垂直位置
          // verticalOrigin: Cesium.VerticalOrigin.CENTER,
          // 相对于坐标的水平位置
          // horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
          // 该属性指定标签在屏幕空间中距此标签原点的像素偏移量
          eyeOffset: new Cesium.Cartesian3(0, 0, 1),
          pixelOffset: new Cesium.Cartesian2(0, 35),
          // 应用于图像的统一比例。比例大于会1.0放大标签，而比例小于会1.0缩小标签。
          scale: 1,
          // 是否显示
          show: true,
          //视角缩放范围 控制显示隐藏
          distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
            0,
            2000
          ),
          // translucencyByDistance: new Cesium.NearFarScalar(0, 1, 2000, 0)//随着距离改变透明度
        },
      })
      // 先画线后画点，防止线压盖点
      let linePositions = []
      linePositions.push(
        new Cesium.Cartesian3.fromDegrees(
          labelCanshu.lon,
          labelCanshu.lat,
          labelCanshu.minheight
        )
      )
      linePositions.push(
        new Cesium.Cartesian3.fromDegrees(
          labelCanshu.lon,
          labelCanshu.lat,
          labelCanshu.maxheight
        )
      )
      viewer.entities.add({
        polyline: {
          positions: linePositions,
          width: 1.5,
          material: Cesium.Color.fromCssColorString(labelCanshu.color),
          distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
            0,
            2000
          ),
        },
      })
      // 画点
      viewer.entities.add({
        // 给初始点位设置一定的离地高度，否者会被压盖
        position: Cesium.Cartesian3.fromDegrees(
          labelCanshu.lon,
          labelCanshu.lat,
          labelCanshu.minheight
        ),
        point: {
          color: Cesium.Color.fromCssColorString(labelCanshu.color),
          pixelSize: 8,
          distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
            0,
            2000
          ),
        },
      })
    },
    //daolu
    loadRoadGeojson() {
      let viewer = window.viewer
      let colors = new Cesium.Color(77 / 255, 201 / 255, 255 / 255, 1)
      let colors1 = new Cesium.Color(244 / 255, 243 / 255, 21 / 255, 0.5)
      let colors2 = new Cesium.Color(77 / 255, 255 / 255, 52 / 255, 1)
      let colors3 = new Cesium.Color(30 / 255, 150 / 255, 255 / 255, 1)
      let duration = [1500, 5000]
      let getCustomMaterialLine = (image, color, duration) => {
        return new Cesium.CustomMaterialLine({
          image: image,
          color: color,
          duration: duration,
        })
      }
      //国道
      let zzguodao = Cesium.GeoJsonDataSource.load(daolu, {
        clampToGround: true,
      })
      zzguodao.then((dataSource) => {
        viewer.dataSources.add(dataSource)
        var entities = dataSource.entities.values
        for (var i = 0; i < entities.length; i++) {
          var line = entities[i]
          line.polyline.material = getCustomMaterialLine(
            lineColor,
            colors1,
            8000
          )
          line.polyline.width = 25
          line.polyline.glowPower = 1
        }
      })
    },
    //加载大唐不夜城附近基站
    loadbycBeas() {
      let position = Cesium.Cartesian3.fromDegrees(108.958393, 34.216579, 12)
      let heading = Cesium.Math.toRadians(35)
      let pitch = 0
      let roll = 0
      let hpRoll = new Cesium.HeadingPitchRoll(heading, pitch, roll)
      let fixedFrameTransforms =
        Cesium.Transforms.localFrameToFixedFrameGenerator('north', 'west')
      // Cesium.Transforms.localFrameToFixedFrameGenerator("south", "east");
      jizhan = viewer.scene.primitives.add(
        Cesium.Model.fromGltf({
          url: 'http://*************:59083/models/gltf/%E6%9C%BA%E6%88%BF.glb',
          modelMatrix: Cesium.Transforms.headingPitchRollToFixedFrame(
            position,
            hpRoll,
            Cesium.Ellipsoid.WGS84,
            fixedFrameTransforms
          ),
          scale: 1,
          minimumPixelSize: 40,
          maximumPixelSize: 40,
          lightColor: new Cesium.Cartesian3(100, 100, 100),
        })
      )
      jifang = viewer.scene.primitives.add(
        Cesium.Model.fromGltf({
          url: 'http://*************:59083/models/gltf/%E7%BE%8E%E5%8C%96%E5%9F%BA%E7%AB%99.glb',
          modelMatrix: Cesium.Transforms.headingPitchRollToFixedFrame(
            position,
            hpRoll,
            Cesium.Ellipsoid.WGS84,
            fixedFrameTransforms
          ),
          scale: 1,
          minimumPixelSize: 40,
          maximumPixelSize: 40,
          lightColor: new Cesium.Cartesian3(100, 100, 100),
        })
      )
    },
    //点击事件
    clickFeatures(pmaras) {
      let that = this
      if (pmaras) {
        this.loadingModel = false
        this.loadText = true
        this.$message.success('数据加载完成！')
      }
      new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas).setInputAction(
        (e) => {
          {  //  初始化
            document.querySelector('.tianxian').style.display = 'none'
            document.querySelector('.station').style.display = 'none'
          }
          let pick = viewer.scene.pick(e.position)
          let cartesian = viewer.scene.pickPosition(e.position)
          let lng, lat, height
          if (cartesian) {
            let cartographic = Cesium.Cartographic.fromCartesian(cartesian)
            lng = Cesium.Math.toDegrees(cartographic.longitude)
            lat = Cesium.Math.toDegrees(cartographic.latitude)
            height = cartographic.height
          }
          // 方位角
          let heading = Cesium.Math.toDegrees(viewer.camera.heading).toFixed(2)
          // 俯仰角
          let pitch = Cesium.Math.toDegrees(viewer.camera.pitch).toFixed(2)
          // 翻滚角
          let roll = Cesium.Math.toDegrees(viewer.camera.roll).toFixed(2)
          var position = viewer.scene.camera.positionCartographic
          var longitude = Cesium.Math.toDegrees(position.longitude).toFixed(6)
          var latitude = Cesium.Math.toDegrees(position.latitude).toFixed(6)
          var height2 = position.height
          // alert('经度' + lng + '纬度' + lat + '高度' + height)
          if (this.icon.icon1) {
            this.loadingModel = true
            this.keyboard_car({
              lng: lng,
              lat: lat,
              height: 0,
              xiaoqufugai: pmaras.coverage,
              limiandian: pmaras.facade_pnts,
              rsrp: pmaras.rsrp,
              antenna: pmaras.antenna,
            })
          }
          //判断点击模型
          if (pickObject.length > 0) {
            pickObject.forEach((item) => {
              item.primitive.color = Cesium.Color.fromCssColorString('#fff')
              item.primitive.lightColor = new Cesium.Cartesian3(20, 20, 20)
            })
            pickObject = []
          }
          if (mimos.length > 0) {
            mimos.forEach((item) => {
              viewer.scene.primitives.remove(item)
            })
            mimos = []
          }
          if (pick && pick.primitive instanceof Cesium.Model) {
            if (pick.id) {
              if (!that.largeScreenShow) {
                that.azimuth = 0
                that.mechanical = 0
                that.tableData = []
                pickObject.push(pick)
                pick.primitive.color =
                  Cesium.Color.fromCssColorString('#FFA300').withAlpha(0.8)
                pick.primitive.lightColor = new Cesium.Cartesian3(200, 200, 200)
                viewer.entities.remove(heatMapjieshou)
                that.value = false
                this.mimoSwitch = false
                antennaArray.forEach((item) => {
                  if (pick.id === item.properties.Transmitte) {
                    that.query_TianxianID = item.properties.Transmitte
                    that.tableData.push(
                      {
                        key: '天线ID',
                        value: item.properties.Transmitte,
                      },
                      {
                        key: '基站ID',
                        value: item.properties.Site,
                      },
                      {
                        key: '安装方式',
                        value: item.properties.安装方,
                      },
                      {
                        key: '方位角',
                        value: item.properties.Azimuth,
                      },
                      {
                        key: '备注',
                        value: item.properties.Comments,
                      },
                      {
                        key: '挂高',
                        value: item.properties.Height,
                      },
                      {
                        key: '机械倾角',
                        value: item.properties.Mechanical,
                      },
                      {
                        key: '频段',
                        value: item.properties.Antenna,
                      }
                    )
                    that.loadMIMO(pick.id)
                  }
                })
                that.$emit('setTianxianInfo', that.query_TianxianID, that.tableData)
                this.azimuthShow = true
                this.zhandianShow = false
              } else {
                that.tableData = []
                pickObject.push(pick)
                pick.primitive.color =
                  Cesium.Color.fromCssColorString('#FFA300').withAlpha(0.8)
                pick.primitive.lightColor = new Cesium.Cartesian3(200, 200, 200)
                bycTransmitterArray.forEach((item) => {
                  if (pick.id === item.properties.Transmitte) {
                    that.query_TianxianID = item.properties.Transmitte
                    that.tableData.push(
                      {
                        key: '天线ID',
                        value: item.properties.Transmitte,
                      },
                      {
                        key: '基站ID',
                        value: item.properties.Site,
                      },
                      {
                        key: '安装方式',
                        value: item.properties.安装方,
                      },
                      {
                        key: '方位角',
                        value: item.properties.Azimuth,
                      },
                      {
                        key: '备注',
                        value: item.properties.Comments,
                      },
                      {
                        key: '频段',
                        value: item.properties.Antenna,
                      }
                    )
                    that.loadMIMO(pick.id, 20)
                  }
                })
                this.azimuthShow = true
                this.zhandianShow = false
              }
            }
          } else {
            viewer.entities.remove(heatMapjieshou)
            this.value = false
            this.azimuthShow = false
            this.zhandianShow = false
            this.legendShow = false
            pickObject = []
            if (mimos.length > 0) {
              mimos.forEach((item) => {
                viewer.scene.primitives.remove(item)
              })
            }
            if (ripple) {
              viewer.entities.remove(ripple)
            }
          }
          //点击基站标签
          if (pick && pick.primitive instanceof Cesium.Billboard) {
            if (!this.largeScreenShow) {
              this.mimoSwitch = false
              that.tableData = []
              let SiteantennaNum = []
              antennaArray.forEach((item) => {
                if (item.properties.Site === pick.id._id) {
                  that.loadMIMO(item.properties.Transmitte)
                  SiteantennaNum.push(item.properties.Transmitte)
                }
              })
              // console.log(antennaArray);    所有小区
              // console.log(jizhanshuju);    所有基站
              jizhanshuju.forEach((item) => {
                if (pick.id._id == item.properties.Site) {
                  that.query_TianxianID = item.properties.Site
                  that.tableData.push(
                    {
                      key: '基站ID',
                      value: item.properties.Site,
                    },
                    {
                      key: '天线挂高',
                      value: item.properties.Height,
                    },
                    {
                      key: '天线数',
                      value: SiteantennaNum.length,
                    },
                    {
                      key: '基站类型',
                      value: item.properties.安装方,
                    }
                  )
                }
              })
              that.$emit('setStationInfo', that.query_TianxianID, that.tableData)
              that.zhandianShow = true
              this.azimuthShow = false
            } else {
              bycTransmitterArray.forEach((item) => {
                if (item.properties.Site == pick.id) {
                  that.loadMIMO(item.properties.Transmitte, 20)
                }
              })
            }
          }
        },
        Cesium.ScreenSpaceEventType.LEFT_CLICK
      )
    },
    //加载不夜城网元
    loadbycwy() {
      if (!this.icon.icon7) {
        this.icon.icon7 = !this.icon.icon7
        this.loadbycsite()
        this.loadbycjizhanazj()
        this.loadbyctianxian()
      } else {
        this.icon.icon7 = !this.icon.icon7
        this.removeBycstart()
      }
    },
    //加载大唐不夜城基站
    async loadbycsite() {
      await this.$http
        .get('http://*************:59083/models/JSON/zhandian/byc/bycsite.json')
        .then((res) => {
          if (res) {
            this.loadbycjizhan(res.data.features)
          }
        })
    },
    loadbycjizhan(pmaras) {
      pmaras.forEach((item) => {
        if (item.properties.安装方 == '单管塔') {
          this.loadWebsite({
            lon: item.geometry.coordinates[0],
            lat: item.geometry.coordinates[1],
            height: 0,
            url: 'http://*************:59083/models/gltf/%E7%BE%8E%E5%8C%96%E5%9F%BA%E7%AB%99.glb',
            scale: 1,
            lightColor: new Cesium.Cartesian3(40, 40, 40),
          })
          this.addMultipleBillboards({
            id: item.properties.Site,
            lon: item.geometry.coordinates[0],
            lat: item.geometry.coordinates[1],
            height: 28,
            image: base,
            width: 32,
            height2: 32,
          })
          if (
            item.properties.Site == '768061' ||
            item.properties.Site == '772024' ||
            item.properties.Site == '769674' ||
            item.properties.Site == '767022' ||
            item.properties.Site == '767047' ||
            item.properties.Site == '770291' ||
            item.properties.Site == '772023' ||
            item.properties.Site == '767033' ||
            item.properties.Site == '767087'
          ) {
            this.loadWebsite({
              lon: item.geometry.coordinates[0],
              lat: item.geometry.coordinates[1],
              height: 0,
              url: 'http://*************:59083/models/gltf/%E6%9C%BA%E6%88%BF.glb',
              scale: 1,
              lightColor: new Cesium.Cartesian3(40, 40, 40),
              heading: 90,
            })
          } else if (item.properties.Site == '767033') {
            this.loadWebsite({
              lon: item.geometry.coordinates[0],
              lat: item.geometry.coordinates[1],
              height: 0,
              url: 'http://*************:59083/models/gltf/%E6%9C%BA%E6%88%BF.glb',
              scale: 1,
              lightColor: new Cesium.Cartesian3(40, 40, 40),
              heading: -30,
            })
          } else {
            //机房
            this.loadWebsite({
              lon: item.geometry.coordinates[0],
              lat: item.geometry.coordinates[1],
              height: 0,
              url: 'http://*************:59083/models/gltf/%E6%9C%BA%E6%88%BF.glb',
              scale: 1,
              lightColor: new Cesium.Cartesian3(40, 40, 40),
            })
          }
        } else {

        }
      })
    },
    //加载大唐不夜城那成天线安装架
    async loadbycjizhanazj() {
      await this.$http
        .get(
          'http://*************:59083/models/JSON/zhandian/byc/byc_transmitter_txzj.json'
        )
        .then((res) => {
          if (res) {
            this.loadbyctransmitter_zj(res.data.features)
          }
        })
    },
    loadbyctransmitter_zj(pmaras) {
      pmaras.forEach((item) => {
        if (item.properties.安装方 == '单管塔') {
          this.xiaoqubaogan_loding({
            lon: item.geometry.coordinates[0],
            lat: item.geometry.coordinates[1],
            height: 20,
            Azimuth: (item.properties.Azimuth - 90) * -1,
            url: 'http://*************:59083/models/gltf/%E5%A4%A9%E7%BA%BF%E6%94%AF%E6%9E%B6.gltf',
            id: item.properties,
            scale: 2,
          })
        }
      })
    },
    //加载不夜城基站天线
    loadbyctianxian() {
      this.$http
        .get(
          'http://*************:59083/models/JSON/zhandian/byc/byc_transmitter_tx.json'
        )
        .then((res) => {
          if (res) {
            bycTransmitterArray = res.data.features
            this.loadbyctransmitter(res.data.features)
          }
        })
    },
    loadbyctransmitter(pmaras) {
      pmaras.forEach((item) => {
        if (item.properties.安装方 == '单管塔') {
          this.loadXiaoqu({
            id: item.properties.Transmitte,
            lon: item.geometry.coordinates[0],
            lat: item.geometry.coordinates[1],
            height: 20 + 0.68 * 2,
            Azimuth: (item.properties.Azimuth - 90) * -1,
            url: 'http://*************:59083/models/JSON/tianxian.gltf',
            scale: 2,
          })
        }
      })
    },
    //双击大唐不夜城边界
    doubleClickFeatures() {
      let that = this
      new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas).setInputAction(
        (e) => {
          let pick = viewer.scene.pick(e.position)
          if (pick && pick.id instanceof Cesium.Entity) {
            if (pick.id._name == '立体墙') {
              wall = pick
              that.btnsShow = !that.btnsShow
              that.chartsShow = !that.chartsShow
              that.icon.icon5 = true
              pick.id._name = 'SSS'
              that.loadwangyuan() //判断后移除网元
              that.bycModelShow() //加载不夜城模型
              //深度检测
              viewer.scene.globe.depthTestAgainstTerrain = false
              setTimeout(() => {
                flyTos({
                  lon: 108.959,
                  lat: 34.2054,
                  height: 10,
                  viewer: viewer,
                  heading: 0,
                  pitch: -8,
                  roll: 0,
                })
              }, 500)
              that.modelName = '西安白膜'
              that.removezhengzhouZGD()
            }
          }
        },
        Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK
      )
    },
    //返回
    returnFn() {
      wall.id._name = '立体墙'
      this.btnsShow = !this.btnsShow
      this.icon.icon5 = false
      this.chartsShow = !this.chartsShow
      this.bycModelShow() //加载不夜城模型
      //深度检测
      viewer.scene.globe.depthTestAgainstTerrain = true
      this.modelName = '大唐不夜城'
      this.removeBycstart()
    },
    //移除基站
    removeBycstart() {
      if (WebsiteArray.length > 0) {
        WebsiteArray.forEach((item) => {
          viewer.scene.primitives.remove(item)
        })
      }
      if (billboardArray.length > 0) {
        billboardArray.forEach((item) => {
          viewer.entities.remove(item)
        })
      }
    },
    //扩散圆
    loadEllipse(ellipsesCanshu) {
      let ellipses = viewer.entities.add({
        position: Cesium.Cartesian3.fromDegrees(
          ellipsesCanshu.lon,
          ellipsesCanshu.lat,
          ellipsesCanshu.alt
        ),
        name: '波纹圆',
        ellipse: {
          semiMinorAxis: ellipsesCanshu.semiMinorAxis,
          semiMajorAxis: ellipsesCanshu.semiMajorAxis,
          material: new Cesium.CircleRippleMaterialProperty({
            color: new Cesium.Color(247 / 255, 255 / 255, 32 / 255, 0.7),
            speed: 12.0,
            count: 4,
            gradient: 0.2,
          }),
        },
      })
      ripple = ellipses
    },
    //热力图
    heatMap() {
      viewer.entities.remove(heatMapjieshou)
      heatMapjieshou = null
      this.value = false
      if (this.heatMapShow) {
        var points = []
        jizhanshuju.forEach((item) => {
          points.push({
            x: Math.floor(
              ((item.geometry.coordinates[0] - lonMin) / (lonMax - lonMin)) *
              10000
            ),
            y: Math.floor(
              (Math.abs(item.geometry.coordinates[1] - latMax) /
                (latMax - latMin)) *
              10000
            ),
            value: Math.floor(item.properties.Height),
          })
        })
        if (!heatMap_Received) {
          heatMap_Received = h337.create({
            container: document.querySelector('#heatMap'),
          })
        }
        let data = {
          max: 25,
          data: points,
        }
        heatMap_Received.setData(data)
        this.stickHot()
        this.heatMapShow = false
        // this.showFs = '关闭'
      } else {
        this.heatMapShow = true
        viewer.entities.remove(heatMapjieshou)
        // this.showFs = '开启'
      }
    },
    //挂高热力图
    guagaoHeatMap(val) {
      if (val == '1') {
        viewer.entities.remove(heatMapjieshou)
        let points = []
        tianxianshuju.forEach((item) => {
          if (item.properties.Height < 25) {
            points.push({
              x: Math.floor(
                ((item.geometry.coordinates[0] - lonMin) / (lonMax - lonMin)) *
                10000
              ),
              y: Math.floor(
                (Math.abs(item.geometry.coordinates[1] - latMax) /
                  (latMax - latMin)) *
                10000
              ),
              value: Math.floor(item.properties.Height),
            })
          }
        })
        if (!heatMap_Received) {
          heatMap_Received = h337.create({
            container: document.querySelector('#heatMap'),
          })
        }
        let data = {
          max: 25,
          data: points,
        }
        heatMap_Received.setData(data)
        this.stickHot()
      } else if (val == '2') {
        let points = []
        viewer.entities.remove(heatMapjieshou)
        tianxianshuju.forEach((item) => {
          if (item.properties.Height >= 25 && item.properties.Height < 40) {
            points.push({
              x: Math.floor(
                ((item.geometry.coordinates[0] - lonMin) / (lonMax - lonMin)) *
                10000
              ),
              y: Math.floor(
                (Math.abs(item.geometry.coordinates[1] - latMax) /
                  (latMax - latMin)) *
                10000
              ),
              value: Math.floor(item.properties.Height),
            })
          }
        })
        if (!heatMap_Received) {
          heatMap_Received = h337.create({
            container: document.querySelector('#heatMap'),
          })
        }
        let data = {
          max: 25,
          data: points,
        }
        heatMap_Received.setData(data)
        this.stickHot()
      } else if (val == '3') {
        let points = []
        viewer.entities.remove(heatMapjieshou)
        tianxianshuju.forEach((item) => {
          if (item.properties.Height >= 40 && item.properties.Height < 80) {
            points.push({
              x: Math.floor(
                ((item.geometry.coordinates[0] - lonMin) / (lonMax - lonMin)) *
                10000
              ),
              y: Math.floor(
                (Math.abs(item.geometry.coordinates[1] - latMax) /
                  (latMax - latMin)) *
                10000
              ),
              value: Math.floor(item.properties.Height),
            })
          }
        })
        if (!heatMap_Received) {
          heatMap_Received = h337.create({
            container: document.querySelector('#heatMap'),
          })
        }
        let data = {
          max: 25,
          data: points,
        }
        heatMap_Received.setData(data)
        this.stickHot()
      } else {
        let points = []
        viewer.entities.remove(heatMapjieshou)
        tianxianshuju.forEach((item) => {
          if (item.properties.Height >= 80) {
            points.push({
              x: Math.floor(
                ((item.geometry.coordinates[0] - lonMin) / (lonMax - lonMin)) *
                10000
              ),
              y: Math.floor(
                (Math.abs(item.geometry.coordinates[1] - latMax) /
                  (latMax - latMin)) *
                10000
              ),
              value: Math.floor(item.properties.Height),
            })
          }
        })
        if (!heatMap_Received) {
          heatMap_Received = h337.create({
            container: document.querySelector('#heatMap'),
          })
        }
        let data = {
          max: 25,
          data: points,
        }
        heatMap_Received.setData(data)
        this.stickHot()
      }
    },
    stickHot() {
      /* 把热⼒图铺到地球上 */
      // 设置画布为⽣成的热⼒图
      canvas2 = document.getElementsByClassName('heatmap-canvas')
      // 控制台输出画布数据
      // 添加热⼒图实例
      let heatMap = viewer.entities.add({
        name: 'heatmap',
        // 设置矩形
        rectangle: {
          // 指定矩形区域
          coordinates: Cesium.Rectangle.fromDegrees(
            lonMin,
            latMin,
            lonMax,
            latMax
          ),
          // 设置矩形图⽚为据透明度的热⼒图
          material: new Cesium.ImageMaterialProperty({
            image: canvas2[0],
            transparent: true,
          }),
        },
      })
      heatMapjieshou = heatMap
    },
    //轨迹颜色
    rountColor(num) {
      if (num >= -120 && num < -110) {
        return '#0000FF'
      } else if (num >= -110 && num < -100) {
        return '#008FFF'
      } else if (num >= -100 && num < -90) {
        return '#00FFA5'
      } else if (num >= -90 && num < -80) {
        return '#00FF4B'
      } else if (num >= -80 && num < -70) {
        return '#75FF00'
      } else if (num >= -70 && num < -60) {
        return '#D0FF00'
      } else if (num >= -60 && num < -50) {
        return '#FFF200'
      } else if (num >= -50 && num < -40) {
        return '#FFAB00'
      } else if (num >= -40 && num < -30) {
        return '#FF6F00'
      } else if (num >= -30 && num < -20) {
        return '#FF5303'
      } else {
        return '#FF0000'
      }
    },
    //连线shader
    materialLine() {
      if (typeof Cesium !== 'undefined')
        (function (_0x28e6fb) {
          var _0x3a4a3c = {
            iKSVw: '1|4|0|5|3|2',
            loCcz: function (_0x8baaf6, _0x53c135) {
              return _0x8baaf6(_0x53c135)
            },
            ZVLvT: function (_0x5a6faa, _0x35dbb2) {
              return _0x5a6faa / _0x35dbb2
            },
            OOtAe: function (_0x5ac73d, _0x1d0c26) {
              return _0x5ac73d * _0x1d0c26
            },
            YUfBq: function (_0x150977, _0x34f301) {
              return _0x150977 - _0x34f301
            },
            AAzBp: function (_0x4456b6, _0x489fd6) {
              return _0x4456b6 === _0x489fd6
            },
            zWdDc: '0|1|5|4|6|2|3',
            phCaN: function (_0x22f7fe, _0x22460b, _0x584f20) {
              return _0x22f7fe(_0x22460b, _0x584f20)
            },
            xUWjD: function (_0x2d653f, _0x3fc619) {
              return _0x2d653f === _0x3fc619
            },
            iTcol: function (_0x4dea27, _0x5c701b) {
              return _0x4dea27 instanceof _0x5c701b
            },
            YLCPu: function (_0xbac2e7, _0x417f56) {
              return _0xbac2e7 + _0x417f56
            },
            kDWEL: 'wallType',
            jikaj: function (_0x37f580, _0xb90f4c) {
              return _0x37f580 * _0xb90f4c
            },
            uvOSD: 'color',
            foFmO:
              'czm_material\x20czm_getMaterial(czm_materialInput\x20materialInput)\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20czm_material\x20material\x20=\x20czm_getDefaultMaterial(materialInput);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20vec2\x20st\x20=\x20materialInput.st;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20if(texture2D(image,\x20vec2(0.0,\x200.0)).a\x20==\x201.0){\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20discard;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20}else{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20material.alpha\x20=\x20texture2D(image,\x20vec2(1.0\x20-\x20fract(time\x20-\x20st.s),\x20st.t)).a\x20*\x20color.a;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20material.diffuse\x20=\x20max(color.rgb\x20*\x20material.alpha\x20*\x203.0,\x20color.rgb);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20return\x20material;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20',
            aQpuV:
              'background:\x20#606060;\x20color:\x20#fff;\x20border-radius:\x203px\x200\x200\x203px;',
            XOzEg:
              'background:\x20#1475B2;\x20color:\x20#fff;\x20border-radius:\x200\x203px\x203px\x200;',
          }
          function _0x265ba3(_0x4f3655) {
            var _0x5f1c70 = {
              KioEk: _0x3a4a3c['zWdDc'],
              mPDit: function (_0x1b1c92, _0x330c7a, _0x2b6846) {
                return _0x3a4a3c['phCaN'](_0x1b1c92, _0x330c7a, _0x2b6846)
              },
              zvyOY: function (_0x337a2c, _0x58ba2d) {
                return _0x3a4a3c['xUWjD'](_0x337a2c, _0x58ba2d)
              },
              UBCpg: function (_0x51b947, _0x1cee24) {
                return _0x3a4a3c['iTcol'](_0x51b947, _0x1cee24)
              },
            }
            var _0x578460 = _0x28e6fb['Color'],
              _0x18e133 = _0x28e6fb['defaultValue'],
              _0x4e36fd = _0x28e6fb['defined'],
              _0x285486 = Object['defineProperties'],
              _0xd32598 = _0x28e6fb['Event'],
              _0x35590 = _0x28e6fb['createPropertyDescriptor'],
              _0x1479bd = _0x28e6fb['Property'],
              _0x5236ab = _0x28e6fb['Material'],
              _0x26ead8 = _0x578460['WHITE'],
              _0xc22a2e =
                _0x4f3655['MaterialType'] ||
                _0x3a4a3c['YLCPu'](
                  _0x3a4a3c['kDWEL'],
                  _0x3a4a3c['loCcz'](
                    parseInt,
                    _0x3a4a3c['jikaj'](Math['random'](), 0x3e8)
                  )
                )

            function _0x467c57(_0x22dfb5) {
              var _0x101c3d = _0x5f1c70['KioEk']['split']('|'),
                _0x53d876 = 0x0
              while (!![]) {
                switch (_0x101c3d[_0x53d876++]) {
                  case '0':
                    _0x22dfb5 = _0x5f1c70['mPDit'](
                      _0x18e133,
                      _0x22dfb5,
                      _0x18e133['EMPTY_OBJECT']
                    )
                    continue
                  case '1':
                    this['_definitionChanged'] = new _0xd32598()
                    continue
                  case '2':
                    this['duration'] = _0x22dfb5['duration'] || 0x3e8
                    continue
                  case '3':
                    this['_time'] = undefined
                    continue
                  case '4':
                    this['_colorSubscription'] = undefined
                    continue
                  case '5':
                    this['_color'] = undefined
                    continue
                  case '6':
                    this['color'] =
                      _0x22dfb5['color'] || _0x28e6fb['Color']['BLUE']
                    continue
                }
                break
              }
            }
            _0x3a4a3c['phCaN'](_0x285486, _0x467c57['prototype'], {
              isvarant: {
                get: function () {
                  return ![]
                },
              },
              definitionChanged: {
                get: function () {
                  return this['_definitionChanged']
                },
              },
              color: _0x3a4a3c['loCcz'](_0x35590, _0x3a4a3c['uvOSD']),
            })
            _0x467c57['prototype']['getType'] = function (_0x17bed1) {
              return _0xc22a2e
            }
            _0x467c57['prototype']['getValue'] = function (
              _0x52a446,
              _0x2c759a
            ) {
              var _0x57bbe9 = _0x3a4a3c['iKSVw']['split']('|'),
                _0x2a5c25 = 0x0
              while (!![]) {
                switch (_0x57bbe9[_0x2a5c25++]) {
                  case '0':
                    _0x2c759a['image'] = _0x4f3655['image']
                    continue
                  case '1':
                    if (!_0x3a4a3c['loCcz'](_0x4e36fd, _0x2c759a)) {
                      _0x2c759a = {}
                    }
                    continue
                  case '2':
                    return _0x2c759a
                  case '3':
                    _0x2c759a['time'] = _0x3a4a3c['ZVLvT'](
                      _0x3a4a3c['OOtAe'](
                        _0x3a4a3c['YUfBq'](
                          _0x52a446['secondsOfDay'],
                          this['_time']
                        ),
                        0x3e8
                      ),
                      this['duration']
                    )
                    continue
                  case '4':
                    _0x2c759a['color'] = _0x1479bd['getValueOrClonedDefault'](
                      this['_color'],
                      _0x52a446,
                      _0x26ead8,
                      _0x2c759a['color']
                    )
                    continue
                  case '5':
                    if (_0x3a4a3c['AAzBp'](this['_time'], undefined)) {
                      this['_time'] = _0x52a446['secondsOfDay']
                    }
                    continue
                }
                break
              }
            }
            _0x467c57['prototype']['equals'] = function (_0x47fd34) {
              return (
                _0x5f1c70['zvyOY'](this, _0x47fd34) ||
                (_0x5f1c70['UBCpg'](_0x47fd34, _0x467c57) &&
                  _0x1479bd['equals'](this['_color'], _0x47fd34['_color']))
              )
            }
            _0x5236ab['_materialCache']['addMaterial'](_0xc22a2e, {
              fabric: {
                type: _0xc22a2e,
                uniforms: {
                  color:
                    _0x4f3655['color'] ||
                    new _0x28e6fb['Color'](0x1, 0x0, 0x0, 0.5),
                  image: _0x4f3655['image'],
                  time: _0x4f3655['color']['time'] || 0x0,
                },
                source: _0x3a4a3c['foFmO'],
              },
              translucent: function (_0x5d5e53) {
                return !![]
              },
            })
            return new _0x467c57(_0x4f3655)
          }
          _0x28e6fb['CustomMaterialLine'] = _0x265ba3
          let _0x552062 = _0x3a4a3c['aQpuV']
          let _0x5de97c = _0x3a4a3c['XOzEg']
        })(Cesium)
    },
  },
}
</script>

<style>
.cesium-widget-credits {
  /* 去除logo */
  display: none !important;
}



#box {
  width: 100vw;
  height: 100vh;
}

/* .buttom_lng {
  position: absolute;
  right: 10px;
  bottom: 0px;
  z-index: 9;
}

.navigation-controls {
  background-color: rgba(68, 67, 67, 0.473);
  bottom: 170px !important;
  right: 35px !important;
}

.compass {
  bottom: 250px !important;
  right: 2px !important;
}

.distance-legend {
  bottom: 7px !important;
  right: 470px !important;
}

.distance-legend-label {
  color: #fff !important;
}

.distance-legend-scale-bar {
  border-left: 1px solid #fff !important;
  border-right: 1px solid #fff !important;
  border-bottom: 1px solid #fff !important;
}

.map-operate {
  position: absolute;
  bottom: 50px;
  right: 5px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
} */

/* .box-card {
  width: 400px;
} */
/* .el-table__cell {
  padding: 5px 0 !important;
}

.clears {
  padding: 12px 0;
}

.el-divider {
  margin: 5px 0 !important;
}

#echarstb {
  width: 380px;
  height: 230px;
  text-align: center;
}

.updataBtns {
  padding: 15px 0 0 0;
}

.el-button--primary {
  padding: 13px 20px !important;
  border: none !important;
}

#charts {
  position: absolute;
  width: 230px;
  height: 320px;
  bottom: 0px;
  left: 0px;
  background-image: url("../assets/chart.png");
  background-size: cover;
} */

/* .el-input__inner {
  border-radius: 0 !important;
  width: 350px !important;
} */

/* #citySelect {
  position: absolute;
  top: 20px;
  right: 20px;
}

.tksn {
  float: right;
  cursor: pointer;
  width: 32px;
  height: 32px;
}

.setClassName {
  left: unset !important;
  top: 20px !important;
  right: 120px !important;
}

.clearfix > span {
  display: inline-block;
  font-weight: 700;
  font-size: 18px;
}

.fnSwitch {
  position: absolute;
  top: 20px;
  left: 460px;
  height: 40px;
  width: 150px;
  text-align: center;
  background-color: rgba(250, 250, 250, 0.884);
}

.el-switch {
  margin-top: 10px;
}

.tianqis {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: rgba(255, 255, 255, 0.699);
  border-radius: 5px;
  padding: 5px 10px;
}

.home {
  position: absolute;
  right: 10px;
  top: 118px;
  bottom: 0px;
  width: 30px;
  height: 30px;
  background: url(../assets/home-color.png);
  background-size: 100% 100%;
  border-radius: 7px;
  z-index: 99;
  cursor: pointer;
}

.cesium-performanceDisplay-defaultContainer {
  top: 270px !important;
}

.loadData {
  position: absolute;
  top: 20px;
  left: 20px;
}

.queryServe {
  position: absolute;
  top: 30%;
  left: 30%;
}

.el-input--suffix .el-input__inner {
  padding-right: 131px !important;
}

.box-card {
  position: absolute;
  top: 105px;
  right: 390px;
  width: 300px;
  background-color: rgba(255, 255, 255, 0) !important;
  border: none !important;
  backdrop-filter: blur(80px);
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.288);
}

.demonstration {
  font-size: 17px;
  color: #fff;
  font-family: "pmzd";
  font-weight: 400;
}

.el-slider__bar {
  background-color: #70e6ff !important;
}

.el-slider__button {
  border: 2px solid #70e6ff !important;
}

.el-card__header {
  border: none !important;
  border-bottom: 6px solid transparent !important;
  border-image: url("../assets/box-border.png") 30 30 stretch !important;
}

.clearfix {
  font-family: "pmzd";
  font-size: 24px;
  color: #ffffff !important;
}

.load_car {
  position: absolute;
  top: 20px;
  left: 20px;
}

#box1 {
  width: 461px;
  height: 327px;
  position: absolute;
  bottom: 100px;
  left: 730px;
  background-color: #fff;
  border-radius: 30px 30px 10px 10px;
}

#box1 .cesium-viewer {
  border-radius: 30px 30px 10px 10px;
}

.hud {
  position: absolute;
  width: 200px;
  height: 100px;
  left: 856px;
  top: 74px;
  border-radius: 5px;
  text-align: center;
} */
/* 
@font-face {
  font-family: mFont;
  src: url(../assets/DS-DIGIB.ttf);
}

.hud span {
  display: inline-block;
  font-family: mFont;
  line-height: 100px;
  font-size: 40px;
}

.daping {
  position: absolute !important;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none !important;
}

.baseXqing {
  background-color: rgba(141, 149, 156, 0.29);
  padding: 5px;
}

.baseXqing > td {
  color: #ffffff;
  font-weight: 400;
  font-size: 0.7vw;
}

.td1 {
  font-weight: 500;
  font-size: 0.8vw;
  color: #70e6ff !important;
  font-family: "pmzd";
}

.loadingModelPromise {
  position: absolute;
  right: 90px;
  bottom: 20px;
  width: 200px;
  height: 50px;
  background-color: rgba(255, 255, 255, 0) !important;
  font-size: 17px;
  color: #fff;
  font-family: "pmzd";
  font-weight: 400;
}

.return {
  position: absolute;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  top: 120px;
  left: 400px;
  backdrop-filter: blur(80px);
  cursor: pointer;
  text-align: center;
}

.return :active {
  transform: scale(0.9);
}

.el-input__inner {
  width: 130px !important;
}

.el-input {
  top: -40 px;
  left: 100 px;
}

.el-dialog {
  height: 370px;
}

.el-dialog__footer {
  position: relative;
  bottom: 60px;
}

.pouqieshendu {
  position: absolute;
  background-color: #fff;
  top: 30px;
  left: 48%;
  width: 100px;
  height: 50px;
  border-radius: 5px;
  text-align: center;
}

.pouqieshendu span {
  display: inline-block;
  font-family: mFont;
  line-height: 50px;
  font-size: 40px;
  color: #70e6ff;
}

.wycz {
  width: 340px;
}

#table2 > tbody {
  display: -webkit-box;
  overflow-y: scroll !important;
}

.cell {
  color: #000;
  font-family: "pmzd";
}

.zhedie {
  position: absolute;
  width: 30px;
  height: 30px;
  top: 20px;
  left: 280px;
  background-image: url("../assets/open.svg");
  background-position: center center;
  background-size: cover;
  cursor: pointer;
}

.zhedie {
  background-image: url("../assets/down.svg");
}

.leftBack {
  width: 20%;
  height: calc(100vh - 105px);
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: 0;
  border-radius: 0 10px 0 0;
  box-shadow: 0 0 30px rgb(61, 61, 61);
  backdrop-filter: blur(50px);
}

.rightBack {
  width: 20%;
  height: calc(100vh - 105px);
  position: absolute;
  right: 0;
  bottom: 0;
  z-index: 0;
  border-radius: 10px 0 0 0;
  box-shadow: 0 0 30px rgb(61, 61, 61);
  backdrop-filter: blur(50px);
}

.el-slider__runway {
  margin: 0 !important;
} */
</style>
