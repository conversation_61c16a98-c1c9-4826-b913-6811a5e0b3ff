
import * as echarts from 'echarts'

export let charts = []
const PieColor = [
    new echarts.graphic.LinearGradient(0, 1, 0, 0, [{ offset: 0, color: '#00C3EE' }, { offset: 1, color: '#00C3EE00' }], false),
    new echarts.graphic.LinearGradient(0, 1, 0, 0, [{ offset: 0, color: '#0066FF' }, { offset: 1, color: '#0066FF00' }], false),
    new echarts.graphic.LinearGradient(0, 1, 0, 0, [{ offset: 0, color: '#CFB0F9' }, { offset: 1, color: '#CFB0F900' }], false),
    new echarts.graphic.LinearGradient(0, 1, 0, 0, [{ offset: 0, color: '#FFC589' }, { offset: 1, color: '#FFC58900' }], false),
    new echarts.graphic.LinearGradient(0, 1, 0, 0, [{ offset: 0, color: '#37DA9E' }, { offset: 1, color: '#37DA9E00' }], false),
    new echarts.graphic.LinearGradient(0, 1, 0, 0, [{ offset: 0, color: '#FF827E' }, { offset: 1, color: '#FF827E00' }], false),
  ]
export function pieChart(id, config = {}, color,) {

    const dom = document.getElementById(id)
    charts[id] = echarts.init(dom)
    const data1 = [
        { value: 40, name: '极好', length: 'block' },
        { value: 38, name: '好', length: 'block' },
        { value: 32, name: '较好', length: 'block' },
        { value: 30, name: '中', length: 'block' },
        { value: 28, name: '差', length: 'block' },
        { value: 26, name: '极差', length: 'block' },
    ]
    const option = {
        tooltip: {
            trigger: 'item'
        },
        legend: {
            top: '5%',
            left: 'center',
            show:false
        },
        series: [
            {
                type: 'pie',
                radius: config.radius || ['66%', '73%'],
                avoidLabelOverlap: false,
                itemStyle: {
                    borderRadius: 0,
                    borderColor: '#fff',
                    borderWidth: 0
                },
                label: {
                    show: false,
                    position: 'center'
                },
                // emphasis: {
                //     label: {
                //         show: true,
                //         fontSize: 40,
                //         fontWeight: 'bold'
                //     }
                // },
                labelLine: {
                    show: false
                },
                data: config.data ? config.data[0] : [1,2,3,4,5,6]
            },
            {
                name: 'Access From',
                type: 'pie',
                radius: config.radius || ['58%', '66%'],
                avoidLabelOverlap: false,
                itemStyle: {
                    borderRadius: 0,
                    borderColor: '#fff',
                    borderWidth: 0
                },
                label: {
                    show: false,
                    position: 'center'
                },
                // emphasis: {
                //     label: {
                //         show: true,
                //         fontSize: 40,
                //         fontWeight: 'bold'
                //     }
                // },
                labelLine: {
                    show: false
                },
                data: config.data ? config.data[1] : [1,2,3,4,5,6]
            }
        ]
    };
    charts[id].setOption(option)
}
export function barChart(id,config = {}) {
    const dom = document.getElementById(id)
    charts[id] = echarts.init(dom)

    
    const option = {
        
    tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
        }
      },
      legend: {
        type: 'scroll',
        textStyle: {
          // 图例字体大小
          fontSize: 12,
          color: 'aliceBlue'
        },
        itemHeight: 10,
        top: '3%',
        right: '5%'
      },
        grid: {
            top: '18%',
            left: '5%',
            right: '7%',
            bottom: '5%',
            containLabel: true
        },
        xAxis: {
          type: 'category',
          data: config.dataOfX || [],
          axisLine: {
            lineStyle: { color: '#b0d7ff'}
          },
          axisTick: { show: false},
          axisLabel: {
            show: true,
            rotate: config.rotate || 0,
            textStyle: {
              color: 'aliceblue'
            }
          }
        },
        yAxis: {
          type: 'value',
          name: '单位:个',
          nameTextStyle: { color: 'aliceblue'},
          axisLine: {
            show: true,
            lineStyle: {
              color: '#dbdbdb30'
            }
          },
          splitLine: {
            // 网格线
            show: true,
            lineStyle: {
              color: '#dbdbdb30'
            }
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: 'aliceblue'
            }
          }
        },
        series: [
          {
            stack: "1",
            name:'当前值',
            data: config.data || [120, 200, 150, 80, 70, 110, 130],
            type: 'bar',
            barGap: '-100%',
            barWidth: 15,
            itemStyle: {
            //   barBorderRadius: [2, 2, 0, 0],
              // color: '#116df9'
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                offset: 0,
                color: '#00DAFF'
              }, {
                offset: 1,
                color: '#00DAFF10'
              }])
            },
            // renderItem: renderItem,
          },
          {
            stack: "1",
            type: "bar",
            data: [5, 5, 5, 5, 5, 5,5],
            itemStyle: {
              normal: {
                color: "#fff",
              },
            },
            
                emphasis: {
                    label: {
                        show: false,
                        fontSize: 40,
                        fontWeight: 'bold'
                    }
                },
          },
        ]
    };
    charts[id].setOption(option)
}
export function lineChart(id, config = {}) {
    const dom = document.getElementById(id)
    charts[id] = echarts.init(dom)
  
    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
        }
      },
      legend: {
        type: 'plain',
        textStyle: {
          fontSize: 12,
          color: 'aliceBlue'
        },
        itemHeight: 10,    // 图例大小
        top: '3%',
        right: '5%',
        formatter: (() => { return "当前值" })
      },
      grid: {
        top: '18%',
        left: '5%',
        right: '7%',
        bottom: '5%',
        containLabel: true
      },
      xAxis: [
        {
          type: 'category',
          boundaryGap: false,
          data: config.dataOfX || ['00:00', "02:00", "04:00", "06:00", "08:00", "010:00", "12:00"],
          axisLine: {
            lineStyle: {
              color: '#b0d7ff'
            }
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            show: true,
            rotate: config.rotate || 0,
            textStyle: {
              color: 'aliceblue'
            }
          }
        }
      ],
      yAxis: [
        {
          nameLocation: config.nameLocation || 'end',
          inverse: config.inverse || false,
          type: 'value',
          name: config.nameY || '',
          nameTextStyle: {
            color: 'aliceblue'
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#dbdbdb30'
            }
          },
          splitLine: {
            // 网格线
            show: true,
            lineStyle: {
              color: '#dbdbdb30'
            }
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: 'aliceblue'
            }
          }
        }
      ],
      series: [
        {
          name: '天线数量',
          type: 'line',
          smooth: false,
          showSymbol: true,
          symbolSize: 10,
          barGap: '-100%',
          lineStyle: {
            color: config.lineColor || '#ffa30f', // 阴影部分
            shadowOffsetX: 0, // 折线的X偏移
            shadowOffsetY: 30, // 折线的Y偏移
            shadowBlur: 40 // 折线模糊
          },
          areaStyle: {
            color: config.areaColor || new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0.1,
                color: '#ffa30f31'
              },
              {
                offset: 0.3,
                color: '#ffa30f70'
              },
              {
                offset: 0.5,
                color: '#ffa30f31'
              },
              {
                offset: 1,
                color: '#ffa30f00'
              }
            ])
          },
          emphasis: {
            focus: 'series'
          },
          data: config.data || [1, 3, 9, 5, 2, 7, 3]
        },
      ]
    }
  
    charts[id].setOption(option)
}