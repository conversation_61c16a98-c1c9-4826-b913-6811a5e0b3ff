package com.startel.common.utils;

import com.startel.common.core.domain.AjaxResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * Python脚本执行工具类
 * 用于执行存放在startel-rf-propagation模块resources目录下的Python脚本
 * 
 * <AUTHOR>
 */
public class PythonExecutorUtil {
    
    private static final Logger log = LoggerFactory.getLogger(PythonExecutorUtil.class);
    
    /** Python命令 - 可以通过系统属性配置 */
    private static final String PYTHON_COMMAND = System.getProperty("python.command", "python");

    /** 备用Python命令列表 */
    private static final String[] PYTHON_COMMANDS = {"python3", "python", "py"};

    /** 缓存的可用Python命令 */
    private static volatile String availablePythonCommand = null;
    
    /** 脚本执行超时时间（秒） */
    private static final int TIMEOUT_SECONDS = 300;
    
    /** Python脚本资源路径前缀 */
    private static final String SCRIPT_RESOURCE_PATH = "classpath:";
    
    /**
     * 执行Python脚本
     * 
     * @param scriptName 脚本文件名（不包含路径）
     * @param args 脚本参数
     * @return 执行结果
     */
    public static AjaxResult executeScript(String scriptName, String... args) {
        log.info("开始执行Python脚本: {}, 参数: {}", scriptName, Arrays.toString(args));
        
        Process process = null;
        try {
            // 验证脚本文件是否存在
            String scriptPath = getScriptPath(scriptName);
            if (scriptPath == null) {
                log.error("Python脚本文件不存在: {}", scriptName);
                return AjaxResult.error("脚本文件不存在: " + scriptName);
            }
            
            // 构建命令
            List<String> command = buildCommand(scriptPath, args);
            
            // 创建ProcessBuilder
            ProcessBuilder pb = new ProcessBuilder(command);
            pb.redirectErrorStream(true); // 合并错误流和输出流

            // 设置Python环境变量，确保使用UTF-8编码
            Map<String, String> env = pb.environment();
            env.put("PYTHONIOENCODING", "utf-8");
            env.put("PYTHONLEGACYWINDOWSSTDIO", "utf-8");

            // 启动进程
            process = pb.start();
            
            // 等待进程完成
            boolean finished = process.waitFor(TIMEOUT_SECONDS, TimeUnit.SECONDS);
            
            if (!finished) {
                log.error("Python脚本执行超时: {}", scriptName);
                process.destroyForcibly();
                return AjaxResult.error("脚本执行超时");
            }
            
            int exitCode = process.exitValue();
            log.info("Python脚本执行完成: {}, 退出码: {}", scriptName, exitCode);
            
            if (exitCode == 0) {
                return AjaxResult.success("脚本执行成功");
            } else {
                String errorOutput = readProcessOutput(process.getInputStream());
                log.error("Python脚本执行失败: {}, 退出码: {}, 错误信息: {}", scriptName, exitCode, errorOutput);
                return AjaxResult.error("脚本执行失败，退出码: " + exitCode);
            }
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Python脚本执行被中断: {}", scriptName, e);
            return AjaxResult.error("脚本执行被中断");
        } catch (IOException e) {
            log.error("执行Python脚本时发生IO异常: {}", scriptName, e);
            return AjaxResult.error("脚本执行IO异常: " + e.getMessage());
        } catch (Exception e) {
            log.error("执行Python脚本时发生未知异常: {}", scriptName, e);
            return AjaxResult.error("脚本执行异常: " + e.getMessage());
        } finally {
            if (process != null) {
                closeProcessStreams(process);
                process.destroy();
            }
        }
    }
    
    /**
     * 执行Python脚本并返回输出结果
     * 
     * @param scriptName 脚本文件名
     * @param args 脚本参数
     * @return 包含输出结果的AjaxResult
     */
    public static AjaxResult executeScriptWithOutput(String scriptName, String... args) {
        log.info("开始执行Python脚本并获取输出: {}, 参数: {}", scriptName, Arrays.toString(args));
        
        Process process = null;
        try {
            // 验证脚本文件是否存在
            String scriptPath = getScriptPath(scriptName);
            if (scriptPath == null) {
                log.error("Python脚本文件不存在: {}", scriptName);
                return AjaxResult.error("脚本文件不存在: " + scriptName);
            }
            
            // 构建命令
            List<String> command = buildCommand(scriptPath, args);
            
            // 创建ProcessBuilder
            ProcessBuilder pb = new ProcessBuilder(command);

            // 设置Python环境变量，确保使用UTF-8编码
            Map<String, String> env = pb.environment();
            env.put("PYTHONIOENCODING", "utf-8");
            env.put("PYTHONLEGACYWINDOWSSTDIO", "utf-8");

            // 启动进程
            process = pb.start();
            
            // 读取输出
            String output = readProcessOutput(process.getInputStream());
            String errorOutput = readProcessOutput(process.getErrorStream());
            
            // 等待进程完成
            boolean finished = process.waitFor(TIMEOUT_SECONDS, TimeUnit.SECONDS);
            
            if (!finished) {
                log.error("Python脚本执行超时: {}", scriptName);
                process.destroyForcibly();
                return AjaxResult.error("脚本执行超时");
            }
            
            int exitCode = process.exitValue();
            log.info("Python脚本执行完成: {}, 退出码: {}", scriptName, exitCode);
            
            if (exitCode == 0) {
                log.debug("Python脚本输出: {}", output);
                return AjaxResult.success("脚本执行成功", output);
            } else {
                log.error("Python脚本执行失败: {}, 退出码: {}, 错误信息: {}", scriptName, exitCode, errorOutput);
                return AjaxResult.error("脚本执行失败，退出码: " + exitCode, errorOutput);
            }
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Python脚本执行被中断: {}", scriptName, e);
            return AjaxResult.error("脚本执行被中断");
        } catch (IOException e) {
            log.error("执行Python脚本时发生IO异常: {}", scriptName, e);
            return AjaxResult.error("脚本执行IO异常: " + e.getMessage());
        } catch (Exception e) {
            log.error("执行Python脚本时发生未知异常: {}", scriptName, e);
            return AjaxResult.error("脚本执行异常: " + e.getMessage());
        } finally {
            if (process != null) {
                closeProcessStreams(process);
                process.destroy();
            }
        }
    }
    
    /**
     * 异步执行Python脚本
     * 
     * @param scriptName 脚本文件名
     * @param args 脚本参数
     * @return CompletableFuture包装的执行结果
     */
    public static CompletableFuture<AjaxResult> executeScriptAsync(String scriptName, String... args) {
        log.info("开始异步执行Python脚本: {}, 参数: {}", scriptName, Arrays.toString(args));
        
        return CompletableFuture.supplyAsync(() -> {
            return executeScriptWithOutput(scriptName, args);
        }).exceptionally(throwable -> {
            log.error("异步执行Python脚本时发生异常: {}", scriptName, throwable);
            return AjaxResult.error("异步执行脚本异常: " + throwable.getMessage());
        });
    }
    
    /**
     * 获取脚本文件的完整路径
     * 
     * @param scriptName 脚本文件名
     * @return 脚本文件路径，如果文件不存在返回null
     */
    private static String getScriptPath(String scriptName) {
        try {
            // 尝试从startel-rf-propagation模块的resources目录加载脚本
            ClassPathResource resource = new ClassPathResource(scriptName);
            if (resource.exists()) {
                return resource.getFile().getAbsolutePath();
            }
            
            // 如果直接加载失败，尝试从不同的路径加载
            String[] possiblePaths = {
                scriptName,
                "scripts/" + scriptName,
                "python/" + scriptName
            };
            
            for (String path : possiblePaths) {
                resource = new ClassPathResource(path);
                if (resource.exists()) {
                    return resource.getFile().getAbsolutePath();
                }
            }
            
            log.warn("未找到Python脚本文件: {}", scriptName);
            return null;
            
        } catch (IOException e) {
            log.error("获取脚本路径时发生异常: {}", scriptName, e);
            return null;
        }
    }
    
    /**
     * 构建执行命令
     *
     * @param scriptPath 脚本路径
     * @param args 参数
     * @return 命令列表
     */
    private static List<String> buildCommand(String scriptPath, String... args) {
        List<String> command = new ArrayList<>();

        // 获取可用的Python命令
        String pythonCmd = getAvailablePythonCommand();
        if (pythonCmd == null) {
            throw new RuntimeException("未找到可用的Python解释器");
        }

        command.add(pythonCmd);
        command.add(scriptPath);

        if (args != null && args.length > 0) {
            command.addAll(Arrays.asList(args));
        }

        log.debug("构建的Python执行命令: {}", command);
        return command;
    }
    
    /**
     * 读取进程输出
     * 
     * @param inputStream 输入流
     * @return 输出内容
     * @throws IOException IO异常
     */
    private static String readProcessOutput(InputStream inputStream) throws IOException {
        StringBuilder output = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line).append(System.lineSeparator());
            }
        }
        return output.toString().trim();
    }
    
    /**
     * 关闭进程流
     * 
     * @param process 进程对象
     */
    private static void closeProcessStreams(Process process) {
        try {
            if (process.getInputStream() != null) {
                process.getInputStream().close();
            }
            if (process.getOutputStream() != null) {
                process.getOutputStream().close();
            }
            if (process.getErrorStream() != null) {
                process.getErrorStream().close();
            }
        } catch (IOException e) {
            log.warn("关闭进程流时发生异常", e);
        }
    }
    
    /**
     * 获取可用的Python命令
     *
     * @return 可用的Python命令，如果没有找到返回null
     */
    private static String getAvailablePythonCommand() {
        if (availablePythonCommand != null) {
            return availablePythonCommand;
        }

        synchronized (PythonExecutorUtil.class) {
            if (availablePythonCommand != null) {
                return availablePythonCommand;
            }

            // 首先尝试系统属性配置的命令
            if (testPythonCommand(PYTHON_COMMAND)) {
                availablePythonCommand = PYTHON_COMMAND;
                return availablePythonCommand;
            }

            // 尝试常见的Python命令
            for (String cmd : PYTHON_COMMANDS) {
                if (testPythonCommand(cmd)) {
                    availablePythonCommand = cmd;
                    log.info("找到可用的Python命令: {}", cmd);
                    return availablePythonCommand;
                }
            }

            log.error("未找到任何可用的Python解释器，请确保Python已安装并在PATH中");
            return null;
        }
    }

    /**
     * 测试Python命令是否可用
     *
     * @param command Python命令
     * @return 是否可用
     */
    private static boolean testPythonCommand(String command) {
        try {
            ProcessBuilder pb = new ProcessBuilder(command, "--version");
            Process process = pb.start();
            boolean finished = process.waitFor(10, TimeUnit.SECONDS);

            if (finished && process.exitValue() == 0) {
                String version = readProcessOutput(process.getInputStream());
                log.debug("Python命令 {} 可用，版本: {}", command, version.trim());
                return true;
            }
        } catch (Exception e) {
            log.debug("Python命令 {} 不可用: {}", command, e.getMessage());
        }
        return false;
    }

    /**
     * 检查Python环境是否可用
     *
     * @return 是否可用
     */
    public static boolean isPythonAvailable() {
        return getAvailablePythonCommand() != null;
    }

    /**
     * 获取Python版本信息
     *
     * @return Python版本信息，如果获取失败返回null
     */
    public static String getPythonVersion() {
        String pythonCmd = getAvailablePythonCommand();
        if (pythonCmd == null) {
            return null;
        }

        try {
            ProcessBuilder pb = new ProcessBuilder(pythonCmd, "--version");
            Process process = pb.start();
            boolean finished = process.waitFor(10, TimeUnit.SECONDS);

            if (finished && process.exitValue() == 0) {
                String version = readProcessOutput(process.getInputStream());
                return version.trim();
            }
        } catch (Exception e) {
            log.error("获取Python版本时发生异常", e);
        }
        return null;
    }
}
